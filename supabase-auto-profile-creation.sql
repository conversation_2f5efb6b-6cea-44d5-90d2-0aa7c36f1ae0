-- =====================================================
-- Automatic User Profile Creation Trigger for ZenVibe
-- =====================================================
-- This trigger automatically creates a user profile when a new user
-- signs up via Google OAuth or email/password authentication.
-- This prevents "Database error saving new user" issues.

-- =====================================================
-- 1. Create Function to Handle New User Profile Creation
-- =====================================================
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a new user profile with default values
  INSERT INTO public.user_profiles (
    id,
    email,
    full_name,
    avatar_url,
    username,
    subscription_tier,
    subscription_status,
    daily_message_count,
    daily_message_limit,
    total_messages_sent,
    total_projects_created,
    preferences,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    NEW.raw_user_meta_data->>'avatar_url',
    NEW.raw_user_meta_data->>'username',
    'free',
    'active',
    0,
    10,
    0,
    0,
    '{}',
    NOW(),
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. Create Trigger on auth.users Table
-- =====================================================
-- This trigger fires after a new user is inserted into auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_user_profile();

-- =====================================================
-- 3. Ensure RLS Policies Allow Profile Creation
-- =====================================================
-- Make sure the trigger can create profiles even during OAuth flow

-- Enable RLS on user_profiles if not already enabled
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow the trigger function to insert profiles
-- This policy allows the system to create profiles during signup
CREATE POLICY "System can create user profiles during signup"
  ON user_profiles
  FOR INSERT
  WITH CHECK (true);

-- Ensure users can read their own profiles
CREATE POLICY "Users can read their own profile" 
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Ensure users can update their own profiles
CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- =====================================================
-- 4. Grant Necessary Permissions
-- =====================================================
-- Grant permissions for the trigger function to work properly
GRANT USAGE ON SCHEMA public TO postgres;
GRANT ALL ON public.user_profiles TO postgres;

-- =====================================================
-- 5. Test the Trigger (Optional - for verification)
-- =====================================================
-- You can test this by creating a test user:
-- INSERT INTO auth.users (id, email, raw_user_meta_data, created_at, updated_at)
-- VALUES (gen_random_uuid(), '<EMAIL>', '{"name": "Test User"}', NOW(), NOW());
-- Then check if a profile was created in user_profiles table.

-- =====================================================
-- 6. Backfill Existing Users (If Needed)
-- =====================================================
-- If you have existing users without profiles, run this:
-- INSERT INTO user_profiles (id, email, full_name, subscription_tier, subscription_status, daily_message_count, daily_message_limit, total_messages_sent, total_projects_created, preferences, created_at, updated_at)
-- SELECT 
--   u.id,
--   u.email,
--   COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name'),
--   'free',
--   'active',
--   0,
--   10,
--   0,
--   0,
--   '{}',
--   u.created_at,
--   NOW()
-- FROM auth.users u
-- LEFT JOIN user_profiles p ON u.id = p.id
-- WHERE p.id IS NULL;

-- =====================================================
-- Instructions:
-- =====================================================
-- 1. Run this SQL in your Supabase SQL Editor
-- 2. This will automatically create user profiles for all new signups
-- 3. The "Database error saving new user" issue should be resolved
-- 4. Google OAuth signups will now work seamlessly
-- =====================================================
