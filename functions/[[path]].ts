import type { ServerBuild } from '@remix-run/cloudflare';
import { createPagesFunctionHandler } from '@remix-run/cloudflare-pages';

export const onRequest = async (context: any) => {
  const serverBuild = (await import('../build/server')) as unknown as ServerBuild;

  const handler = createPagesFunctionHandler({
    build: serverBuild,
    getLoadContext: () => ({
      cloudflare: {
        env: context.env,
      },
    }),
  });

  return handler(context);
};
