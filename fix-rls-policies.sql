-- =====================================================
-- FIX: Google OAuth "Database error saving new user"
-- =====================================================
-- Root Cause: RLS policies are blocking profile creation during OAuth signup
-- Solution: Add proper policies and trigger for automatic profile creation

-- =====================================================
-- 1. Fix RLS Policies to Allow Profile Creation
-- =====================================================

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can read their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;

-- Create new policies that work with OAuth
-- Allow authenticated users to read their own profile
CREATE POLICY "Users can read their own profile" 
  ON public.user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Allow authenticated users to update their own profile
CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Allow authenticated users to insert their own profile
CREATE POLICY "Users can insert their own profile"
  ON public.user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- CRITICAL: Allow system/service role to insert profiles during OAuth
-- This policy allows the trigger function to create profiles
CREATE POLICY "System can create user profiles"
  ON public.user_profiles
  FOR INSERT
  WITH CHECK (true);

-- =====================================================
-- 2. Create Automatic Profile Creation Trigger
-- =====================================================

-- Create function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert new user profile with data from OAuth
  INSERT INTO public.user_profiles (
    id,
    email,
    full_name,
    avatar_url,
    username,
    subscription_tier,
    subscription_status,
    daily_message_count,
    daily_message_limit,
    total_messages_sent,
    total_projects_created,
    preferences,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    NEW.raw_user_meta_data->>'avatar_url',
    NEW.raw_user_meta_data->>'username',
    'free',
    'active',
    0,
    10,
    0,
    0,
    '{}',
    NOW(),
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on auth.users table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 3. Grant Necessary Permissions
-- =====================================================

-- Ensure the function has proper permissions
GRANT USAGE ON SCHEMA public TO postgres;
GRANT ALL ON public.user_profiles TO postgres;

-- =====================================================
-- 4. Test the Fix (Optional)
-- =====================================================

-- You can test this by creating a test user in the auth.users table
-- The trigger should automatically create a profile

-- =====================================================
-- 5. Cleanup Existing Issues (If Needed)
-- =====================================================

-- If you have users without profiles, run this to backfill:
-- INSERT INTO public.user_profiles (
--   id, email, full_name, subscription_tier, subscription_status,
--   daily_message_count, daily_message_limit, total_messages_sent,
--   total_projects_created, preferences, created_at, updated_at
-- )
-- SELECT 
--   u.id,
--   u.email,
--   COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name'),
--   'free',
--   'active',
--   0,
--   10,
--   0,
--   0,
--   '{}',
--   u.created_at,
--   NOW()
-- FROM auth.users u
-- LEFT JOIN public.user_profiles p ON u.id = p.id
-- WHERE p.id IS NULL;

-- =====================================================
-- INSTRUCTIONS:
-- =====================================================
-- 1. Copy this entire SQL script
-- 2. Go to Supabase SQL Editor: https://app.supabase.com/project/bgyzehghvajcgqdkpkve/sql
-- 3. Paste and run this script
-- 4. Test Google OAuth signup - it should work now!
-- =====================================================
