#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create the OAuth trigger using the Supabase API
 * This requires a Supabase access token to execute SQL
 */

// S<PERSON> to create the automatic user profile creation trigger
const CREATE_TRIGGER_SQL = `
-- Create Function to Handle New User Profile Creation
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id,
    email,
    full_name,
    avatar_url,
    username,
    subscription_tier,
    subscription_status,
    daily_message_count,
    daily_message_limit,
    total_messages_sent,
    total_projects_created,
    preferences,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    NEW.raw_user_meta_data->>'avatar_url',
    NEW.raw_user_meta_data->>'username',
    'free',
    'active',
    0,
    10,
    0,
    0,
    '{}',
    NOW(),
    NOW()
  );
  
  R<PERSON>URN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create Trigger on auth.users Table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_user_profile();

-- Ensure RLS Policies Allow Profile Creation
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow the trigger function to insert profiles
DROP POLICY IF EXISTS "System can create user profiles during signup" ON user_profiles;
CREATE POLICY "System can create user profiles during signup"
  ON user_profiles
  FOR INSERT
  WITH CHECK (true);

-- Ensure users can read their own profiles
DROP POLICY IF EXISTS "Users can read their own profile" ON user_profiles;
CREATE POLICY "Users can read their own profile" 
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Ensure users can update their own profiles
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);
`;

async function createTriggerViaAPI() {
  console.log('🔧 Creating OAuth trigger via Supabase API...');
  
  // You need to get your Supabase access token from:
  // https://app.supabase.com/account/tokens
  const SUPABASE_ACCESS_TOKEN = process.env.SUPABASE_ACCESS_TOKEN;
  const PROJECT_ID = 'bgyzehghvajcgqdkpkve'; // From the URL
  
  if (!SUPABASE_ACCESS_TOKEN) {
    console.log('❌ SUPABASE_ACCESS_TOKEN environment variable is required');
    console.log('');
    console.log('📋 To get your access token:');
    console.log('1. Go to https://app.supabase.com/account/tokens');
    console.log('2. Create a new access token');
    console.log('3. Run: export SUPABASE_ACCESS_TOKEN="your_token_here"');
    console.log('4. Then run this script again');
    console.log('');
    console.log('🔄 Alternative: Manual SQL execution');
    console.log('1. Go to https://app.supabase.com/project/bgyzehghvajcgqdkpkve/sql');
    console.log('2. Copy the SQL from supabase-auto-profile-creation.sql');
    console.log('3. Paste and run it in the SQL editor');
    return;
  }
  
  try {
    console.log('📡 Executing SQL via Supabase API...');
    
    const response = await fetch(`https://api.supabase.com/v1/projects/${PROJECT_ID}/database/query`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: CREATE_TRIGGER_SQL
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, response.statusText);
      console.error('❌ Error details:', errorText);
      return;
    }
    
    const result = await response.json();
    console.log('✅ Trigger created successfully!');
    console.log('📊 Result:', result);
    
    console.log('\n🎉 Google OAuth fix completed!');
    console.log('✅ Users can now sign up with Google without database errors');
    console.log('🧪 Test by trying to sign up with Google OAuth');
    
  } catch (error) {
    console.error('❌ Failed to create trigger:', error);
    console.log('\n📋 Manual Steps Required:');
    console.log('1. Go to https://app.supabase.com/project/bgyzehghvajcgqdkpkve/sql');
    console.log('2. Copy the SQL from supabase-auto-profile-creation.sql');
    console.log('3. Paste and run it in the SQL editor');
  }
}

// Run the script
createTriggerViaAPI().catch(console.error);
