/**
 * Performance Test for AI Configuration Modal
 * 
 * This script helps test the modal's performance improvements
 * Run this in browser console to test modal performance
 */

console.log('🚀 AI Configuration Modal Performance Test');
console.log('==========================================');

// Performance monitoring
const performanceMetrics = {
  modalOpenTime: 0,
  modalCloseTime: 0,
  animationFrames: 0,
  memoryUsage: 0,
  renderCount: 0
};

// Helper to measure performance
const measurePerformance = (label, fn) => {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  console.log(`⏱️ ${label}: ${(end - start).toFixed(2)}ms`);
  return result;
};

// Monitor animation frames
const monitorAnimations = () => {
  let frameCount = 0;
  const startTime = performance.now();
  
  const countFrames = () => {
    frameCount++;
    if (performance.now() - startTime < 1000) {
      requestAnimationFrame(countFrames);
    } else {
      performanceMetrics.animationFrames = frameCount;
      console.log(`🎬 Animation FPS: ${frameCount} frames/second`);
    }
  };
  
  requestAnimationFrame(countFrames);
};

// Memory usage monitoring
const checkMemoryUsage = () => {
  if (performance.memory) {
    const memory = performance.memory;
    performanceMetrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
    console.log(`💾 Memory Usage: ${performanceMetrics.memoryUsage.toFixed(2)} MB`);
    console.log(`📊 Memory Details:`, {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
    });
  } else {
    console.log('💾 Memory monitoring not available in this browser');
  }
};

// Test modal opening performance
const testModalOpen = () => {
  console.log('\n🔓 Testing Modal Open Performance...');
  
  const modalTrigger = document.querySelector('[data-modal-trigger]') || 
                      document.querySelector('button[class*="api"]') ||
                      document.querySelector('button[class*="config"]');
  
  if (modalTrigger) {
    const startTime = performance.now();
    
    // Monitor for modal appearance
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1 && node.querySelector && 
                (node.querySelector('[class*="modal"]') || 
                 node.querySelector('[class*="dialog"]') ||
                 node.textContent?.includes('AI Configuration'))) {
              const endTime = performance.now();
              performanceMetrics.modalOpenTime = endTime - startTime;
              console.log(`✅ Modal opened in: ${performanceMetrics.modalOpenTime.toFixed(2)}ms`);
              observer.disconnect();
            }
          });
        }
      });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
    
    // Trigger modal
    modalTrigger.click();
    
    // Cleanup after 5 seconds
    setTimeout(() => observer.disconnect(), 5000);
  } else {
    console.log('❌ Modal trigger not found');
  }
};

// Test modal animations
const testModalAnimations = () => {
  console.log('\n🎭 Testing Modal Animations...');
  
  const modal = document.querySelector('[class*="modal"]') ||
               document.querySelector('[class*="dialog"]') ||
               document.querySelector('[role="dialog"]');
  
  if (modal) {
    console.log('✅ Modal found, monitoring animations...');
    monitorAnimations();
    
    // Test provider selection animations
    const providerButtons = modal.querySelectorAll('button[class*="provider"], button[class*="group"]');
    if (providerButtons.length > 0) {
      console.log(`🔘 Found ${providerButtons.length} provider buttons`);
      
      // Test hover animations
      providerButtons.forEach((button, index) => {
        setTimeout(() => {
          button.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));
          setTimeout(() => {
            button.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }));
          }, 200);
        }, index * 100);
      });
    }
  } else {
    console.log('❌ Modal not found for animation testing');
  }
};

// Test modal responsiveness
const testModalResponsiveness = () => {
  console.log('\n📱 Testing Modal Responsiveness...');
  
  const modal = document.querySelector('[class*="modal"]') ||
               document.querySelector('[class*="dialog"]');
  
  if (modal) {
    const rect = modal.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    
    console.log('📐 Modal Dimensions:', {
      width: `${rect.width}px`,
      height: `${rect.height}px`,
      top: `${rect.top}px`,
      left: `${rect.left}px`
    });
    
    console.log('📱 Viewport:', viewport);
    
    // Check if modal fits in viewport
    const fitsInViewport = rect.width <= viewport.width && rect.height <= viewport.height;
    console.log(`✅ Fits in viewport: ${fitsInViewport ? 'YES' : 'NO'}`);
    
    // Check mobile responsiveness
    const isMobileSize = viewport.width <= 768;
    console.log(`📱 Mobile size: ${isMobileSize ? 'YES' : 'NO'}`);
    
    if (isMobileSize) {
      const mobileOptimized = rect.width <= viewport.width * 0.95;
      console.log(`📱 Mobile optimized: ${mobileOptimized ? 'YES' : 'NO'}`);
    }
  }
};

// Test form interactions
const testFormInteractions = () => {
  console.log('\n📝 Testing Form Interactions...');
  
  const modal = document.querySelector('[class*="modal"]') ||
               document.querySelector('[class*="dialog"]');
  
  if (modal) {
    // Test API key input
    const apiKeyInput = modal.querySelector('input[type="password"]');
    if (apiKeyInput) {
      console.log('🔑 Testing API key input...');
      
      const testKey = 'test-api-key-12345';
      const startTime = performance.now();
      
      apiKeyInput.focus();
      apiKeyInput.value = testKey;
      apiKeyInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      const endTime = performance.now();
      console.log(`⌨️ Input response time: ${(endTime - startTime).toFixed(2)}ms`);
    }
    
    // Test provider selection
    const providerButtons = modal.querySelectorAll('button[class*="provider"], button[class*="group"]');
    if (providerButtons.length > 0) {
      console.log('🔘 Testing provider selection...');
      
      providerButtons.forEach((button, index) => {
        setTimeout(() => {
          const startTime = performance.now();
          button.click();
          const endTime = performance.now();
          console.log(`🔘 Provider ${index + 1} selection: ${(endTime - startTime).toFixed(2)}ms`);
        }, index * 500);
      });
    }
  }
};

// Run comprehensive performance test
const runPerformanceTest = () => {
  console.log('🧪 Starting Comprehensive Performance Test...\n');
  
  checkMemoryUsage();
  
  // Test modal opening
  testModalOpen();
  
  // Wait for modal to open, then run other tests
  setTimeout(() => {
    testModalAnimations();
    testModalResponsiveness();
    testFormInteractions();
    
    // Final memory check
    setTimeout(() => {
      console.log('\n📊 Final Performance Report:');
      console.log('============================');
      checkMemoryUsage();
      console.log('Performance Metrics:', performanceMetrics);
      
      // Performance score
      let score = 100;
      if (performanceMetrics.modalOpenTime > 500) score -= 20;
      if (performanceMetrics.animationFrames < 30) score -= 15;
      if (performanceMetrics.memoryUsage > 50) score -= 10;
      
      console.log(`🏆 Performance Score: ${score}/100`);
      
      if (score >= 90) {
        console.log('🎉 Excellent performance!');
      } else if (score >= 70) {
        console.log('✅ Good performance');
      } else if (score >= 50) {
        console.log('⚠️ Average performance - consider optimizations');
      } else {
        console.log('❌ Poor performance - needs optimization');
      }
    }, 3000);
  }, 1000);
};

// Export test functions
window.modalPerformanceTest = {
  run: runPerformanceTest,
  testOpen: testModalOpen,
  testAnimations: testModalAnimations,
  testResponsiveness: testModalResponsiveness,
  testInteractions: testFormInteractions,
  checkMemory: checkMemoryUsage,
  metrics: performanceMetrics
};

// Auto-run instructions
console.log('🔧 Modal performance testing tools loaded!');
console.log('Run: modalPerformanceTest.run() for comprehensive test');
console.log('Run: modalPerformanceTest.testOpen() to test modal opening');
console.log('Run: modalPerformanceTest.checkMemory() to check memory usage');

// Auto-run if modal is already open
if (document.querySelector('[class*="modal"]') || document.querySelector('[role="dialog"]')) {
  console.log('\n🎯 Modal detected! Running quick performance check...');
  setTimeout(() => {
    testModalAnimations();
    testModalResponsiveness();
    checkMemoryUsage();
  }, 500);
}
