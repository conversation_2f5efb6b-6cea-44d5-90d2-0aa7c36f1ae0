-- =====================================================
-- 🧹 SUPABASE TABLE CLEANUP - REMOVE UNUSED TABLES
-- =====================================================
-- This script removes tables that are no longer used after
-- reverting to IndexedDB-only architecture for chats/projects

-- =====================================================
-- BACKUP DATA (Optional - Run if you want to keep data)
-- =====================================================
-- Uncomment these lines if you want to backup data before deletion:

-- CREATE TABLE chat_conversations_backup AS SELECT * FROM chat_conversations;
-- CREATE TABLE chat_messages_backup AS SELECT * FROM chat_messages;
-- CREATE TABLE projects_backup AS SELECT * FROM projects;
-- CREATE TABLE usage_tracking_backup AS SELECT * FROM usage_tracking;

-- =====================================================
-- REMOVE UNUSED TABLES
-- =====================================================

-- 1. Remove chat_messages table (not used - IndexedDB only)
DROP TABLE IF EXISTS chat_messages CASCADE;
COMMENT ON SCHEMA public IS 'Removed chat_messages - using IndexedDB only';

-- 2. Remove chat_conversations table (not used - IndexedDB only)
DROP TABLE IF EXISTS chat_conversations CASCADE;
COMMENT ON SCHEMA public IS 'Removed chat_conversations - using IndexedDB only';

-- 3. Remove projects table (not used - IndexedDB only)
DROP TABLE IF EXISTS projects CASCADE;
COMMENT ON SCHEMA public IS 'Removed projects - using IndexedDB only';

-- 4. Remove usage_tracking table (optional - can be removed if analytics not needed)
-- Uncomment the next line if you don't need usage analytics:
-- DROP TABLE IF EXISTS usage_tracking CASCADE;

-- =====================================================
-- KEEP ESSENTIAL TABLES
-- =====================================================
-- These tables are KEPT and are essential:
-- ✅ user_profiles - Required for authentication and message limits
-- ✅ user_api_keys - Required for secure API key storage
-- ✅ usage_tracking - Optional, kept for message limit analytics

-- =====================================================
-- VERIFY REMAINING TABLES
-- =====================================================
-- Check what tables remain after cleanup:
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- =====================================================
-- STORAGE SPACE SAVED
-- =====================================================
-- After cleanup, you should see significant storage reduction:
-- - chat_conversations: Removed (was storing chat metadata)
-- - chat_messages: Removed (was storing message content)  
-- - projects: Removed (was storing project files)
-- 
-- This should free up significant space in your 500MB Supabase limit!

-- =====================================================
-- FUNCTIONS CLEANUP (Optional)
-- =====================================================
-- If you want to remove the message limit functions we created
-- (only do this if you're not implementing the new message limit system):

-- DROP FUNCTION IF EXISTS get_user_message_status(UUID, DATE);
-- DROP FUNCTION IF EXISTS consume_user_message(UUID, DATE);
-- DROP FUNCTION IF EXISTS reset_daily_message_count(UUID);

-- =====================================================
-- FINAL VERIFICATION
-- =====================================================
-- Run this to see your final table structure:
SELECT 
  table_name,
  table_type,
  (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
ORDER BY table_name;

-- =====================================================
-- EXPECTED RESULT AFTER CLEANUP
-- =====================================================
-- You should have only these tables remaining:
-- 1. user_profiles (essential for auth & message limits)
-- 2. user_api_keys (essential for API key security)  
-- 3. usage_tracking (optional for analytics)
--
-- Total tables: 2-3 (down from 6)
-- Storage saved: Significant reduction in database usage
-- Architecture: Clean, focused on essential functionality

-- =====================================================
-- INSTRUCTIONS
-- =====================================================
-- 1. Review the tables being removed
-- 2. Backup data if needed (uncomment backup section)
-- 3. Run this script in Supabase SQL editor
-- 4. Verify the results with the final queries
-- 5. Your database will be much cleaner and use less storage!
-- =====================================================
