# Supabase Storage Implementation

## Overview

This document explains the implementation of smart Supabase storage for GenVibe platform, designed to store only important chat data while preventing database bloat.

## Database Schema

### Table Structure

#### 1. `chat_conversations` (Parent Table)
- **Purpose**: Stores conversation metadata and summary information
- **Key Fields**:
  - `id`: Unique conversation identifier
  - `user_id`: Links to authenticated user
  - `title`: Conversation title (auto-generated from first artifact)
  - `description`: Optional description
  - `project_type`: Type of project (web, react, python, etc.)
  - `message_count`: Number of important messages stored
  - `last_message_at`: Timestamp of last activity
  - `metadata`: JSON field for additional data

#### 2. `chat_messages` (Child Table)
- **Purpose**: Stores individual messages within conversations
- **Key Fields**:
  - `conversation_id`: Links to parent conversation
  - `user_id`: Message owner
  - `role`: user/assistant/system
  - `content`: Message content (optimized/summarized if large)
  - `content_type`: text/summary/important
  - `tokens_used`: Token consumption tracking
  - `model_used`: AI model that generated the response
  - `metadata`: Additional message data

#### 3. `projects` (Project Storage)
- **Purpose**: Stores generated project files and metadata
- **Key Fields**:
  - `conversation_id`: Links to originating chat
  - `name`: Project name
  - `project_type`: Detected project type
  - `framework`: Detected framework (React, Vue, etc.)
  - `file_structure`: Optimized file content (JSON)
  - `dependencies`: Package dependencies
  - `environment_variables`: Environment configuration

#### 4. `usage_tracking` (Analytics)
- **Purpose**: Tracks daily usage statistics
- **Key Fields**:
  - `user_id`: User identifier
  - `date`: Daily tracking date
  - `messages_sent`: Number of messages
  - `tokens_used`: Total tokens consumed
  - `projects_created`: Projects generated
  - `subscription_tier`: User's subscription level

## Smart Storage Strategy

### Message Filtering

**Important Messages (Always Stored):**
- All user messages
- Assistant messages containing:
  - Code blocks (````)
  - Artifacts (boltArtifact)
  - Project-related content
  - Error messages
  - Installation commands
  - Messages > 1KB in size

**Excluded Messages:**
- Simple acknowledgments
- Short responses without code
- System messages
- Messages marked with 'no-store' annotation

### File Storage Optimization

**Included Files:**
- Source code files (.js, .ts, .jsx, .tsx, .py, etc.)
- Configuration files (.json, .yaml, .env)
- Documentation (.md, .txt)
- Stylesheets (.css, .scss)
- Files under 100KB

**Excluded Files:**
- Binary files (.png, .jpg, .mp4, etc.)
- Font files (.woff, .ttf)
- Large files (>100KB)
- node_modules directory
- Build artifacts

### Storage Limits

```typescript
const STORAGE_LIMITS = {
  MAX_MESSAGE_SIZE: 50000,           // 50KB per message
  MAX_CONVERSATION_MESSAGES: 100,    // Keep last 100 important messages
  MAX_TOTAL_CONVERSATIONS: 50,       // 50 conversations per user
  MAX_FILE_SIZE: 100000,            // 100KB max file size
  MAX_TOTAL_PROJECT_SIZE: 5000000,  // 5MB per project
  MAX_FILES_PER_PROJECT: 100        // 100 files per project
};
```

## Implementation Details

### Services Created

#### 1. `ChatStorageService` (`app/lib/services/chatStorageService.ts`)
- **Purpose**: Handles chat conversation and message storage
- **Key Methods**:
  - `saveConversation()`: Creates/updates conversation metadata
  - `saveMessages()`: Stores important messages with optimization
  - `trackUsage()`: Records usage statistics
  - `cleanupOldConversations()`: Removes old data to stay within limits

#### 2. `ProjectStorageService` (`app/lib/services/projectStorageService.ts`)
- **Purpose**: Manages project file storage and metadata
- **Key Methods**:
  - `saveProject()`: Stores optimized project files
  - `updateProject()`: Updates existing project
  - `getUserProjects()`: Retrieves user's projects
  - `extractProjectMetadata()`: Auto-detects framework and dependencies

#### 3. `StorageTestService` (`app/lib/services/storageTestService.ts`)
- **Purpose**: Testing and verification of storage functionality
- **Usage**: Run `testStorage()` in browser console to verify setup

### Integration Points

#### Modified Files:
1. **`app/lib/persistence/useChatHistory.ts`**
   - Added Supabase storage calls to `storeMessageHistory()`
   - Maintains IndexedDB as local backup
   - Integrates chat, project, and usage tracking

2. **Database Schema** (`supabase-schema.sql`)
   - All tables created with proper RLS policies
   - Optimized indexes for performance

## Data Flow

### Chat Message Flow:
1. User sends message → Chat system processes
2. `storeMessageHistory()` called with all messages
3. **IndexedDB**: Stores complete chat locally (backup)
4. **Supabase**: Stores only important messages (cloud)
5. **Usage Tracking**: Records statistics
6. **Project Storage**: Saves artifacts if present
7. **Cleanup**: Periodically removes old data

### Project Creation Flow:
1. AI generates project files
2. `projectStorageService.saveProject()` called
3. Files filtered and optimized
4. Metadata extracted (framework, dependencies)
5. Stored in `projects` table
6. Usage statistics updated

## Benefits

### 1. **Database Efficiency**
- Only important content stored
- Automatic cleanup prevents bloat
- Smart file filtering reduces storage needs

### 2. **User Experience**
- Fast chat loading (metadata only)
- Complete local backup in IndexedDB
- Cross-device chat synchronization

### 3. **Analytics & Insights**
- Daily usage tracking
- Token consumption monitoring
- Project creation statistics

### 4. **Scalability**
- Per-user storage limits
- Automatic old data cleanup
- Optimized queries with proper indexing

## Testing

### Manual Testing:
```javascript
// Run in browser console after authentication
testStorage()
```

### Verification Steps:
1. Send a chat message with code
2. Check Supabase tables for data
3. Verify only important messages stored
4. Confirm usage tracking updated

## Future Enhancements

1. **Message Search**: Full-text search across stored messages
2. **Project Templates**: Save successful projects as templates
3. **Usage Analytics**: Dashboard for usage insights
4. **Data Export**: Export user data for backup
5. **Collaborative Features**: Share projects between users

## Troubleshooting

### Common Issues:
1. **No data in Supabase**: Check user authentication
2. **Storage errors**: Verify RLS policies
3. **Large database**: Run cleanup manually
4. **Missing messages**: Check importance filtering logic

### Debug Commands:
```javascript
// Check auth state
authState.get()

// Test storage
testStorage()

// Check database directly
supabase.from('chat_conversations').select('*')
```
