name: Update Stable Branch

on:
  push:
    branches:
      - main

permissions:
  contents: write

jobs:
  prepare-release:
    if: contains(github.event.head_commit.message, '#release')
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Get Current Version
        id: current_version
        run: |
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

      - name: Install semver
        run: pnpm add -g semver

      - name: Determine Version Bump
        id: version_bump
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          if [[ $COMMIT_MSG =~ "#release:major" ]]; then
            echo "bump=major" >> $GITHUB_OUTPUT
          elif [[ $COMMIT_MSG =~ "#release:minor" ]]; then
            echo "bump=minor" >> $GITHUB_OUTPUT
          else
            echo "bump=patch" >> $GITHUB_OUTPUT
          fi

      - name: Bump Version
        id: bump_version
        run: |
          NEW_VERSION=$(semver -i ${{ steps.version_bump.outputs.bump }} ${{ steps.current_version.outputs.version }})
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT

      - name: Update Package.json
        run: |
          NEW_VERSION=${{ steps.bump_version.outputs.new_version }}
          pnpm version $NEW_VERSION --no-git-tag-version --allow-same-version

      - name: Prepare changelog script
        run: chmod +x .github/scripts/generate-changelog.sh

      - name: Generate Changelog
        id: changelog
        env:
          NEW_VERSION: ${{ steps.bump_version.outputs.new_version }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

        run: .github/scripts/generate-changelog.sh

      - name: Get the latest commit hash and version tag
        run: |
          echo "COMMIT_HASH=$(git rev-parse HEAD)" >> $GITHUB_ENV
          echo "NEW_VERSION=${{ steps.bump_version.outputs.new_version }}" >> $GITHUB_ENV

      - name: Commit and Tag Release
        run: |
          git pull
          git add package.json pnpm-lock.yaml changelog.md
          git commit -m "chore: release version ${{ steps.bump_version.outputs.new_version }}"
          git tag "v${{ steps.bump_version.outputs.new_version }}"
          git push
          git push --tags

      - name: Update Stable Branch
        run: |
          if ! git checkout stable 2>/dev/null; then
            echo "Creating new stable branch..."
            git checkout -b stable
          fi
          git merge main --no-ff -m "chore: release version ${{ steps.bump_version.outputs.new_version }}"
          git push --set-upstream origin stable --force

      - name: Create GitHub Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          VERSION="v${{ steps.bump_version.outputs.new_version }}"
          # Save changelog to a file
          echo "${{ steps.changelog.outputs.content }}" > release_notes.md
          gh release create "$VERSION" \
            --title "Release $VERSION" \
            --notes-file release_notes.md \
            --target stable
