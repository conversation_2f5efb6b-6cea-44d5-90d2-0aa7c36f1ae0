name: CI/CD

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup and Build
        uses: ./.github/actions/setup-and-build

      - name: Run type check
        run: pnpm run typecheck

      # - name: Run ESLint
      #   run: pnpm run lint

      - name: Run tests
        run: pnpm run test
