#!/usr/bin/env node

/**
 * Debug script for Google OAuth "Database error saving new user" issue
 * Based on Supabase troubleshooting guide: https://supabase.com/docs/guides/troubleshooting/database-error-saving-new-user-RU_EwB
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://bgyzehghvajcgqdkpkve.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJneXplaGdodmFqY2dxZGtwa3ZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNzIyNTksImV4cCI6MjA2Mjg0ODI1OX0.2RN76yDDQK7CFLRrUEANZ_bbYKtmX6AQIEVcOI95AGw';

async function debugOAuthIssue() {
  console.log('🔍 Debugging Google OAuth "Database error saving new user" issue...');
  console.log('📖 Based on: https://supabase.com/docs/guides/troubleshooting/database-error-saving-new-user-RU_EwB');
  console.log('');

  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

  try {
    // 1. Check for triggers on auth.users table
    console.log('🔍 1. Checking for triggers on auth.users table...');
    await checkAuthUsersTriggers(supabase);

    // 2. Check for constraints on auth.users table
    console.log('🔍 2. Checking for constraints on auth.users table...');
    await checkAuthUsersConstraints(supabase);

    // 3. Check RLS policies on user_profiles table
    console.log('🔍 3. Checking RLS policies on user_profiles table...');
    await checkUserProfilesPolicies(supabase);

    // 4. Check if user_profiles table exists and structure
    console.log('🔍 4. Checking user_profiles table structure...');
    await checkUserProfilesTable(supabase);

    // 5. Test basic database connectivity
    console.log('🔍 5. Testing basic database connectivity...');
    await testDatabaseConnectivity(supabase);

    console.log('');
    console.log('🎯 NEXT STEPS:');
    console.log('1. Check Supabase Auth logs: https://app.supabase.com/project/bgyzehghvajcgqdkpkve/logs/auth-logs');
    console.log('2. Check Postgres logs: https://app.supabase.com/project/bgyzehghvajcgqdkpkve/logs/postgres-logs');
    console.log('3. Look for specific error messages during OAuth signup attempts');

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

async function checkAuthUsersTriggers(supabase) {
  try {
    const { data, error } = await supabase
      .from('information_schema.triggers')
      .select('*')
      .eq('event_object_table', 'users')
      .eq('event_object_schema', 'auth');

    if (error) {
      console.log('⚠️  Could not check triggers (this is normal with RLS)');
      console.log('   You need to check manually in Supabase SQL Editor');
    } else if (data && data.length > 0) {
      console.log('🚨 FOUND TRIGGERS on auth.users table:');
      data.forEach(trigger => {
        console.log(`   - ${trigger.trigger_name}: ${trigger.action_statement}`);
      });
      console.log('   ⚠️  These triggers might be causing the OAuth failure!');
    } else {
      console.log('✅ No triggers found on auth.users table');
    }
  } catch (error) {
    console.log('⚠️  Could not check triggers:', error.message);
  }
  console.log('');
}

async function checkAuthUsersConstraints(supabase) {
  try {
    const { data, error } = await supabase
      .from('information_schema.table_constraints')
      .select('*')
      .eq('table_name', 'users')
      .eq('table_schema', 'auth');

    if (error) {
      console.log('⚠️  Could not check constraints (this is normal with RLS)');
    } else if (data && data.length > 0) {
      console.log('📋 Constraints on auth.users table:');
      data.forEach(constraint => {
        console.log(`   - ${constraint.constraint_name}: ${constraint.constraint_type}`);
      });
    } else {
      console.log('✅ Standard constraints on auth.users table');
    }
  } catch (error) {
    console.log('⚠️  Could not check constraints:', error.message);
  }
  console.log('');
}

async function checkUserProfilesPolicies(supabase) {
  try {
    const { data, error } = await supabase
      .from('pg_policies')
      .select('*')
      .eq('tablename', 'user_profiles');

    if (error) {
      console.log('⚠️  Could not check RLS policies (this is normal with RLS)');
    } else if (data && data.length > 0) {
      console.log('🔒 RLS Policies on user_profiles table:');
      data.forEach(policy => {
        console.log(`   - ${policy.policyname}: ${policy.cmd} (${policy.permissive})`);
        if (policy.qual) console.log(`     Condition: ${policy.qual}`);
      });
    } else {
      console.log('⚠️  No RLS policies found - this might be the issue!');
    }
  } catch (error) {
    console.log('⚠️  Could not check RLS policies:', error.message);
  }
  console.log('');
}

async function checkUserProfilesTable(supabase) {
  try {
    // Check if table exists
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('*')
      .eq('table_name', 'user_profiles')
      .eq('table_schema', 'public');

    if (tableError) {
      console.log('⚠️  Could not check table existence');
    } else if (!tableInfo || tableInfo.length === 0) {
      console.log('🚨 user_profiles table does NOT exist!');
      console.log('   This is likely the cause of the OAuth error');
    } else {
      console.log('✅ user_profiles table exists');
      
      // Check table structure
      const { data: columns, error: columnsError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'user_profiles')
        .eq('table_schema', 'public');

      if (!columnsError && columns) {
        console.log('📋 Table structure:');
        columns.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
        });
      }
    }
  } catch (error) {
    console.log('⚠️  Could not check table structure:', error.message);
  }
  console.log('');
}

async function testDatabaseConnectivity(supabase) {
  try {
    // Test basic query
    const { data, error } = await supabase
      .from('user_profiles')
      .select('count(*)')
      .limit(1);

    if (error) {
      console.log('🚨 Database connectivity issue:');
      console.log(`   Error: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      console.log(`   Details: ${error.details}`);
    } else {
      console.log('✅ Database connectivity is working');
    }
  } catch (error) {
    console.log('🚨 Database connectivity failed:', error.message);
  }
  console.log('');
}

// Manual SQL queries to run in Supabase SQL Editor
console.log('');
console.log('📋 MANUAL SQL QUERIES TO RUN IN SUPABASE SQL EDITOR:');
console.log('');
console.log('-- 1. Check for triggers on auth.users:');
console.log('SELECT * FROM information_schema.triggers WHERE event_object_table = \'users\' AND event_object_schema = \'auth\';');
console.log('');
console.log('-- 2. Check for functions that might be called by triggers:');
console.log('SELECT * FROM information_schema.routines WHERE routine_schema = \'public\' AND routine_name LIKE \'%user%\';');
console.log('');
console.log('-- 3. Check user_profiles table exists:');
console.log('SELECT * FROM information_schema.tables WHERE table_name = \'user_profiles\';');
console.log('');
console.log('-- 4. Check RLS policies:');
console.log('SELECT * FROM pg_policies WHERE tablename = \'user_profiles\';');
console.log('');
console.log('-- 5. Check recent auth logs (run in Supabase dashboard):');
console.log('-- Go to: https://app.supabase.com/project/bgyzehghvajcgqdkpkve/logs/auth-logs');
console.log('');

// Run the debug
debugOAuthIssue().catch(console.error);
