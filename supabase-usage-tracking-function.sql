-- =====================================================
-- Atomic Usage Tracking Function for Supabase
-- =====================================================
-- This function provides atomic increment operations for usage tracking
-- to prevent race conditions when multiple requests update the same record

CREATE OR REPLACE FUNCTION increment_usage_tracking(
  p_user_id UUID,
  p_date DATE,
  p_messages_sent INTEGER DEFAULT 0,
  p_tokens_used INTEGER DEFAULT 0,
  p_api_calls_made INTEGER DEFAULT 0,
  p_subscription_tier TEXT DEFAULT 'free'
)
RETURNS TABLE(
  id UUID,
  user_id UUID,
  date DATE,
  messages_sent INTEGER,
  tokens_used INTEGER,
  api_calls_made INTEGER,
  projects_created INTEGER,
  subscription_tier TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Try to update existing record first
  UPDATE usage_tracking 
  SET 
    messages_sent = usage_tracking.messages_sent + p_messages_sent,
    tokens_used = usage_tracking.tokens_used + p_tokens_used,
    api_calls_made = usage_tracking.api_calls_made + p_api_calls_made,
    updated_at = NOW()
  WHERE 
    usage_tracking.user_id = p_user_id 
    AND usage_tracking.date = p_date;
  
  -- If no rows were updated, insert a new record
  IF NOT FOUND THEN
    INSERT INTO usage_tracking (
      user_id,
      date,
      messages_sent,
      tokens_used,
      api_calls_made,
      projects_created,
      subscription_tier,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_date,
      p_messages_sent,
      p_tokens_used,
      p_api_calls_made,
      0, -- projects_created starts at 0
      p_subscription_tier,
      NOW(),
      NOW()
    );
  END IF;
  
  -- Return the updated/inserted record
  RETURN QUERY
  SELECT 
    ut.id,
    ut.user_id,
    ut.date,
    ut.messages_sent,
    ut.tokens_used,
    ut.api_calls_made,
    ut.projects_created,
    ut.subscription_tier,
    ut.created_at,
    ut.updated_at
  FROM usage_tracking ut
  WHERE ut.user_id = p_user_id AND ut.date = p_date;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_usage_tracking TO authenticated;

-- =====================================================
-- Test the function (optional - remove in production)
-- =====================================================

-- Example usage:
-- SELECT * FROM increment_usage_tracking(
--   'user-uuid-here'::UUID,
--   '2024-01-15'::DATE,
--   1, -- messages_sent
--   100, -- tokens_used
--   1, -- api_calls_made
--   'free' -- subscription_tier
-- );

-- =====================================================
-- RLS Policy for the function
-- =====================================================

-- Ensure RLS is enabled on usage_tracking table
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to access their own usage data
CREATE POLICY "Users can access their own usage tracking" ON usage_tracking
  FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- Instructions for use:
-- =====================================================
-- 1. Run this SQL in your Supabase SQL editor
-- 2. The function will be available as supabase.rpc('increment_usage_tracking', {...})
-- 3. This provides atomic increments to prevent race conditions
-- 4. Falls back to manual upsert if RPC is not available
-- =====================================================
