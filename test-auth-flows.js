/**
 * GenVibe Authentication Flow Testing Script
 * 
 * This script helps test all authentication flows:
 * 1. Email signup with full name
 * 2. Email signin
 * 3. Forgot password
 * 4. Reset password
 * 5. Google OAuth (manual test)
 * 
 * Run this in the browser console on your GenVibe site
 */

console.log('🧪 GenVibe Authentication Flow Testing Script');
console.log('============================================');

// Test configuration
const TEST_CONFIG = {
  testEmail: '<EMAIL>', // Change this to a real email you control
  testFullName: 'Test User',
  testPassword: 'testpassword123',
  newPassword: 'newpassword123'
};

// Helper function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to check if element exists
const waitForElement = (selector, timeout = 5000) => {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
};

// Test functions
const tests = {
  async testSignupForm() {
    console.log('\n📝 Testing Signup Form...');
    
    try {
      // Navigate to signup
      window.location.href = '/signup';
      await wait(2000);
      
      // Check if full name field exists
      const fullNameField = document.querySelector('input[name="fullName"]');
      const emailField = document.querySelector('input[name="email"]');
      const passwordField = document.querySelector('input[name="password"]');
      const confirmPasswordField = document.querySelector('input[name="confirmPassword"]');
      
      console.log('✅ Full name field exists:', !!fullNameField);
      console.log('✅ Email field exists:', !!emailField);
      console.log('✅ Password field exists:', !!passwordField);
      console.log('✅ Confirm password field exists:', !!confirmPasswordField);
      
      if (fullNameField && emailField && passwordField && confirmPasswordField) {
        console.log('✅ All signup form fields are present');
        return true;
      } else {
        console.log('❌ Some signup form fields are missing');
        return false;
      }
    } catch (error) {
      console.error('❌ Signup form test failed:', error);
      return false;
    }
  },

  async testSigninForm() {
    console.log('\n🔐 Testing Signin Form...');
    
    try {
      // Navigate to signin
      window.location.href = '/signin';
      await wait(2000);
      
      // Check if forgot password link exists
      const forgotPasswordLink = document.querySelector('a[href="/forgot-password"]');
      const emailField = document.querySelector('input[name="email"]');
      const passwordField = document.querySelector('input[name="password"]');
      
      console.log('✅ Email field exists:', !!emailField);
      console.log('✅ Password field exists:', !!passwordField);
      console.log('✅ Forgot password link exists:', !!forgotPasswordLink);
      
      if (forgotPasswordLink && emailField && passwordField) {
        console.log('✅ Signin form is properly configured');
        return true;
      } else {
        console.log('❌ Signin form is missing elements');
        return false;
      }
    } catch (error) {
      console.error('❌ Signin form test failed:', error);
      return false;
    }
  },

  async testForgotPasswordForm() {
    console.log('\n🔄 Testing Forgot Password Form...');
    
    try {
      // Navigate to forgot password
      window.location.href = '/forgot-password';
      await wait(2000);
      
      // Check form elements
      const emailField = document.querySelector('input[name="email"]');
      const submitButton = document.querySelector('button[type="submit"]');
      const backLink = document.querySelector('a[href="/signin"]');
      
      console.log('✅ Email field exists:', !!emailField);
      console.log('✅ Submit button exists:', !!submitButton);
      console.log('✅ Back to signin link exists:', !!backLink);
      
      if (emailField && submitButton && backLink) {
        console.log('✅ Forgot password form is properly configured');
        return true;
      } else {
        console.log('❌ Forgot password form is missing elements');
        return false;
      }
    } catch (error) {
      console.error('❌ Forgot password form test failed:', error);
      return false;
    }
  },

  async testResetPasswordRoute() {
    console.log('\n🔑 Testing Reset Password Route...');
    
    try {
      // Test with a dummy code (should show error)
      window.location.href = '/reset-password?code=dummy-code';
      await wait(2000);
      
      // Check if the page loads (even with error)
      const pageTitle = document.title;
      const isResetPage = pageTitle.includes('Reset Password') || 
                         document.querySelector('h1')?.textContent?.includes('Reset') ||
                         window.location.pathname === '/reset-password';
      
      console.log('✅ Reset password route exists:', isResetPage);
      
      if (isResetPage) {
        console.log('✅ Reset password route is accessible');
        return true;
      } else {
        console.log('❌ Reset password route not found');
        return false;
      }
    } catch (error) {
      console.error('❌ Reset password route test failed:', error);
      return false;
    }
  },

  async testSupabaseConnection() {
    console.log('\n🔌 Testing Supabase Connection...');
    
    try {
      // Check if Supabase client is available
      if (typeof window !== 'undefined' && window.supabase) {
        console.log('✅ Supabase client is available');
        
        // Test connection by getting session
        const { data: { session }, error } = await window.supabase.auth.getSession();
        
        if (error) {
          console.log('⚠️ Supabase connection error:', error.message);
          return false;
        } else {
          console.log('✅ Supabase connection successful');
          console.log('Current session:', session ? 'Authenticated' : 'Not authenticated');
          return true;
        }
      } else {
        console.log('❌ Supabase client not found');
        return false;
      }
    } catch (error) {
      console.error('❌ Supabase connection test failed:', error);
      return false;
    }
  }
};

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Authentication Flow Tests...\n');
  
  const results = {
    signupForm: await tests.testSignupForm(),
    signinForm: await tests.testSigninForm(),
    forgotPasswordForm: await tests.testForgotPasswordForm(),
    resetPasswordRoute: await tests.testResetPasswordRoute(),
    supabaseConnection: await tests.testSupabaseConnection()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All authentication flows are working correctly!');
  } else {
    console.log('⚠️ Some authentication flows need attention.');
  }
  
  return results;
}

// Manual testing instructions
function showManualTestInstructions() {
  console.log('\n📋 Manual Testing Instructions:');
  console.log('===============================');
  console.log('1. Test Email Signup:');
  console.log('   - Go to /signup');
  console.log('   - Fill in full name, email, password, confirm password');
  console.log('   - Submit and check email for verification');
  console.log('');
  console.log('2. Test Email Signin:');
  console.log('   - Go to /signin');
  console.log('   - Enter verified email and password');
  console.log('   - Should redirect to homepage on success');
  console.log('');
  console.log('3. Test Forgot Password:');
  console.log('   - Go to /signin');
  console.log('   - Click "Forgot password?" link');
  console.log('   - Enter email and submit');
  console.log('   - Check email for reset link');
  console.log('');
  console.log('4. Test Reset Password:');
  console.log('   - Click reset link from email');
  console.log('   - Should go to /reset-password with code');
  console.log('   - Enter new password and confirm');
  console.log('   - Should show success message');
  console.log('');
  console.log('5. Test Google OAuth:');
  console.log('   - Go to /signup or /signin');
  console.log('   - Click "Continue with Google"');
  console.log('   - Complete Google OAuth flow');
  console.log('   - Should redirect to homepage');
}

// Export functions for manual use
window.testAuth = {
  runAllTests,
  showManualTestInstructions,
  individual: tests,
  config: TEST_CONFIG
};

// Auto-run tests
console.log('🔧 Authentication testing tools loaded!');
console.log('Run: testAuth.runAllTests() to test all flows');
console.log('Run: testAuth.showManualTestInstructions() for manual testing guide');
console.log('Run: testAuth.individual.testSignupForm() to test individual components');

// Auto-run if not in production
if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
  console.log('\n🏠 Development environment detected, running tests automatically...');
  runAllTests();
} else {
  console.log('\n🌐 Production environment detected, run tests manually with testAuth.runAllTests()');
}
