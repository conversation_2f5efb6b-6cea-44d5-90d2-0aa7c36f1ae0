site_name: zenvibe Docs
site_dir: ../site
theme:
  name: material
  palette:
    - scheme: default
      toggle:
        icon: material/toggle-switch-off-outline
        name: Switch to dark mode
    - scheme: slate
      toggle:
        icon: material/toggle-switch
        name: Switch to light mode
  features:
    - navigation.tabs
    - navigation.sections
    - toc.follow
    - toc.integrate
    - navigation.top
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
    # - navigation.instant
    # - navigation.tracking
    # - navigation.tabs.sticky
    # - navigation.expand
    # - content.code.annotate
  icon:
    repo: fontawesome/brands/github
  # logo: assets/logo.png
  # favicon: assets/logo.png
repo_name: zenvibe
repo_url: https://github.com/zenvibe/zenvibe
edit_uri: ''

extra:
  generator: false
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/zenvibe/zenvibe
      name: zenvibe
    - icon: fontawesome/brands/discourse
      link: https://thinktank.ottomator.ai/
      name: zenvibe Discourse
    - icon: fontawesome/brands/x-twitter
      link: https://x.com/zenvibe
      name: zenvibe on X
    - icon: fontawesome/brands/bluesky
      link: https://bsky.app/profile/zenvibe
      name: zenvibe on Bluesky

markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.arithmatex:
      generic: true
  - footnotes
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.mark
  - attr_list
  - md_in_html
  - tables
  - def_list
  - admonition
  - pymdownx.tasklist:
      custom_checkbox: true
  - toc:
      permalink: true
