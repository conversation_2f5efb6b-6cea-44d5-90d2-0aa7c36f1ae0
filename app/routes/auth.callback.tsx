import { redirect, type LoaderFunctionArgs } from '@remix-run/cloudflare';
import { createSupabaseClient } from '~/lib/supabase/client';

export async function loader({ request, context }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const error = url.searchParams.get('error');
  const errorDescription = url.searchParams.get('error_description');

  if (error) {
    console.error('Auth callback error:', error, errorDescription);
    return redirect(`/signin?error=${encodeURIComponent(errorDescription || error)}`);
  }

  if (code) {
    try {
      const supabase = createSupabaseClient(context?.cloudflare?.env);
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

      if (exchangeError) {
        console.error('Code exchange error:', exchangeError);
        return redirect(`/signin?error=${encodeURIComponent(exchangeError.message)}`);
      }

      if (data.session) {
        // Redirect with success parameter to trigger toast
        return redirect('/?login=success');
      }
    } catch (error) {
      console.error('Auth callback error:', error);
      return redirect('/signin?error=Authentication failed');
    }
  }

  return redirect('/signin');
}
