import { PlatformHealthCheck } from '~/components/PlatformHealthCheck';

export default function HealthCheckPage() {
  return (
    <div className="min-h-screen bg-gray-950 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white mb-4">🏥 GenVibe Platform Health Check</h1>
          <p className="text-gray-400 text-lg">
            Comprehensive diagnosis and repair system for storage, sync, and counting issues
          </p>
        </div>

        <PlatformHealthCheck />

        <div className="mt-12 p-6 bg-gray-900 border border-gray-700 rounded-lg">
          <h2 className="text-xl font-bold text-white mb-4">🔧 Manual Console Commands</h2>
          <p className="text-gray-400 mb-4">
            Open browser console (F12) and run these commands for advanced debugging:
          </p>

          <div className="bg-black p-4 rounded text-green-400 font-mono text-sm space-y-2">
            <div className="text-blue-400">// Full platform diagnosis</div>
            <div>comprehensiveDiagnosis()</div>

            <div className="text-blue-400 mt-4">// Quick fixes</div>
            <div>syncSupabaseToIndexedDB() // Fix empty sidebar</div>
            <div>fixAllCounting() // Fix message/project counts</div>
            <div>fixProjectLinking() // Link orphaned projects</div>

            <div className="text-blue-400 mt-4">// Legacy cleanup functions</div>
            <div>cleanupDuplicates() // Remove duplicate conversations</div>
            <div>completeCleanup() // Full cleanup and sync</div>
            <div>showConversationStats() // Show conversation statistics</div>
            <div>showUsageStats() // Show usage statistics</div>

            <div className="text-red-400 mt-4">// ⚠️ DANGER ZONE</div>
            <div className="text-red-400">nuclearReset() // DELETE EVERYTHING & LOGOUT</div>
          </div>
        </div>

        <div className="mt-8 p-6 bg-gray-900 border border-gray-700 rounded-lg">
          <h2 className="text-xl font-bold text-white mb-4">📋 Common Issues & Solutions</h2>

          <div className="space-y-4">
            <div className="border-l-4 border-red-500 pl-4">
              <h3 className="text-red-400 font-semibold">🚨 Sidebar shows "No previous conversations"</h3>
              <p className="text-gray-400 text-sm">
                <strong>Cause:</strong> IndexedDB is empty but Supabase has conversations
                <br />
                <strong>Solution:</strong> Run{' '}
                <code className="bg-gray-800 px-2 py-1 rounded">syncSupabaseToIndexedDB()</code>
              </p>
            </div>

            <div className="border-l-4 border-yellow-500 pl-4">
              <h3 className="text-yellow-400 font-semibold">⚠️ Message counts don't match</h3>
              <p className="text-gray-400 text-sm">
                <strong>Cause:</strong> Inconsistent counting across usage tracking, profile, and auth state
                <br />
                <strong>Solution:</strong> Run <code className="bg-gray-800 px-2 py-1 rounded">fixAllCounting()</code>
              </p>
            </div>

            <div className="border-l-4 border-blue-500 pl-4">
              <h3 className="text-blue-400 font-semibold">🔗 Projects not linked to conversations</h3>
              <p className="text-gray-400 text-sm">
                <strong>Cause:</strong> Projects created without proper conversation linking
                <br />
                <strong>Solution:</strong> Run{' '}
                <code className="bg-gray-800 px-2 py-1 rounded">fixProjectLinking()</code>
              </p>
            </div>

            <div className="border-l-4 border-purple-500 pl-4">
              <h3 className="text-purple-400 font-semibold">🔄 General sync issues</h3>
              <p className="text-gray-400 text-sm">
                <strong>Cause:</strong> Multiple storage systems out of sync
                <br />
                <strong>Solution:</strong> Run <code className="bg-gray-800 px-2 py-1 rounded">completeCleanup()</code>{' '}
                for full cleanup
              </p>
            </div>

            <div className="border-l-4 border-red-500 pl-4">
              <h3 className="text-red-400 font-semibold">💥 Complete platform reset needed</h3>
              <p className="text-gray-400 text-sm">
                <strong>Cause:</strong> Severe corruption or need for fresh start
                <br />
                <strong>Solution:</strong> Use Nuclear Reset button or run{' '}
                <code className="bg-gray-800 px-2 py-1 rounded text-red-400">nuclearReset()</code>
                <br />
                <strong>⚠️ WARNING:</strong> This deletes ALL data and logs you out!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
