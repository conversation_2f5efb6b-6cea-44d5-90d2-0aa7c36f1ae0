import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from '@remix-run/node';
import { ApiKeyService, SUPPORTED_PROVIDERS, validateApiKeyFormat } from '~/lib/services/apiKeyService';
import { requireAuth } from '~/lib/auth/auth.server';

// GET /api/api-keys - Get all API keys for the user
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const user = await requireAuth(request);

    const [apiKeys, status] = await Promise.all([
      ApiKeyService.getUserApiKeys(user.id),
      ApiKeyService.getApiKeyStatus(user.id)
    ]);

    return json({
      success: true,
      data: {
        apiKeys,
        supportedProviders: SUPPORTED_PROVIDERS,
        status
      }
    });
  } catch (error) {
    console.error('Error fetching API keys:', error);

    // Handle auth errors specifically
    if (error instanceof Response) {
      return error;
    }

    return json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch API keys'
      },
      { status: 500 }
    );
  }
}

// POST /api/api-keys - Save or update an API key
export async function action({ request }: ActionFunctionArgs) {
  try {
    const user = await requireAuth(request);
    const { providerName, apiKey, action: actionType } = await request.json();

    // Validate provider
    if (!SUPPORTED_PROVIDERS.find(p => p.name === providerName)) {
      return json(
        { success: false, error: 'Invalid provider. Supported providers: Google, OpenAI, Anthropic' },
        { status: 400 }
      );
    }

    if (actionType === 'delete') {
      await ApiKeyService.deleteApiKey(user.id, providerName);
      return json({
        success: true,
        message: `${providerName} API key deleted successfully`
      });
    }

    // Validate API key
    if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
      return json(
        { success: false, error: 'API key is required' },
        { status: 400 }
      );
    }

    // Use enhanced validation from service
    const validation = validateApiKeyFormat(providerName, apiKey);
    if (!validation.isValid) {
      return json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    // Save the API key (validation is done in the service)
    await ApiKeyService.saveApiKey(user.id, providerName, apiKey.trim());

    return json({
      success: true,
      message: `${providerName} API key saved successfully`
    });

  } catch (error) {
    console.error('Error saving API key:', error);

    // Handle auth errors specifically
    if (error instanceof Response) {
      return error;
    }

    return json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save API key'
      },
      { status: 500 }
    );
  }
}
