import { type MetaFunction } from '@remix-run/cloudflare';
import { ClientOnly } from 'remix-utils/client-only';
import { useSearchParams } from '@remix-run/react';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { Chat } from '~/components/chat/Chat.client';
import { Header } from '~/components/header/Header';
import BackgroundRays from '~/components/ui/BackgroundRays';

export const meta: MetaFunction = () => {
  return [
    { title: 'GenVibe - Generate the Vibe' },
    {
      name: 'description',
      content: 'Build stunning apps instantly. From idea to production in seconds with GenVibe AI.',
    },
  ];
};

export const loader = () => ({});

/**
 * Landing page component for GenVibe
 * Note: Settings functionality should ONLY be accessed through the sidebar menu.
 * Do not add settings button/panel to this landing page as it was intentionally removed
 * to keep the UI clean and consistent with the design system.
 */
export default function Index() {
  const [searchParams] = useSearchParams();

  useEffect(() => {
    // Check for login success parameter
    if (searchParams.get('login') === 'success') {
      toast.success('Welcome back to GenVibe! 🎉', {
        position: 'bottom-right',
        autoClose: 3000,
      });

      // Clean up URL by removing the parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('login');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [searchParams]);

  /*
   * Allow access to homepage without authentication
   * Authentication will be required when user tries to send messages
   */
  return (
    <div className="flex flex-col h-full w-full bg-bolt-elements-background-depth-1">
      <BackgroundRays />
      <Header />
      <ClientOnly
        fallback={
          <div className="flex-1 flex items-center justify-center">
            <div className="text-bolt-elements-textSecondary">Loading...</div>
          </div>
        }
      >
        {() => <Chat />}
      </ClientOnly>
    </div>
  );
}
