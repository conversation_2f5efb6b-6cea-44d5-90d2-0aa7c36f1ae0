import type { LoaderFunction } from '@remix-run/cloudflare';
import { LLMManager } from '~/lib/modules/llm/manager';
import { getApiKeysFromCookie } from '~/lib/api/cookies';
import { getOptionalAuth } from '~/lib/auth/auth.server';
import { ApiKeyService } from '~/lib/services/apiKeyService';
import { createClient } from '@supabase/supabase-js';

export const loader: LoaderFunction = async ({ context, request }) => {
  // Get API keys from cookie (legacy support)
  const cookieHeader = request.headers.get('Cookie');
  const apiKeysFromCookie = getApiKeysFromCookie(cookieHeader);

  // Initialize the LLM manager to access environment variables
  const llmManager = LLMManager.getInstance(context?.cloudflare?.env as any);

  // Get all provider instances to find their API token keys
  const providers = llmManager.getAllProviders();

  // Create a comprehensive API keys object starting with cookies
  const apiKeys: Record<string, string> = { ...apiKeysFromCookie };

  // Try to get API keys from Supabase database if user is authenticated
  try {
    const user = await getOptionalAuth(request);

    if (user) {
      // Create Supabase client for server-side use
      const supabaseUrl = process.env.VITE_SUPABASE_URL || (context?.cloudflare?.env as any)?.VITE_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || (context?.cloudflare?.env as any)?.SUPABASE_SERVICE_ROLE_KEY;

      if (supabaseUrl && supabaseServiceKey) {
        const supabase = createClient(supabaseUrl, supabaseServiceKey);

        // Get all API keys for the user from database
        const dbApiKeys = await ApiKeyService.getAllApiKeysForChat(supabase, user.id);

        // Merge database keys with existing keys (database takes precedence)
        Object.assign(apiKeys, dbApiKeys);
      }
    }
  } catch (error) {
    console.error('Error fetching API keys from database:', error);
    // Continue with cookie-based keys if database fails
  }

  // For each provider, check environment variables as fallback
  for (const provider of providers) {
    if (!provider.config.apiTokenKey) {
      continue;
    }

    const envVarName = provider.config.apiTokenKey;

    // Skip if we already have this provider's key from cookies or database
    if (apiKeys[provider.name]) {
      continue;
    }

    // Check environment variables in order of precedence
    const envValue =
      (context?.cloudflare?.env as Record<string, any>)?.[envVarName] ||
      process.env[envVarName] ||
      llmManager.env[envVarName];

    if (envValue) {
      apiKeys[provider.name] = envValue;
    }
  }

  return Response.json(apiKeys);
};
