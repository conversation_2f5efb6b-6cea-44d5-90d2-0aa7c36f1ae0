/* ===== MODERN BEAUTIFUL TOAST DESIGN ===== */

/* Base Toast Container */
.Toastify__toast-container {
  width: auto !important;
  max-width: 420px !important;
  padding: 0 !important;
}

/* Legacy Toast Fallback */
.Toastify__toast {
  --at-apply: shadow-md;
  background-color: var(--bolt-elements-bg-depth-2);
  color: var(--bolt-elements-textPrimary);
  border: 1px solid var(--bolt-elements-borderColor);
  border-radius: 12px !important;
  padding: 16px 20px !important;
  min-height: auto !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Legacy Close Button */
.Toastify__close-button {
  color: var(--bolt-elements-item-contentDefault);
  opacity: 1;
  transition: all 0.2s ease;
  width: 20px !important;
  height: 20px !important;

  &:hover {
    color: var(--bolt-elements-item-contentActive);
    transform: scale(1.1);
  }
}

/* ===== MODERN TOAST COMPONENT ===== */
.modern-toast {
  /* Layout & Structure */
  border-radius: 16px !important;
  padding: 0 !important;
  min-height: auto !important;
  width: 100% !important;
  max-width: 400px !important;
  margin: 0 !important;

  /* Glass Morphism Base */
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);

  /* Advanced Shadows */
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;

  /* Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5;

  /* Animations */
  animation: modernToastSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);

  /* Hover Effects */
  &:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.2),
      0 12px 24px rgba(0, 0, 0, 0.15),
      0 4px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.25),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
  }

  /* Toast Content Layout */
  .Toastify__toast-body {
    padding: 20px 24px !important;
    margin: 0 !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;

    /* Icon Container */
    &::before {
      content: '';
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      margin-top: 1px;
    }
  }

  /* Close Button Redesign */
  .Toastify__close-button {
    position: absolute !important;
    top: 16px !important;
    right: 16px !important;
    width: 24px !important;
    height: 24px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
    opacity: 0.8 !important;

    &:hover {
      opacity: 1 !important;
      background: rgba(255, 255, 255, 0.2) !important;
      transform: scale(1.1) rotate(90deg) !important;
      border-color: rgba(255, 255, 255, 0.3) !important;
    }

    svg {
      width: 12px !important;
      height: 12px !important;
    }
  }
}

/* ===== TOAST TYPE VARIANTS ===== */

/* Success Toast */
.modern-toast.modern-toast-success {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.95) 0%,
    rgba(5, 150, 105, 0.9) 50%,
    rgba(4, 120, 87, 0.85) 100%) !important;
  border: 1px solid rgba(16, 185, 129, 0.4) !important;
  color: #ffffff !important;

  .Toastify__toast-body::before {
    content: '✓';
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .Toastify__close-button {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      color: #ffffff !important;
    }
  }
}

/* Error Toast */
.modern-toast.modern-toast-error {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.95) 0%,
    rgba(220, 38, 38, 0.9) 50%,
    rgba(185, 28, 28, 0.85) 100%) !important;
  border: 1px solid rgba(239, 68, 68, 0.4) !important;
  color: #ffffff !important;

  .Toastify__toast-body::before {
    content: '✕';
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .Toastify__close-button {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      color: #ffffff !important;
    }
  }
}

/* Warning Toast */
.modern-toast.modern-toast-warning {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.95) 0%,
    rgba(217, 119, 6, 0.9) 50%,
    rgba(180, 83, 9, 0.85) 100%) !important;
  border: 1px solid rgba(245, 158, 11, 0.4) !important;
  color: #ffffff !important;

  .Toastify__toast-body::before {
    content: '⚠';
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .Toastify__close-button {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      color: #ffffff !important;
    }
  }
}

/* Info Toast */
.modern-toast.modern-toast-info {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.95) 0%,
    rgba(37, 99, 235, 0.9) 50%,
    rgba(29, 78, 216, 0.85) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  color: #ffffff !important;

  .Toastify__toast-body::before {
    content: 'ℹ';
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .Toastify__close-button {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      color: #ffffff !important;
    }
  }
}

/* Default/Loading Toast */
.modern-toast:not(.modern-toast-success):not(.modern-toast-error):not(.modern-toast-warning):not(.modern-toast-info) {
  background: linear-gradient(135deg,
    rgba(71, 85, 105, 0.95) 0%,
    rgba(51, 65, 85, 0.9) 50%,
    rgba(30, 41, 59, 0.85) 100%) !important;
  border: 1px solid rgba(71, 85, 105, 0.4) !important;
  color: #ffffff !important;

  .Toastify__toast-body::before {
    content: '○';
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: modernToastSpin 1s linear infinite;
  }

  .Toastify__close-button {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      color: #ffffff !important;
    }
  }
}

/* ===== ANIMATIONS ===== */

@keyframes modernToastSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-10%) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes modernToastSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 480px) {
  .Toastify__toast-container {
    max-width: calc(100vw - 32px) !important;
    left: 16px !important;
    right: 16px !important;
  }

  .modern-toast {
    max-width: 100% !important;

    .Toastify__toast-body {
      padding: 16px 20px !important;
      font-size: 13px !important;
    }
  }
}

/* ===== DARK MODE ENHANCEMENTS ===== */

@media (prefers-color-scheme: dark) {
  .modern-toast {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2),
      0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;

    &:hover {
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 12px 24px rgba(0, 0, 0, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
    }
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .modern-toast {
    animation: none !important;
    transition: none !important;

    &:hover {
      transform: none !important;
    }

    .Toastify__close-button:hover {
      transform: scale(1.05) !important;
    }
  }

  .modern-toast .Toastify__toast-body::before {
    animation: none !important;
  }
}

/* ===== LEGACY COMPATIBILITY ===== */
.modern-toast-body {
  padding: 0 !important;
  margin: 0 !important;
  font-weight: 500;
  line-height: 1.5;
}
