.animated {
  animation-fill-mode: both;
  animation-duration: var(--animate-duration, 0.2s);
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);

  &.fadeInRight {
    animation-name: fadeInRight;
  }

  &.fadeOutRight {
    animation-name: fadeOutRight;
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}

.dropdown-animation {
  opacity: 0;
  animation: fadeMoveDown 0.15s forwards;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeMoveDown {
  to {
    opacity: 1;
    transform: translateY(6px);
  }
}

/* GenVibe Brand Animations */
@keyframes genvibe-gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes genvibe-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes genvibe-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.genvibe-gradient-text {
  background: linear-gradient(135deg, #3B82F6, #9333EA);
  background-size: 200% 200%;
  animation: genvibe-gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.genvibe-logo-pulse {
  animation: genvibe-pulse 2s infinite;
}

.genvibe-float {
  animation: genvibe-float 3s ease-in-out infinite;
}

/* Page Load Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}
