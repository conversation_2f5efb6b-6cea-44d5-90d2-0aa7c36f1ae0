/**
 * Secure logging utility that respects production environment
 * Replaces direct console usage to prevent sensitive data exposure
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogConfig {
  level: LogLevel;
  enableInProduction: boolean;
  sanitize: boolean;
}

class SecureLogger {
  private config: LogConfig;
  private isProduction: boolean;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.config = {
      level: (process.env.VITE_LOG_LEVEL as LogLevel) || (this.isProduction ? 'error' : 'debug'),
      enableInProduction: false, // Never enable debug logs in production
      sanitize: true,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    if (this.isProduction && !this.config.enableInProduction && level !== 'error') {
      return false;
    }

    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    };

    return levels[level] >= levels[this.config.level];
  }

  private sanitizeData(data: any): any {
    if (!this.config.sanitize) return data;

    if (typeof data === 'string') {
      // Remove potential sensitive patterns
      return data
        .replace(/api[_-]?key[s]?[:\s=]+[^\s\n]+/gi, 'api_key=***')
        .replace(/token[s]?[:\s=]+[^\s\n]+/gi, 'token=***')
        .replace(/password[s]?[:\s=]+[^\s\n]+/gi, 'password=***')
        .replace(/secret[s]?[:\s=]+[^\s\n]+/gi, 'secret=***')
        .replace(/bearer\s+[^\s\n]+/gi, 'bearer ***')
        .replace(/authorization[:\s=]+[^\s\n]+/gi, 'authorization=***');
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized = { ...data };
      const sensitiveKeys = ['apiKey', 'token', 'password', 'secret', 'authorization', 'key'];

      for (const key of Object.keys(sanitized)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          sanitized[key] = '***';
        }
      }
      return sanitized;
    }

    return data;
  }

  private formatMessage(level: LogLevel, scope: string, ...args: any[]): any[] {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] ${scope ? `[${scope}]` : ''}`;

    const sanitizedArgs = args.map(arg => this.sanitizeData(arg));
    return [prefix, ...sanitizedArgs];
  }

  debug(scope: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      if (process.env.NODE_ENV === 'development') {
        console.log(...this.formatMessage('debug', scope, ...args));
      }
    }
  }

  info(scope: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.info(...this.formatMessage('info', scope, ...args));
    }
  }

  warn(scope: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(...this.formatMessage('warn', scope, ...args));
    }
  }

  error(scope: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(...this.formatMessage('error', scope, ...args));
    }
  }

  // Development-only logging (completely disabled in production)
  dev(...args: any[]): void {
    if (!this.isProduction) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[DEV]', ...args);
      };
    }
  }
}

// Create singleton instance
export const secureLogger = new SecureLogger();

// Convenience exports
export const log = {
  debug: (scope: string, ...args: any[]) => secureLogger.debug(scope, ...args),
  info: (scope: string, ...args: any[]) => secureLogger.info(scope, ...args),
  warn: (scope: string, ...args: any[]) => secureLogger.warn(scope, ...args),
  error: (scope: string, ...args: any[]) => secureLogger.error(scope, ...args),
  dev: (...args: any[]) => secureLogger.dev(...args),
};

// Production console override (only in browser)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Override console methods in production to prevent accidental logging
  const originalConsole = { ...console };

  console.log = () => {}; // Disable console.log in production
  console.debug = () => {}; // Disable console.debug in production
  console.info = () => {}; // Disable console.info in production

  // Keep warn and error but sanitize them
  console.warn = (...args: any[]) => {
    const sanitized = args.map(arg => secureLogger.sanitizeData(arg));
    originalConsole.warn(...sanitized);
  };

  console.error = (...args: any[]) => {
    const sanitized = args.map(arg => secureLogger.sanitizeData(arg));
    originalConsole.error(...sanitized);
  };
}
