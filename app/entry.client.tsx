import { RemixBrowser } from '@remix-run/react';
import { startTransition } from 'react';
import { hydrateRoot } from 'react-dom/client';

// SECURITY: Disable console logs in production to prevent data leaks
if (process.env.NODE_ENV === 'production') {
  console.log = () => {};
  console.debug = () => {};
  console.info = () => {};

  // Keep warn and error but sanitize sensitive data
  const originalWarn = console.warn;
  const originalError = console.error;

  console.warn = (...args: any[]) => {
    const sanitized = args.map((arg) => {
      if (typeof arg === 'string') {
        return arg
          .replace(/api[_-]?key[s]?[:\s=]+[^\s\n]+/gi, 'api_key=***')
          .replace(/token[s]?[:\s=]+[^\s\n]+/gi, 'token=***')
          .replace(/password[s]?[:\s=]+[^\s\n]+/gi, 'password=***')
          .replace(/secret[s]?[:\s=]+[^\s\n]+/gi, 'secret=***')
          .replace(/bearer\s+[^\s\n]+/gi, 'bearer ***');
      }
      return arg;
    });
    originalWarn(...sanitized);
  };

  console.error = (...args: any[]) => {
    const sanitized = args.map((arg) => {
      if (typeof arg === 'string') {
        return arg
          .replace(/api[_-]?key[s]?[:\s=]+[^\s\n]+/gi, 'api_key=***')
          .replace(/token[s]?[:\s=]+[^\s\n]+/gi, 'token=***')
          .replace(/password[s]?[:\s=]+[^\s\n]+/gi, 'password=***')
          .replace(/secret[s]?[:\s=]+[^\s\n]+/gi, 'secret=***')
          .replace(/bearer\s+[^\s\n]+/gi, 'bearer ***');
      }
      return arg;
    });
    originalError(...sanitized);
  };
}

startTransition(() => {
  hydrateRoot(document.getElementById('root')!, <RemixBrowser />);
});
