/*
 * @ts-nocheck
 * Preventing TS checks with files presented in the video for a better presentation.
 */
import type { JSONValue, Message } from 'ai';
import React, { type RefCallback, useEffect, useState } from 'react';
import { ClientOnly } from 'remix-utils/client-only';
import { motion } from 'framer-motion';
import { Menu } from '~/components/sidebar/Menu.client';
import { IconButton } from '~/components/ui/IconButton';
import { Workbench } from '~/components/workbench/Workbench.client';
import { classNames } from '~/utils/classNames';
import { Messages } from './Messages.client';
import { SendButton } from './SendButton.client';
import { getApiKeysFromCookies } from './APIKeyManager';
import Cookies from 'js-cookie';
import * as Tooltip from '@radix-ui/react-tooltip';

import styles from './BaseChat.module.scss';
import { ExamplePrompts } from '~/components/chat/ExamplePrompts';
import GitCloneButton from './GitCloneButton';

import FilePreview from './FilePreview';
import { SpeechRecognitionButton } from '~/components/chat/SpeechRecognition';
import type { ProviderInfo } from '~/types/model';
import { ScreenshotStateManager } from './ScreenshotStateManager';
import { toast } from 'react-toastify';
import type { ActionAlert, SupabaseAlert, DeployAlert } from '~/types/actions';
import DeployChatAlert from '~/components/deploy/DeployAlert';
import ChatAlert from './ChatAlert';
import type { ModelInfo } from '~/lib/modules/llm/types';
import ProgressCompilation from './ProgressCompilation';
import type { ProgressAnnotation } from '~/types/context';
import type { ActionRunner } from '~/lib/runtime/action-runner';
import { SupabaseChatAlert } from '~/components/chat/SupabaseAlert';
// import { SupabaseConnection } from '~/components/chat/SupabaseConnection'; // Hidden from users
import { ExpoQrModal } from '~/components/workbench/ExpoQrModal';
import { expoUrlAtom } from '~/lib/stores/qrCodeStore';
import { useStore } from '@nanostores/react';
import { ImagePlus } from 'lucide-react';
import { StickToBottom, useStickToBottomContext } from '~/lib/hooks';
import { useAuthStatus } from '~/lib/hooks/useAuthRequiredChat';
import { useApiKeySetup } from '~/lib/hooks/useApiKeySetup';
import { ApiKeySetupModal } from './ApiKeySetupModal';
import { UserSettingsModal } from '~/components/modals/UserSettingsModal';
import { UpgradeModal } from '~/components/modals/UpgradeModal';
import { modalStore, modalActions } from '~/lib/stores/modals';
import { useMessageLimits } from '~/lib/hooks/useMessageLimits';
import { MessageCounter } from './MessageCounter';

const TEXTAREA_MIN_HEIGHT = 76;

interface BaseChatProps {
  textareaRef?: React.RefObject<HTMLTextAreaElement> | undefined;
  messageRef?: RefCallback<HTMLDivElement> | undefined;
  scrollRef?: RefCallback<HTMLDivElement> | undefined;
  showChat?: boolean;
  chatStarted?: boolean;
  isStreaming?: boolean;
  onStreamingChange?: (streaming: boolean) => void;
  messages?: Message[];
  description?: string;
  enhancingPrompt?: boolean;
  promptEnhanced?: boolean;
  input?: string;
  model?: string;
  setModel?: (model: string) => void;
  provider?: ProviderInfo;
  setProvider?: (provider: ProviderInfo) => void;
  providerList?: ProviderInfo[];
  handleStop?: () => void;
  sendMessage?: (event: React.UIEvent, messageInput?: string) => void;
  handleInputChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  enhancePrompt?: () => void;
  importChat?: (description: string, messages: Message[]) => Promise<void>;
  exportChat?: () => void;
  uploadedFiles?: File[];
  setUploadedFiles?: (files: File[]) => void;
  imageDataList?: string[];
  setImageDataList?: (dataList: string[]) => void;
  actionAlert?: ActionAlert;
  clearAlert?: () => void;
  supabaseAlert?: SupabaseAlert;
  clearSupabaseAlert?: () => void;
  deployAlert?: DeployAlert;
  clearDeployAlert?: () => void;
  data?: JSONValue[] | undefined;
  actionRunner?: ActionRunner;
}

export const BaseChat = React.forwardRef<HTMLDivElement, BaseChatProps>(
  (
    {
      textareaRef,
      showChat = true,
      chatStarted = false,
      isStreaming = false,
      onStreamingChange,
      model,
      setModel,
      provider,
      setProvider,
      providerList,
      input = '',
      enhancingPrompt,
      handleInputChange,

      // promptEnhanced,
      enhancePrompt,
      sendMessage,
      handleStop,
      importChat,
      exportChat: _exportChat,
      uploadedFiles = [],
      setUploadedFiles,
      imageDataList = [],
      setImageDataList,
      messages,
      actionAlert,
      clearAlert,
      deployAlert,
      clearDeployAlert,
      supabaseAlert,
      clearSupabaseAlert,
      data,
      actionRunner,
    },
    ref,
  ) => {
    const TEXTAREA_MAX_HEIGHT = chatStarted ? 400 : 200;
    const [modelList, setModelList] = useState<ModelInfo[]>([]);

    const [isListening, setIsListening] = useState(false);
    const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
    const [transcript, setTranscript] = useState('');
    const [_isModelLoading, _setIsModelLoading] = useState<string | undefined>('all');
    const [progressAnnotations, setProgressAnnotations] = useState<ProgressAnnotation[]>([]);
    const expoUrl = useStore(expoUrlAtom);
    const [qrModalOpen, setQrModalOpen] = useState(false);

    // API Key Setup
    const {
      isSetupModalOpen,
      hasValidApiKey,
      showSetupModal,
      hideSetupModal,
      handleSetupComplete,
      canSendMessage: _canSendMessage,
      withApiKeyCheck,
    } = useApiKeySetup({
      requiredForChat: true,
      autoShowOnFirstVisit: true,
    });

    // Authentication status
    const { isAuthenticated, isPro } = useAuthStatus();

    // Message limits for real-time updates
    const messageLimits = useMessageLimits();

    // Refresh message limits when authentication changes or periodically
    useEffect(() => {
      if (isAuthenticated) {
        // Initial refresh
        messageLimits.refreshStatus();

        // Set up periodic refresh every 10 seconds for more responsive updates
        const interval = setInterval(() => {
          messageLimits.refreshStatus();
        }, 10000);

        return () => clearInterval(interval);
      }

      return undefined;
    }, [isAuthenticated, messageLimits]);

    // Modal states
    const modals = useStore(modalStore);

    // Reload API keys when authentication state changes
    useEffect(() => {
      if (isAuthenticated && typeof window !== 'undefined') {
        // Small delay to ensure API keys have been synced
        setTimeout(() => {
          try {
            const parsedApiKeys = getApiKeysFromCookies();
            console.log('🔄 Reloaded API keys after authentication:', Object.keys(parsedApiKeys));
          } catch (error) {
            console.error('Error reloading API keys after authentication:', error);
          }
        }, 1000);
      }
    }, [isAuthenticated]);

    useEffect(() => {
      if (expoUrl) {
        setQrModalOpen(true);
      }
    }, [expoUrl]);

    useEffect(() => {
      if (data) {
        const progressList = data.filter(
          (x) => typeof x === 'object' && (x as any).type === 'progress',
        ) as ProgressAnnotation[];
        setProgressAnnotations(progressList);
      }
    }, [data]);
    useEffect(() => {
      console.log(transcript);
    }, [transcript]);

    useEffect(() => {
      onStreamingChange?.(isStreaming);
    }, [isStreaming, onStreamingChange]);

    useEffect(() => {
      if (typeof window !== 'undefined' && ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        recognition.onresult = (event) => {
          const transcript = Array.from(event.results)
            .map((result) => result[0])
            .map((result) => result.transcript)
            .join('');

          setTranscript(transcript);

          if (handleInputChange) {
            const syntheticEvent = {
              target: { value: transcript },
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(syntheticEvent);
          }
        };

        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
        };

        setRecognition(recognition);
      }
    }, []);

    useEffect(() => {
      if (typeof window !== 'undefined') {
        let parsedApiKeys: Record<string, string> | undefined = {};

        try {
          parsedApiKeys = getApiKeysFromCookies();
          console.log('🔑 Loaded API keys from cookies:', Object.keys(parsedApiKeys));
        } catch (error) {
          console.error('Error loading API keys from cookies:', error);
          Cookies.remove('apiKeys');
        }

        _setIsModelLoading('all');
        fetch('/api/models')
          .then((response) => response.json())
          .then((data) => {
            const typedData = data as { modelList: ModelInfo[] };
            setModelList(typedData.modelList);
            console.log('📋 Loaded model list:', typedData.modelList.length, 'models');
          })
          .catch((error) => {
            console.error('Error fetching model list:', error);
          })
          .finally(() => {
            _setIsModelLoading(undefined);
          });
      }
    }, [providerList, provider, isAuthenticated]); // Add isAuthenticated to dependencies

    // Removed unused onApiKeysChange function

    const startListening = () => {
      if (recognition) {
        recognition.start();
        setIsListening(true);
      }
    };

    const stopListening = () => {
      if (recognition) {
        recognition.stop();
        setIsListening(false);
      }
    };

    const handleSendMessage = withApiKeyCheck((event: React.UIEvent, messageInput?: string) => {
      if (sendMessage) {
        sendMessage(event, messageInput);

        if (recognition) {
          recognition.abort(); // Stop current recognition
          setTranscript(''); // Clear transcript
          setIsListening(false);

          // Clear the input by triggering handleInputChange with empty value
          if (handleInputChange) {
            const syntheticEvent = {
              target: { value: '' },
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(syntheticEvent);
          }
        }
      }
    });

    const handleFileUpload = () => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';

      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];

        if (file) {
          const reader = new FileReader();

          reader.onload = (e) => {
            const base64Image = e.target?.result as string;
            setUploadedFiles?.([...uploadedFiles, file]);
            setImageDataList?.([...imageDataList, base64Image]);
          };
          reader.readAsDataURL(file);
        }
      };

      input.click();
    };

    const handlePaste = async (e: React.ClipboardEvent) => {
      const items = e.clipboardData?.items;

      if (!items) {
        return;
      }

      for (const item of items) {
        if (item.type.startsWith('image/')) {
          e.preventDefault();

          const file = item.getAsFile();

          if (file) {
            const reader = new FileReader();

            reader.onload = (e) => {
              const base64Image = e.target?.result as string;
              setUploadedFiles?.([...uploadedFiles, file]);
              setImageDataList?.([...imageDataList, base64Image]);
            };
            reader.readAsDataURL(file);
          }

          break;
        }
      }
    };

    const baseChat = (
      <div
        ref={ref}
        className={classNames(styles.BaseChat, 'relative flex h-full w-full overflow-hidden')}
        data-chat-visible={showChat}
      >
        <ClientOnly>{() => <Menu />}</ClientOnly>
        <div className="flex flex-col lg:flex-row overflow-y-auto w-full h-full">
          <div className={classNames(styles.Chat, 'flex flex-col flex-grow lg:min-w-[var(--chat-min-width)] h-full')}>
            {!chatStarted && (
              <motion.div
                id="intro"
                className="mt-[12vh] max-w-3xl mx-auto text-center px-4 lg:px-6"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
              >
                <motion.h1
                  className="text-3xl sm:text-4xl lg:text-5xl font-bold text-bolt-elements-textPrimary mb-4 leading-tight"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <span className="block sm:inline">Where Ideas Become </span>
                  <span className="bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                    Reality
                  </span>
                </motion.h1>

                <motion.p
                  className="text-lg text-bolt-elements-textSecondary max-w-xl mx-auto mb-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  Build stunning web & mobile apps instantly with intelligent design.{' '}
                  <span className="text-bolt-elements-textPrimary font-medium">
                    From idea to production in seconds.
                  </span>
                </motion.p>
              </motion.div>
            )}
            <StickToBottom
              className={classNames('pt-6 px-2 sm:px-6 relative', {
                'h-full flex flex-col modern-scrollbar': chatStarted,
              })}
              resize="smooth"
              initial="smooth"
            >
              <StickToBottom.Content className="flex flex-col gap-4">
                <ClientOnly>
                  {() => {
                    return chatStarted ? (
                      <Messages
                        className="flex flex-col w-full flex-1 max-w-chat pb-6 mx-auto z-1"
                        messages={messages}
                        isStreaming={isStreaming}
                      />
                    ) : null;
                  }}
                </ClientOnly>
              </StickToBottom.Content>
              <div
                className={classNames('my-auto flex flex-col gap-2 w-full max-w-chat mx-auto z-prompt mb-6', {
                  'sticky bottom-2': chatStarted,
                })}
              >
                <div className="flex flex-col gap-2">
                  {deployAlert && (
                    <DeployChatAlert
                      alert={deployAlert}
                      clearAlert={() => clearDeployAlert?.()}
                      postMessage={(message: string | undefined) => {
                        sendMessage?.({} as any, message);
                        clearSupabaseAlert?.();
                      }}
                    />
                  )}
                  {supabaseAlert && (
                    <SupabaseChatAlert
                      alert={supabaseAlert}
                      clearAlert={() => clearSupabaseAlert?.()}
                      postMessage={(message) => {
                        sendMessage?.({} as any, message);
                        clearSupabaseAlert?.();
                      }}
                    />
                  )}
                  {actionAlert && (
                    <ChatAlert
                      alert={actionAlert}
                      clearAlert={() => clearAlert?.()}
                      postMessage={(message) => {
                        sendMessage?.({} as any, message);
                        clearAlert?.();
                      }}
                    />
                  )}
                </div>
                <ScrollToBottom />
                {progressAnnotations && <ProgressCompilation data={progressAnnotations} />}

                <div
                  className={classNames(
                    'relative bg-gradient-to-br from-blue-500/5 via-white/10 to-purple-600/5 backdrop-blur-xl p-4 rounded-2xl border border-blue-500/20 relative w-full max-w-chat mx-auto z-prompt shadow-2xl isolate',
                    {
                      'shadow-xl border-blue-500/40 shadow-blue-500/10': !chatStarted,
                    },
                  )}
                >
                  {/* Message Counter - Only show when chat has started (not on home page) */}
                  {chatStarted && (
                    <ClientOnly>
                      {() => <MessageCounter messageLimits={messageLimits} isAuthenticated={isAuthenticated} />}
                    </ClientOnly>
                  )}
                  <FilePreview
                    files={uploadedFiles}
                    imageDataList={imageDataList}
                    onRemove={(index) => {
                      setUploadedFiles?.(uploadedFiles.filter((_, i) => i !== index));
                      setImageDataList?.(imageDataList.filter((_, i) => i !== index));
                    }}
                  />
                  <ClientOnly>
                    {() => (
                      <ScreenshotStateManager
                        setUploadedFiles={setUploadedFiles}
                        setImageDataList={setImageDataList}
                        uploadedFiles={uploadedFiles}
                        imageDataList={imageDataList}
                      />
                    )}
                  </ClientOnly>
                  <div
                    className={classNames(
                      'relative  rounded-xl overflow-hidden',
                      'border-none transition-all duration-300',
                    )}
                  >
                    <textarea
                      ref={textareaRef}
                      className={classNames(
                        'w-full pl-4 pt-4 pr-16 pb-4 outline-none resize-none text-bolt-elements-textPrimary placeholder-bolt-elements-textTertiary bg-transparent text-base',
                        'transition-all duration-300',
                        'focus:ring-0 focus:outline-none',
                      )}
                      onDragEnter={(e) => {
                        e.preventDefault();
                        e.currentTarget.style.border = '2px solid #1488fc';
                      }}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.currentTarget.style.border = '2px solid #1488fc';
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        e.currentTarget.style.border = '1px solid var(--bolt-elements-borderColor)';
                      }}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.currentTarget.style.border = '1px solid var(--bolt-elements-borderColor)';

                        const files = Array.from(e.dataTransfer.files);
                        files.forEach((file) => {
                          if (file.type.startsWith('image/')) {
                            const reader = new FileReader();

                            reader.onload = (e) => {
                              const base64Image = e.target?.result as string;
                              setUploadedFiles?.([...uploadedFiles, file]);
                              setImageDataList?.([...imageDataList, base64Image]);
                            };
                            reader.readAsDataURL(file);
                          }
                        });
                      }}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                          if (event.shiftKey) {
                            return;
                          }

                          event.preventDefault();

                          if (isStreaming) {
                            handleStop?.();
                            return;
                          }

                          // ignore if using input method engine
                          if (event.nativeEvent.isComposing) {
                            return;
                          }

                          handleSendMessage?.(event);
                        }
                      }}
                      value={input}
                      onChange={(event) => {
                        handleInputChange?.(event);
                      }}
                      onPaste={handlePaste}
                      style={{
                        minHeight: TEXTAREA_MIN_HEIGHT,
                        maxHeight: TEXTAREA_MAX_HEIGHT,
                      }}
                      placeholder={chatStarted ? 'Ask to modify' : 'Prompt the vibe — what app do you want to build?'}
                      translate="no"
                    />
                    <ClientOnly>
                      {() => (
                        <SendButton
                          show={input.length > 0 || isStreaming || uploadedFiles.length > 0}
                          isStreaming={isStreaming}
                          disabled={!providerList || providerList.length === 0}
                          onClick={(event) => {
                            if (isStreaming) {
                              handleStop?.();
                              return;
                            }

                            if (input.length > 0 || uploadedFiles.length > 0) {
                              handleSendMessage?.(event);
                            }
                          }}
                        />
                      )}
                    </ClientOnly>
                    <div className="flex justify-between items-center text-sm px-4 py-3">
                      <div className="flex gap-3 items-center">
                        <IconButton
                          title="Upload images & files"
                          className="transition-all hover:scale-105 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary p-2"
                          onClick={() => handleFileUpload()}
                        >
                          <ImagePlus size={18} />
                        </IconButton>

                        <IconButton
                          title="Enhance prompt"
                          disabled={input.length === 0 || enhancingPrompt}
                          className={classNames(
                            'transition-all hover:scale-105 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary p-2',
                            enhancingPrompt ? 'opacity-100' : '',
                          )}
                          onClick={() => {
                            enhancePrompt?.();
                            toast.success('Prompt enhanced!');
                          }}
                        >
                          {enhancingPrompt ? (
                            <div className="i-svg-spinners:90-ring-with-bg text-bolt-elements-loader-progress text-lg animate-spin"></div>
                          ) : (
                            <div className="i-bolt:stars text-lg"></div>
                          )}
                        </IconButton>

                        {/* <SpeechRecognitionButton
                          isListening={isListening}
                          onStart={startListening}
                          onStop={stopListening}
                          disabled={isStreaming}
                        /> */}
                      </div>
                      <div className="flex items-center gap-4">
                        {/* Model & Provider Button - Only show when user has valid API keys */}
                        {hasValidApiKey && (
                          <button
                            title="Change Model & Provider"
                            className={classNames(
                              'transition-all flex items-center gap-2 px-3 py-2 rounded-lg text-xs',
                              'bg-gradient-to-r from-blue-500/10 to-purple-600/10 hover:from-blue-500/20 hover:to-purple-600/20',
                              'border border-blue-500/30 hover:border-blue-500/50 text-bolt-elements-textSecondary',
                              'hover:text-bolt-elements-textPrimary backdrop-blur-sm hover:scale-105',
                              'min-w-0 max-w-[200px]',
                            )}
                            onClick={showSetupModal}
                            disabled={!providerList || providerList.length === 0}
                          >
                            <div className="w-3 h-3 i-ph:gear flex-shrink-0" />
                            <span className="text-xs font-medium truncate">
                              {provider?.name} • {model}
                            </span>
                          </button>
                        )}

                        {/* Authentication Status - Simplified for bottom toolbar */}
                        {isAuthenticated && isPro ? (
                          <div className="text-xs text-green-400 flex items-center gap-1.5 px-2 py-1 rounded-md bg-green-500/10">
                            <div className="i-ph:crown w-3 h-3" />
                            <span className="font-medium">Pro</span>
                          </div>
                        ) : null}

                        {input.length > 3 && (
                          <div className="text-xs text-bolt-elements-textTertiary hidden sm:flex items-center gap-1">
                            <kbd className="px-1.5 py-0.5 rounded bg-bolt-elements-background-depth-2 border border-bolt-elements-borderColor/30 font-mono">
                              ⇧
                            </kbd>
                            <span>+</span>
                            <kbd className="px-1.5 py-0.5 rounded bg-bolt-elements-background-depth-2 border border-bolt-elements-borderColor/30 font-mono">
                              ↵
                            </kbd>
                            <span className="ml-1">new line</span>
                          </div>
                        )}
                      </div>
                      {/* <SupabaseConnection /> - Hidden from users */}
                      <ExpoQrModal open={qrModalOpen} onClose={() => setQrModalOpen(false)} />
                    </div>
                  </div>
                </div>
              </div>
            </StickToBottom>
            <div className="flex flex-col justify-center">
              {!chatStarted && (
                <motion.div
                  className="flex justify-center gap-4 mb-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.5 }}
                >
                  <GitCloneButton importChat={importChat} />
                </motion.div>
              )}
              <div className="flex flex-col gap-8">
                {!chatStarted &&
                  ExamplePrompts((event, messageInput) => {
                    if (isStreaming) {
                      handleStop?.();
                      return;
                    }

                    handleSendMessage?.(event, messageInput);
                  })}

                {/* Footer section */}
                {!chatStarted && (
                  <motion.div
                    className="text-center py-8 px-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 2 }}
                  >
                    <p className="text-xs text-bolt-elements-textTertiary">
                      <span className="bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent font-medium">
                        GenVibe
                      </span>{' '}
                      • Powered by AI • Built for creators • Made with ❤️
                    </p>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
          <ClientOnly>
            {() => (
              <Workbench
                actionRunner={actionRunner ?? ({} as ActionRunner)}
                chatStarted={chatStarted}
                isStreaming={isStreaming}
              />
            )}
          </ClientOnly>
        </div>
      </div>
    );

    return (
      <Tooltip.Provider delayDuration={200}>
        {baseChat}

        {/* API Key Setup Modal */}
        <ApiKeySetupModal
          isOpen={isSetupModalOpen}
          onClose={hideSetupModal}
          onComplete={handleSetupComplete}
          selectedProvider={provider}
          modelList={modelList}
          onProviderChange={setProvider}
          onModelChange={setModel}
          currentModel={model}
        />

        {/* User Settings & Upgrade Modals */}
        <UserSettingsModal isOpen={modals.userSettings} onClose={modalActions.closeUserSettings} />
        <UpgradeModal isOpen={modals.upgrade} onClose={modalActions.closeUpgrade} />
      </Tooltip.Provider>
    );
  },
);

function ScrollToBottom() {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  return (
    !isAtBottom && (
      <button
        className="absolute z-50 top-[0%] translate-y-[-100%] text-4xl rounded-lg left-[50%] translate-x-[-50%] px-1.5 py-0.5 flex items-center gap-2 bg-bolt-elements-background-depth-3 border border-bolt-elements-borderColor text-bolt-elements-textPrimary text-sm"
        onClick={() => scrollToBottom()}
      >
        Go to last message
        <span className="i-ph:arrow-down animate-bounce" />
      </button>
    )
  );
}
