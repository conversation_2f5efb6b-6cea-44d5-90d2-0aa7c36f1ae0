import React from 'react';
import { motion } from 'framer-motion';
import type { Template } from '~/types/template';
import { STARTER_TEMPLATES } from '~/utils/constants';

interface FrameworkLinkProps {
  template: Template;
}

const FrameworkLink: React.FC<FrameworkLinkProps> = ({ template }) => (
  <motion.a
    href={`/git?url=https://github.com/${template.githubRepo}.git`}
    data-state="closed"
    data-discover="true"
    className="group relative flex items-center justify-center p-3 rounded-xl bg-bolt-elements-background-depth-2 hover:bg-bolt-elements-background-depth-3 border border-bolt-elements-borderColor hover:border-blue-500/50 transition-all duration-300 hover:shadow-lg"
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.95 }}
  >
    <div
      className={`${template.icon} w-8 h-8 text-3xl text-bolt-elements-textSecondary group-hover:text-blue-500 transition-all duration-300`}
      title={template.label}
    />
    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-bolt-elements-textTertiary opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
      {template.label}
    </div>
  </motion.a>
);

const StarterTemplates: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.div
      className="flex flex-col items-center gap-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <motion.div className="text-center" variants={itemVariants}>
        <h3 className="text-lg font-semibold text-bolt-elements-textPrimary mb-2">Quick Start Templates</h3>
        <p className="text-sm text-bolt-elements-textSecondary">Choose a framework to get started instantly</p>
      </motion.div>
      <div className="flex justify-center">
        <motion.div
          className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4 max-w-4xl"
          variants={containerVariants}
        >
          {STARTER_TEMPLATES.map((template) => (
            <motion.div key={template.name} variants={itemVariants}>
              <FrameworkLink template={template} />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default StarterTemplates;
