/**
 * 🛡️ Secure API Key Provider - Safe Integration Component
 * 
 * This component provides secure API key management alongside the existing system.
 * It can be gradually enabled without breaking existing functionality.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSecureApiKeys } from '~/lib/hooks/useSecureApiKeys';
import { getApiKeysFromCookies } from '~/components/chat/APIKeyManager';

interface SecureApiKeyContextType {
  // Secure API key methods
  secureApiKeys: Record<string, string>;
  getSecureApiKey: (provider: string) => Promise<string | null>;
  saveSecureApiKey: (provider: string, apiKey: string) => Promise<void>;
  hasSecureApiKeys: () => Promise<boolean>;
  
  // Backward compatibility methods
  legacyApiKeys: Record<string, string>;
  getLegacyApiKey: (provider: string) => string | null;
  
  // Unified methods (tries secure first, falls back to legacy)
  getApiKey: (provider: string) => Promise<string | null>;
  getAllApiKeys: () => Promise<Record<string, string>>;
  
  // Status
  isSecureMode: boolean;
  loading: boolean;
  error: string | null;
  source: 'supabase' | 'cookie' | 'mixed';
}

const SecureApiKeyContext = createContext<SecureApiKeyContextType | null>(null);

interface SecureApiKeyProviderProps {
  children: React.ReactNode;
  enableSecureMode?: boolean;  // Feature flag to enable secure mode
  enableFallback?: boolean;    // Enable fallback to cookies
}

export function SecureApiKeyProvider({ 
  children, 
  enableSecureMode = true,
  enableFallback = true 
}: SecureApiKeyProviderProps) {
  // Secure API key management
  const {
    apiKeys: secureApiKeys,
    loading: secureLoading,
    error: secureError,
    source: secureSource,
    getApiKey: getSecureApiKey,
    saveApiKey: saveSecureApiKey,
    hasApiKeys: hasSecureApiKeys,
    loadApiKeys: loadSecureApiKeys,
    getApiKeysFromCookies
  } = useSecureApiKeys({
    enableSecureMode,
    enableFallback,
    autoSync: true
  });

  // Legacy cookie-based API keys
  const [legacyApiKeys, setLegacyApiKeys] = useState<Record<string, string>>({});

  // Load legacy API keys from cookies
  useEffect(() => {
    try {
      const cookieKeys = getApiKeysFromCookies();
      setLegacyApiKeys(cookieKeys);
    } catch (error) {
      console.error('🚨 Failed to load legacy API keys:', error);
    }
  }, [getApiKeysFromCookies]);

  // Legacy API key getter
  const getLegacyApiKey = (provider: string): string | null => {
    return legacyApiKeys[provider] || null;
  };

  // Unified API key getter (tries secure first, falls back to legacy)
  const getApiKey = async (provider: string): Promise<string | null> => {
    try {
      // Try secure API key first
      if (enableSecureMode) {
        const secureKey = await getSecureApiKey(provider);
        if (secureKey) {
          return secureKey;
        }
      }

      // Fallback to legacy cookie-based key
      if (enableFallback) {
        const legacyKey = getLegacyApiKey(provider);
        if (legacyKey) {
          console.log(`🍪 Using legacy API key for ${provider}`);
          return legacyKey;
        }
      }

      return null;
    } catch (error) {
      console.error(`🚨 Failed to get API key for ${provider}:`, error);
      
      // Emergency fallback to legacy
      if (enableFallback) {
        return getLegacyApiKey(provider);
      }
      
      return null;
    }
  };

  // Get all API keys from both sources
  const getAllApiKeys = async (): Promise<Record<string, string>> => {
    try {
      const allKeys: Record<string, string> = {};

      // Get secure keys
      if (enableSecureMode) {
        const secureKeys = await loadSecureApiKeys();
        Object.assign(allKeys, secureKeys);
      }

      // Add legacy keys (if not already present)
      if (enableFallback) {
        Object.entries(legacyApiKeys).forEach(([provider, key]) => {
          if (!allKeys[provider] && key) {
            allKeys[provider] = key;
          }
        });
      }

      return allKeys;
    } catch (error) {
      console.error('🚨 Failed to get all API keys:', error);
      
      // Emergency fallback to legacy only
      if (enableFallback) {
        return legacyApiKeys;
      }
      
      return {};
    }
  };

  // Determine current source
  const getSource = (): 'supabase' | 'cookie' | 'mixed' => {
    const hasSecure = Object.keys(secureApiKeys).length > 0;
    const hasLegacy = Object.keys(legacyApiKeys).length > 0;

    if (hasSecure && hasLegacy) return 'mixed';
    if (hasSecure) return 'supabase';
    if (hasLegacy) return 'cookie';
    return 'cookie'; // Default
  };

  const contextValue: SecureApiKeyContextType = {
    // Secure methods
    secureApiKeys,
    getSecureApiKey,
    saveSecureApiKey,
    hasSecureApiKeys,

    // Legacy methods
    legacyApiKeys,
    getLegacyApiKey,

    // Unified methods
    getApiKey,
    getAllApiKeys,

    // Status
    isSecureMode: enableSecureMode,
    loading: secureLoading,
    error: secureError,
    source: getSource()
  };

  return (
    <SecureApiKeyContext.Provider value={contextValue}>
      {children}
    </SecureApiKeyContext.Provider>
  );
}

// Hook to use the secure API key context
export function useSecureApiKeyContext(): SecureApiKeyContextType {
  const context = useContext(SecureApiKeyContext);
  
  if (!context) {
    throw new Error('useSecureApiKeyContext must be used within a SecureApiKeyProvider');
  }
  
  return context;
}

// Optional hook with fallback for gradual migration
export function useApiKeyContext(): SecureApiKeyContextType | null {
  return useContext(SecureApiKeyContext);
}

// Debug component for development
export function ApiKeyDebugPanel() {
  const context = useApiKeyContext();
  
  if (!context) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
        <p className="text-yellow-800">SecureApiKeyProvider not found - using legacy system</p>
      </div>
    );
  }

  const {
    secureApiKeys,
    legacyApiKeys,
    isSecureMode,
    loading,
    error,
    source
  } = context;

  return (
    <div className="p-4 bg-gray-100 border border-gray-300 rounded text-sm">
      <h3 className="font-bold mb-2">🔐 API Key Debug Panel</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-green-700">Secure Keys (Supabase)</h4>
          <ul className="text-xs">
            {Object.keys(secureApiKeys).map(provider => (
              <li key={provider} className="text-green-600">
                ✅ {provider}: {secureApiKeys[provider] ? '***' : 'None'}
              </li>
            ))}
            {Object.keys(secureApiKeys).length === 0 && (
              <li className="text-gray-500">No secure keys</li>
            )}
          </ul>
        </div>

        <div>
          <h4 className="font-semibold text-blue-700">Legacy Keys (Cookies)</h4>
          <ul className="text-xs">
            {Object.keys(legacyApiKeys).map(provider => (
              <li key={provider} className="text-blue-600">
                🍪 {provider}: {legacyApiKeys[provider] ? '***' : 'None'}
              </li>
            ))}
            {Object.keys(legacyApiKeys).length === 0 && (
              <li className="text-gray-500">No legacy keys</li>
            )}
          </ul>
        </div>
      </div>

      <div className="mt-4 pt-2 border-t border-gray-300">
        <p><strong>Mode:</strong> {isSecureMode ? '🔐 Secure' : '🍪 Legacy'}</p>
        <p><strong>Source:</strong> {source}</p>
        <p><strong>Loading:</strong> {loading ? '⏳ Yes' : '✅ No'}</p>
        {error && <p className="text-red-600"><strong>Error:</strong> {error}</p>}
      </div>
    </div>
  );
}

export default SecureApiKeyProvider;
