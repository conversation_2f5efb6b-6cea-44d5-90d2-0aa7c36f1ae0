import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { <PERSON>, Zap, Bot, Key, X, ExternalLink, Shield, Check, CheckCircle, Loader2, Save } from 'lucide-react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { classNames } from '~/utils/classNames';
import { ApiKeyService, verifyApiKey, SUPPORTED_PROVIDERS } from '~/lib/services/apiKeyService';
import { supabase } from '~/lib/supabase/client';
import type { ProviderInfo } from '~/types/model';
import type { ModelInfo } from '~/lib/modules/llm/types';
import { getCodingModelsForProvider, getRecommendedModel } from '~/lib/modules/llm/types';

interface ProviderConfig {
  name: string;
  displayName: string;
  icon: string;
  getApiKeyUrl: string;
  description: string;
  models?: ModelInfo[];
}

interface ApiKeySetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
  title?: string;
  description?: string;
  selectedProvider?: ProviderInfo;
  modelList?: ModelInfo[];
  onProviderChange?: (provider: ProviderInfo) => void;
  onModelChange?: (model: string) => void;
  currentModel?: string;
}

// Simple icon mapping
const getProviderIcon = (iconName: string) => {
  switch (iconName) {
    case 'Brain':
      return Brain;
    case 'Zap':
      return Zap;
    case 'Bot':
      return Bot;
    default:
      return Brain;
  }
};

export function ApiKeySetupModal({
  isOpen,
  onClose,
  onComplete,
  title = 'AI Configuration',
  description = 'Configure your AI provider, model, and API keys',
  selectedProvider,
  modelList = [],
  onProviderChange,
  onModelChange,
  currentModel,
}: ApiKeySetupModalProps) {
  const auth = useStore(authState);
  const [internalSelectedProvider, setInternalSelectedProvider] = useState<string>(selectedProvider?.name || 'Google');
  const [apiKey, setApiKey] = useState<string>('');
  const [saving, setSaving] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [hasValidKey, setHasValidKey] = useState(false);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [verificationResult, setVerificationResult] = useState<{
    isValid: boolean;
    billingEnabled?: boolean;
    recommendedModel?: string;
  } | null>(null);
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);
  const [isKeyVerified, setIsKeyVerified] = useState(false);
  const [originalApiKey, setOriginalApiKey] = useState<string>('');

  const activeProvider = selectedProvider?.name || internalSelectedProvider;
  const selectedProviderConfig = SUPPORTED_PROVIDERS.find((p) => p.name === activeProvider);

  useEffect(() => {
    if (isOpen && !auth.isAuthenticated) {
      window.location.href = '/signin';
      return;
    }
    if (isOpen && auth.isAuthenticated) {
      loadExistingKeysOnly();
    }
  }, [isOpen, auth.isAuthenticated]);

  useEffect(() => {
    if (selectedProvider?.name && selectedProvider.name !== internalSelectedProvider) {
      setInternalSelectedProvider(selectedProvider.name);
    }
  }, [selectedProvider?.name, internalSelectedProvider]);

  useEffect(() => {
    if (currentModel) {
      setSelectedModel(currentModel);
    } else if (availableModels.length > 0) {
      setSelectedModel(availableModels[0].name);
    }
  }, [activeProvider, availableModels, currentModel]);

  const handleProviderChange = async (providerName: string) => {
    if (selectedProvider && onProviderChange) {
      const newProvider = { name: providerName } as ProviderInfo;
      onProviderChange(newProvider);
      const newProviderModels = modelList.filter((model) => model.provider === providerName);
      if (newProviderModels.length > 0 && onModelChange) {
        onModelChange(newProviderModels[0].name);
      }
    } else {
      setInternalSelectedProvider(providerName);
    }

    if (activeProvider !== providerName) {
      setApiKey('');
      setOriginalApiKey('');
      setValidationError(null);
      setVerificationResult(null);
      setShowModelSelector(false);
      setAvailableModels([]);
      setIsKeyVerified(false);
      if (auth.user) {
        await loadExistingApiKeyWithoutVerification(auth.user.id, providerName);
      }
    }
  };

  const handleModelChange = async (modelName: string) => {
    setSelectedModel(modelName);
    if (onModelChange) {
      onModelChange(modelName);
    }

    if (isKeyVerified && verificationResult?.isValid && auth.user) {
      try {
        const keyToSave = typeof apiKey === 'string' ? apiKey.trim() : '';
        await ApiKeyService.saveApiKey(supabase, auth.user.id, activeProvider, keyToSave);
        await ApiKeyService.syncApiKeysToCookies(supabase, auth.user.id);
        toast.success(`Model switched to ${modelName}`, {
          position: 'bottom-right',
          autoClose: 2000,
        });
      } catch (error) {
        console.error('Error updating model selection:', error);
      }
    }
  };

  const checkExistingKeys = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) return;

      const hasKeys = await ApiKeyService.hasApiKeys(supabase, user.id);
      setHasValidKey(hasKeys);

      // Load existing API key for current provider
      await loadExistingApiKey(user.id, activeProvider);
    } catch (error) {
      console.error('Error checking existing keys:', error);
    }
  };

  const loadExistingKeysOnly = async () => {
    if (!auth.user) return;

    try {
      const existingKey = await ApiKeyService.getApiKey(supabase, auth.user.id, activeProvider);
      if (existingKey && typeof existingKey === 'string' && existingKey.trim().length > 0) {
        setApiKey(existingKey);
        setOriginalApiKey(existingKey);
        setHasValidKey(true);
        setIsKeyVerified(true);
        setShowModelSelector(true);

        const models = getCodingModelsForProvider(activeProvider);
        setAvailableModels(models);

        if (currentModel) {
          setSelectedModel(currentModel);
        } else if (models.length > 0) {
          setSelectedModel(models[0].name);
        }
      } else {
        setApiKey('');
        setOriginalApiKey('');
        setHasValidKey(false);
        setIsKeyVerified(false);
        setShowModelSelector(false);
        setAvailableModels([]);
      }
    } catch (error) {
      console.error(`Error loading existing API key for ${activeProvider}:`, error);
    }
  };

  // Load existing API key without verification (for provider switching)
  const loadExistingApiKeyWithoutVerification = async (userId: string, providerName: string) => {
    try {
      const existingKey = await ApiKeyService.getApiKey(supabase, userId, providerName);

      if (existingKey && typeof existingKey === 'string' && existingKey.trim().length > 0) {
        setApiKey(existingKey);
        setOriginalApiKey(existingKey);
        setHasValidKey(true);
        setIsKeyVerified(true);
        setShowModelSelector(true);

        // Load available models for the provider without verification
        const models = getCodingModelsForProvider(providerName);
        setAvailableModels(models);

        console.log(`✅ Loaded existing API key for ${providerName} (no verification)`);
      } else {
        // No existing key, reset state
        setApiKey('');
        setOriginalApiKey('');
        setHasValidKey(false);
        setIsKeyVerified(false);
        setShowModelSelector(false);
        setAvailableModels([]);
      }
    } catch (error) {
      console.error(`Error loading existing API key for ${providerName}:`, error);
    }
  };

  const loadExistingApiKey = async (userId: string, providerName: string) => {
    try {
      const existingKey = await ApiKeyService.getApiKey(supabase, userId, providerName);

      console.log('🔍 Loading existing API key debug:', {
        provider: providerName,
        keyType: typeof existingKey,
        keyValue:
          existingKey && typeof existingKey === 'string' && existingKey.length > 8
            ? `${existingKey.substring(0, 8)}...`
            : existingKey,
        isString: typeof existingKey === 'string',
        keyLength: existingKey ? (typeof existingKey === 'string' ? existingKey.length : 'not string') : 'null',
      });

      if (existingKey && typeof existingKey === 'string' && existingKey.trim().length > 0) {
        setApiKey(existingKey);
        setOriginalApiKey(existingKey);
        // Auto-verify the existing key to show models (only if key is not empty)
        if (existingKey.trim().length >= 10) {
          await handleVerifyKey(existingKey);
          setIsKeyVerified(true);
        }
      } else {
        // No existing key, reset states
        setApiKey('');
        setOriginalApiKey('');
        setIsKeyVerified(false);
        setVerificationResult(null);
        setShowModelSelector(false);
      }
    } catch (error) {
      console.error('Error loading existing API key:', error);
      setApiKey('');
      setOriginalApiKey('');
      setIsKeyVerified(false);
    }
  };

  const handleSave = async () => {
    if (!verificationResult?.isValid) {
      setValidationError('Please verify your API key first');
      return;
    }

    if (!selectedModel) {
      setValidationError('Please select a model');
      return;
    }

    if (!auth.isAuthenticated) {
      toast.error('Please sign in to save API keys', {
        position: 'bottom-right',
      });
      return;
    }

    try {
      setSaving(true);
      setValidationError(null);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        setValidationError('Authentication required');
        return;
      }

      // Save directly using the service with client-side Supabase
      const keyToSave = typeof apiKey === 'string' ? apiKey.trim() : '';
      await ApiKeyService.saveApiKey(supabase, user.id, activeProvider, keyToSave);

      // Sync to cookies for compatibility with existing system
      await ApiKeyService.syncApiKeysToCookies(supabase, user.id);

      toast.success(`${activeProvider} API key saved with ${selectedModel}!`, {
        position: 'bottom-right',
      });
      setApiKey('');
      setHasValidKey(true);
      onComplete?.();
      onClose();
    } catch (error) {
      console.error('Error saving API key:', error);
      setValidationError(error instanceof Error ? error.message : 'Failed to save API key');
    } finally {
      setSaving(false);
    }
  };

  const handleApiKeyChange = (value: string) => {
    console.log('🔑 API Key input changed:', {
      valueType: typeof value,
      valueLength: value ? value.length : 0,
      valuePreview: value && value.length > 0 ? `${value.substring(0, 8)}...` : 'empty',
      currentApiKeyState: apiKey,
      currentApiKeyLength: apiKey ? apiKey.length : 0,
    });
    setApiKey(value);

    // Check if the key has changed from the original
    const hasKeyChanged = value !== originalApiKey;

    if (hasKeyChanged) {
      // Key changed, need to re-verify
      setIsKeyVerified(false);
      setValidationError(null);
      setVerificationResult(null);
      setShowModelSelector(false);
    } else if (value === originalApiKey && originalApiKey) {
      // Key is back to original and was previously verified
      setIsKeyVerified(true);
      // Re-verify to restore model selector
      if (value.trim()) {
        handleVerifyKey(value);
      }
    }
  };

  const handleVerifyKey = async (keyToVerify?: string) => {
    // Enhanced type handling to ensure we always have a string
    let keyValue: string;

    if (keyToVerify && typeof keyToVerify === 'string') {
      keyValue = keyToVerify.trim();
    } else if (typeof apiKey === 'string') {
      keyValue = apiKey.trim();
    } else {
      keyValue = '';
    }

    console.log('🔍 HandleVerifyKey Debug:', {
      provider: activeProvider,
      keyToVerify: keyToVerify && typeof keyToVerify === 'string' ? `${keyToVerify.substring(0, 8)}...` : 'undefined',
      apiKeyState:
        typeof apiKey === 'string' && apiKey.length > 0 ? `${apiKey.substring(0, 8)}...` : `type: ${typeof apiKey}`,
      keyValue: keyValue && keyValue.length > 0 ? `${keyValue.substring(0, 8)}...` : 'empty',
      keyValueType: typeof keyValue,
      keyValueLength: keyValue.length,
      processedCorrectly: typeof keyValue === 'string',
    });

    // Now we can safely use keyValue as a string
    if (!keyValue || keyValue.length === 0) {
      setValidationError('Please enter an API key');
      return;
    }

    if (keyValue.length < 10) {
      setValidationError('API key appears to be too short (minimum 10 characters)');
      return;
    }

    try {
      setVerifying(true);
      setValidationError(null);

      const result = await verifyApiKey(activeProvider, keyValue);
      setVerificationResult(result);

      if (result.isValid) {
        // Get available models based on billing status
        const models = getCodingModelsForProvider(activeProvider, result.billingEnabled);
        setAvailableModels(models);

        // Set recommended model
        if (result.recommendedModel) {
          setSelectedModel(result.recommendedModel);
          if (onModelChange) {
            onModelChange(result.recommendedModel);
          }
        }

        setShowModelSelector(true);
        setIsKeyVerified(true);

        // Update original key if this was a new verification
        if (!keyToVerify) {
          setOriginalApiKey(keyValue);
        }

        // Only show toast for manual verification, not auto-verification
        if (!keyToVerify) {
          toast.success(`✅ API key verified! ${availableModels.length} models available`, {
            position: 'bottom-right',
          });
        }
      } else {
        setValidationError(result.error || 'API key verification failed');
        setShowModelSelector(false);
        setIsKeyVerified(false);
      }
    } catch (error) {
      console.error('Error verifying API key:', error);
      setValidationError(error instanceof Error ? error.message : 'Verification failed');
      setShowModelSelector(false);
      setIsKeyVerified(false);
    } finally {
      setVerifying(false);
    }
  };

  const handleSkip = () => {
    toast.info('You can add API keys later in the settings.', {
      position: 'bottom-right',
      autoClose: 4000,
    });
    onClose();
  };

  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] border border-gray-200 dark:border-gray-700 flex flex-col">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Key className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{title}</h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-6 space-y-6">
                {/* Provider Selection */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Choose AI Provider</h3>
                  <div className="grid grid-cols-1 gap-3">
                    {SUPPORTED_PROVIDERS.map((provider) => (
                      <button
                        key={provider.name}
                        onClick={() => handleProviderChange(provider.name)}
                        className={classNames(
                          'bg-transparent p-4 rounded-lg border-2 transition-colors text-left',
                          activeProvider === provider.name
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-gray-50 dark:hover:bg-gray-800/50',
                        )}
                      >
                        <div className="flex items-center gap-4">
                          <div
                            className={classNames(
                              'w-10 h-10 rounded-lg flex items-center justify-center',
                              activeProvider === provider.name
                                ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                                : 'bg-gray-100 dark:bg-gray-800',
                            )}
                          >
                            {React.createElement(getProviderIcon(provider.icon), {
                              className: classNames(
                                'w-5 h-5',
                                activeProvider === provider.name ? 'text-white' : 'text-gray-600 dark:text-gray-400',
                              ),
                            })}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 dark:text-white">{provider.displayName}</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">{provider.description}</div>
                          </div>
                          {activeProvider === provider.name && <CheckCircle className="w-5 h-5 text-blue-500" />}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* API Key Input */}
                {selectedProviderConfig && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {selectedProviderConfig.displayName} API Key
                      </h3>
                      <a
                        href={selectedProviderConfig.getApiKeyUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
                      >
                        Get API Key
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </div>

                    <div className="space-y-4">
                      <div className="relative">
                        <input
                          type="password"
                          value={apiKey}
                          onChange={(e) => handleApiKeyChange(e.target.value)}
                          placeholder={`Enter your ${selectedProviderConfig.displayName} API key...`}
                          className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                        />
                        {apiKey && (
                          <div className="absolute right-3 top-1/2 -translate-y-1/2">
                            {isKeyVerified ? (
                              <CheckCircle className="w-5 h-5 text-green-500" />
                            ) : (
                              <Key className="w-5 h-5 text-gray-400" />
                            )}
                          </div>
                        )}
                      </div>

                      <button
                        onClick={handleVerifyKey}
                        disabled={!(typeof apiKey === 'string' && apiKey.trim()) || verifying || isKeyVerified}
                        className={classNames(
                          'bg-transparent w-full px-4 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2',
                          isKeyVerified
                            ? 'bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 text-green-700 dark:text-green-300 cursor-not-allowed'
                            : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white disabled:opacity-50 disabled:cursor-not-allowed',
                        )}
                      >
                        {verifying ? (
                          <>
                            <Loader2 className="w-5 h-5 animate-spin" />
                            Verifying...
                          </>
                        ) : isKeyVerified ? (
                          <>
                            <CheckCircle className="w-5 h-5" />
                            Key Verified
                          </>
                        ) : (
                          <>
                            <Shield className="w-5 h-5" />
                            Verify API Key
                          </>
                        )}
                      </button>

                      {validationError && (
                        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                          <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-2">
                            <X className="w-4 h-4" />
                            {validationError}
                          </p>
                        </div>
                      )}

                      {verificationResult?.isValid && (
                        <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                          <p className="text-sm text-green-600 dark:text-green-400 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4" />
                            API key verified successfully!
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Model Selection */}
                {showModelSelector && availableModels.length > 0 && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Select Model</h3>
                      {isKeyVerified && (
                        <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-lg">
                          Auto-saves on change
                        </span>
                      )}
                    </div>
                    <div className="grid grid-cols-1 gap-3">
                      {availableModels.map((model) => (
                        <button
                          key={model.name}
                          onClick={() => handleModelChange(model.name)}
                          className={classNames(
                            'bg-transparent p-3 rounded-lg border-2 transition-colors text-left',
                            selectedModel === model.name
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-gray-50 dark:hover:bg-gray-800/50',
                          )}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-gray-900 dark:text-white">{model.label}</div>
                              {model.description && (
                                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{model.description}</div>
                              )}
                            </div>
                            {selectedModel === model.name && <CheckCircle className="w-5 h-5 text-blue-500" />}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
              <div className="space-y-4">
                <div className="flex gap-3">
                  <button
                    onClick={handleSkip}
                    className="bg-transparent flex-1 px-4 py-3 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 rounded-lg font-medium transition-colors"
                  >
                    Skip for Now
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={!verificationResult?.isValid || !selectedModel || saving}
                    className={classNames(
                      'bg-transparent flex-1 px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2',
                      verificationResult?.isValid && selectedModel
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed',
                    )}
                  >
                    {saving ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Saving...
                      </>
                    ) : isKeyVerified && apiKey === originalApiKey ? (
                      <>
                        <Check className="w-4 h-4" />
                        Continue
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Save & Continue
                      </>
                    )}
                  </button>
                </div>

                <div className="text-center">
                  <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center justify-center gap-1">
                    <Shield className="w-3 h-3" />
                    Your API keys are securely stored and encrypted.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
