import React, { useState, useEffect, useRef } from 'react';
import { Crown, Clock, Loader2 } from 'lucide-react';
import { modalActions } from '~/lib/stores/modals';
import type { UseMessageLimitsReturn } from '~/lib/hooks/useMessageLimits';

interface MessageCounterProps {
  messageLimits: UseMessageLimitsReturn;
  isAuthenticated: boolean;
}

export function MessageCounter({ messageLimits, isAuthenticated }: MessageCounterProps) {
  const { status, remaining, isPro, resetTime, loading } = messageLimits;

  // Stable state management to prevent flickering
  const [shouldShow, setShouldShow] = useState(false);
  const [lastValidStatus, setLastValidStatus] = useState<{
    remaining: number;
    isPro: boolean;
    resetTime: Date | null;
  } | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Debounce timer to prevent rapid show/hide cycles
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // Determine if component should be shown (consolidated logic)
  const shouldShowCounter = isAuthenticated && !isPro && status && remaining !== undefined;

  // Update stable state with debouncing
  useEffect(() => {
    // Clear any existing timer
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    // Debounce the show/hide decision to prevent flickering
    debounceTimer.current = setTimeout(() => {
      if (shouldShowCounter) {
        setShouldShow(true);
        // Update last valid status for optimistic UI
        if (status) {
          setLastValidStatus({
            remaining,
            isPro,
            resetTime,
          });
        }
      } else if (isAuthenticated && isPro) {
        // Pro user - hide immediately
        setShouldShow(false);
      }
      // For other cases (loading, no auth), keep current state to prevent flicker
    }, 100); // 100ms debounce

    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [shouldShowCounter, isAuthenticated, isPro, remaining, resetTime, status]);

  // Handle visibility transitions
  useEffect(() => {
    if (shouldShow) {
      setIsVisible(true);
    } else {
      // Delay hiding to allow for smooth transitions
      const hideTimer = setTimeout(() => {
        setIsVisible(false);
      }, 150);

      return () => clearTimeout(hideTimer);
    }
  }, [shouldShow]);

  // Don't render if not visible
  if (!isVisible) {
    return null;
  }

  // Use last valid status during loading for optimistic UI
  const displayStatus = loading && lastValidStatus ? lastValidStatus : { remaining, isPro, resetTime };
  const displayRemaining = displayStatus.remaining;
  const displayResetTime = displayStatus.resetTime;

  // Format reset time
  const formatResetTime = (resetTime: Date | null) => {
    if (!resetTime) return '20h';

    const now = new Date();
    const diffMs = resetTime.getTime() - now.getTime();
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));

    if (diffHours <= 1) {
      const diffMinutes = Math.ceil(diffMs / (1000 * 60));
      return `${diffMinutes}m`;
    } else if (diffHours < 24) {
      return `${diffHours}h`;
    } else {
      return '20h';
    }
  };

  const resetTimeText = formatResetTime(displayResetTime);

  // Simple unified design for all states
  const getStatusColor = () => {
    if (displayRemaining === 0) return 'text-red-600 dark:text-red-400';
    if (displayRemaining <= 2) return 'text-orange-600 dark:text-orange-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  const getStatusText = () => {
    if (displayRemaining === 0) return 'Daily limit reached';
    return `${displayRemaining} message${displayRemaining !== 1 ? 's' : ''} remaining`;
  };

  return (
    <div
      className={`flex items-center justify-between px-4 py-2 mb-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 transition-all duration-200 ${
        shouldShow ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform -translate-y-2'
      }`}
    >
      <div className="flex items-center space-x-3">
        <div className={`text-sm font-medium ${getStatusColor()} flex items-center gap-2`}>
          {loading && <Loader2 className="w-3 h-3 animate-spin opacity-60" />}
          {getStatusText()}
        </div>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
          <Clock className="w-3 h-3 mr-1" />
          Resets in {resetTimeText}
        </div>
      </div>
      <button
        onClick={() => modalActions.openUpgrade()}
        className="bg-transparent px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
        disabled={loading}
      >
        Upgrade
      </button>
    </div>
  );
}
