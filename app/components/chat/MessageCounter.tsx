import React from 'react';
import { Crown, Clock } from 'lucide-react';
import { modalActions } from '~/lib/stores/modals';
import type { UseMessageLimitsReturn } from '~/lib/hooks/useMessageLimits';

interface MessageCounterProps {
  messageLimits: UseMessageLimitsReturn;
  isAuthenticated: boolean;
}

export function MessageCounter({ messageLimits, isAuthenticated }: MessageCounterProps) {
  const { status, remaining, isPro, resetTime } = messageLimits;

  // Don't show anything if user is not authenticated or still loading
  if (!isAuthenticated || !status || messageLimits.loading) {
    return null;
  }

  // Don't show for pro users
  if (isPro) {
    return null;
  }

  // Format reset time
  const formatResetTime = (resetTime: Date | null) => {
    if (!resetTime) return '20h';

    const now = new Date();
    const diffMs = resetTime.getTime() - now.getTime();
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));

    if (diffHours <= 1) {
      const diffMinutes = Math.ceil(diffMs / (1000 * 60));
      return `${diffMinutes}m`;
    } else if (diffHours < 24) {
      return `${diffHours}h`;
    } else {
      return '20h';
    }
  };

  const resetTimeText = formatResetTime(resetTime);

  // Simple unified design for all states
  const getStatusColor = () => {
    if (remaining === 0) return 'text-red-600 dark:text-red-400';
    if (remaining <= 2) return 'text-orange-600 dark:text-orange-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  const getStatusText = () => {
    if (remaining === 0) return 'Daily limit reached';
    return `${remaining} message${remaining !== 1 ? 's' : ''} remaining`;
  };

  return (
    <div className="flex items-center justify-between px-4 py-2 mb-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
      <div className="flex items-center space-x-3">
        <div className={`text-sm font-medium ${getStatusColor()}`}>{getStatusText()}</div>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
          <Clock className="w-3 h-3 mr-1" />
          Resets in {resetTimeText}
        </div>
      </div>
      <button
        onClick={() => modalActions.openUpgrade()}
        className="px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
      >
        Upgrade
      </button>
    </div>
  );
}
