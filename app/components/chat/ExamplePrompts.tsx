import React from 'react';
import { motion } from 'framer-motion';

const EXAMPLE_PROMPTS = [
  { text: 'Create a glassmorphic e-commerce store' },
  { text: 'Build a mobile-first social media app' },
  { text: 'Design a modern dashboard with analytics' },
  { text: 'Create a food delivery app with glassmorphic UI' },
  { text: 'Build a portfolio website with stunning animations' },
  { text: 'Make a real estate app with property listings' },
];

export function ExamplePrompts(sendMessage?: { (event: React.UIEvent, messageInput?: string): void | undefined }) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
      },
    },
  };

  return (
    <div id="examples" className="relative w-full max-w-5xl mx-auto mt-4">
      <motion.div
        className="flex flex-wrap justify-center gap-2 sm:gap-3"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {EXAMPLE_PROMPTS.map((examplePrompt, index: number) => {
          return (
            <motion.button
              key={index}
              variants={itemVariants}
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.2 },
              }}
              whileTap={{ scale: 0.98 }}
              onClick={(event) => {
                if (sendMessage) {
                  sendMessage(event, examplePrompt.text);
                }
              }}
              className="px-3 py-2 rounded-lg bg-gradient-to-r from-blue-500/10 to-purple-600/10 backdrop-blur-sm border border-blue-500/30 text-bolt-elements-textPrimary hover:text-white text-xs font-medium transition-all duration-300 hover:border-blue-500/50 hover:from-blue-500/20 hover:to-purple-600/20 hover:scale-105 hover:shadow-lg whitespace-nowrap"
            >
              {examplePrompt.text}
            </motion.button>
          );
        })}
      </motion.div>
    </div>
  );
}
