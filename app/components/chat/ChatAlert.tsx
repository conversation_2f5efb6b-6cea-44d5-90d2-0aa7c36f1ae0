import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import type { ActionAlert } from '~/types/actions';
import { classNames } from '~/utils/classNames';

interface Props {
  alert: ActionAlert;
  clearAlert: () => void;
  postMessage: (message: string) => void;
}

export default function ChatAlert({ alert, clearAlert, postMessage }: Props) {
  const { description, content, source } = alert;
  const [isAutoFixing, setIsAutoFixing] = useState(false);

  const isPreview = source === 'preview';
  const title = isPreview ? 'Preview Error' : 'Terminal Error';
  const message = isPreview
    ? 'We encountered an error while running the preview. Would you like ZenVibe to analyze and help resolve this issue?'
    : 'We encountered an error while running terminal commands. Would you like ZenVibe to analyze and help resolve this issue?';

  // Auto-fix functionality
  const handleAutoFix = async () => {
    setIsAutoFixing(true);

    try {
      // Generate auto-fix message based on error type
      let autoFixMessage = '';

      if (isPreview) {
        if (description.includes('SyntaxError') || description.includes('Unexpected token')) {
          autoFixMessage = `*Auto-fix syntax error* Please fix the syntax error in the code:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        } else if (description.includes('Module not found') || description.includes('Cannot resolve')) {
          autoFixMessage = `*Auto-fix missing dependency* Please install the missing dependency and fix the import:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        } else if (description.includes('ReferenceError')) {
          autoFixMessage = `*Auto-fix reference error* Please fix the undefined variable or function:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        } else if (description.includes('TypeError')) {
          autoFixMessage = `*Auto-fix type error* Please fix the type-related issue:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        } else if (description.includes('404') || description.includes('Not Found')) {
          autoFixMessage = `*Auto-fix missing file* Please create the missing file or fix the path:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        } else if (description.includes('timeout') || description.includes('unresponsive')) {
          autoFixMessage = `*Auto-fix server timeout* Please optimize the code or fix server issues:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        } else {
          autoFixMessage = `*Auto-fix preview error* Please analyze and fix this preview error:\n\`\`\`\n${description}\n\`\`\`\n\nError details:\n${content}`;
        }
      } else {
        // Enhanced terminal error handling
        if (content.includes('Failed to resolve import') || content.includes('Cannot resolve module')) {
          // Extract package name from error
          const packageMatch = content.match(/["']([^"']+)["']/);
          const packageName = packageMatch ? packageMatch[1] : 'the missing package';

          // Map common package names to their correct npm package names
          const packageMappings: Record<string, string> = {
            'react-router-dom': 'react-router-dom',
            'lucide-react': 'lucide-react',
            'framer-motion': 'framer-motion',
            '@headlessui/react': '@headlessui/react',
            '@heroicons/react': '@heroicons/react',
            clsx: 'clsx',
            'class-variance-authority': 'class-variance-authority',
            'tailwind-merge': 'tailwind-merge',
          };

          const correctPackageName = packageMappings[packageName] || packageName;

          autoFixMessage =
            `*Auto-fix missing dependency* I'll install the missing package and restart the dev server.\n\n` +
            `**Issue:** The import \`${packageName}\` cannot be resolved because the package is not installed.\n\n` +
            `**Solution:** I'll fix this by:\n` +
            `1. 📦 Adding \`${correctPackageName}\` to package.json dependencies\n` +
            `2. 🔧 Running \`npm install\` to install the package\n` +
            `3. 🚀 Restarting the development server to pick up the new dependency\n\n` +
            `This will resolve the import error and get your app running again.\n\n` +
            `Error details:\n\`\`\`sh\n${content}\n\`\`\`\n`;
        } else if (content.includes('npm ERR!') || content.includes('pnpm ERR!')) {
          autoFixMessage = `*Auto-fix package manager error* I'll resolve this package installation issue:\n\`\`\`sh\n${content}\n\`\`\`\n`;
        } else if (content.includes('ENOENT') || content.includes('command not found')) {
          autoFixMessage = `*Auto-fix missing command* I'll install the missing command or fix the path issue:\n\`\`\`sh\n${content}\n\`\`\`\n`;
        } else if (content.includes('port') && content.includes('already in use')) {
          autoFixMessage = `*Auto-fix port conflict* I'll find an available port and restart the server:\n\`\`\`sh\n${content}\n\`\`\`\n`;
        } else {
          autoFixMessage = `*Auto-fix terminal error* I'll analyze and fix this terminal error:\n\`\`\`sh\n${content}\n\`\`\`\n`;
        }
      }

      // Send the auto-fix message
      postMessage(autoFixMessage);
      clearAlert();
    } catch (error) {
      console.error('Auto-fix failed:', error);
    } finally {
      setIsAutoFixing(false);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={`rounded-lg border border-bolt-elements-borderColor bg-bolt-elements-background-depth-2 p-4 mb-2`}
      >
        <div className="flex items-start">
          {/* Icon */}
          <motion.div
            className="flex-shrink-0"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className={`i-ph:warning-duotone text-xl text-bolt-elements-button-danger-text`}></div>
          </motion.div>
          {/* Content */}
          <div className="ml-3 flex-1">
            <motion.h3
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              className={`text-sm font-medium text-bolt-elements-textPrimary`}
            >
              {title}
            </motion.h3>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
              className={`mt-2 text-sm text-bolt-elements-textSecondary`}
            >
              <p>{message}</p>
              {description && (
                <div className="text-xs text-bolt-elements-textSecondary p-2 bg-bolt-elements-background-depth-3 rounded mt-4 mb-4">
                  Error: {description}
                </div>
              )}
            </motion.div>

            {/* Actions */}
            <motion.div
              className="mt-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className={classNames(' flex gap-2')}>
                <button
                  onClick={handleAutoFix}
                  disabled={isAutoFixing}
                  className={classNames(
                    `px-2 py-1.5 rounded-md text-sm font-medium`,
                    'bg-bolt-elements-button-primary-background',
                    'hover:bg-bolt-elements-button-primary-backgroundHover',
                    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-bolt-elements-button-primary-background',
                    'text-bolt-elements-button-primary-text',
                    'flex items-center gap-1.5',
                    'disabled:opacity-50 disabled:cursor-not-allowed',
                  )}
                >
                  {isAutoFixing ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                      Auto-fixing...
                    </>
                  ) : (
                    <>
                      <div className="i-ph:magic-wand-duotone"></div>
                      Auto-fix
                    </>
                  )}
                </button>
                <button
                  onClick={() =>
                    postMessage(
                      `*Fix this ${isPreview ? 'preview' : 'terminal'} error* \n\`\`\`${isPreview ? 'js' : 'sh'}\n${content}\n\`\`\`\n`,
                    )
                  }
                  className={classNames(
                    `px-2 py-1.5 rounded-md text-sm font-medium`,
                    'bg-bolt-elements-button-secondary-background',
                    'hover:bg-bolt-elements-button-secondary-backgroundHover',
                    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-bolt-elements-button-secondary-background',
                    'text-bolt-elements-button-secondary-text',
                    'flex items-center gap-1.5',
                  )}
                >
                  <div className="i-ph:chat-circle-duotone"></div>
                  Manual fix
                </button>
                <button
                  onClick={clearAlert}
                  className={classNames(
                    `px-2 py-1.5 rounded-md text-sm font-medium`,
                    'bg-bolt-elements-button-secondary-background',
                    'hover:bg-bolt-elements-button-secondary-backgroundHover',
                    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-bolt-elements-button-secondary-background',
                    'text-bolt-elements-button-secondary-text',
                  )}
                >
                  Dismiss
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
