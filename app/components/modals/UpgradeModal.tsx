import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Check, Crown, Zap, MessageCircle } from 'lucide-react';
import { toast } from 'react-toastify';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function UpgradeModal({ isOpen, onClose }: UpgradeModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgrade = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement actual upgrade logic
      toast.success('Redirecting to payment...');
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      toast.error('Failed to process upgrade');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-md overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 text-center">
            <button
              onClick={onClose}
              className="bg-transparent absolute top-4 right-4 p-1 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X size={18} />
            </button>

            <div className="flex items-center justify-center gap-2 mb-3">
              <Crown size={20} className="text-yellow-300" />
              <h1 className="text-xl font-bold">Upgrade to Pro</h1>
            </div>
            <p className="text-blue-100 text-sm">Unlock unlimited creativity</p>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Current vs Pro */}
            <div className="space-y-4 mb-6">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <MessageCircle size={16} className="text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Current: 10 messages/day</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-2">
                  <Zap size={16} className="text-blue-600" />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Pro: Unlimited messages</span>
                </div>
                <Check size={16} className="text-green-600" />
              </div>
            </div>

            {/* Pro Features */}
            <div className="space-y-3 mb-6">
              <h3 className="font-medium text-gray-900 dark:text-white text-sm">What you get:</h3>
              {['Unlimited daily messages', 'All premium AI models', 'Priority support', 'Early access features'].map(
                (feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check size={14} className="text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                  </div>
                ),
              )}
            </div>

            {/* Pricing */}
            <div className="text-center mb-6">
              <div className="flex items-baseline justify-center gap-1 mb-2">
                <span className="text-2xl font-bold text-gray-900 dark:text-white">$9.99</span>
                <span className="text-gray-500 dark:text-gray-400">/month</span>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">30-day money-back guarantee</p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <button
                onClick={handleUpgrade}
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-200 hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Processing...
                  </div>
                ) : (
                  'Upgrade Now'
                )}
              </button>

              <button
                onClick={onClose}
                className="w-full bg-transparent text-gray-600 dark:text-gray-400 py-2 px-4 rounded-xl font-medium transition-colors hover:text-gray-800 dark:hover:text-gray-200"
              >
                Maybe later
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
