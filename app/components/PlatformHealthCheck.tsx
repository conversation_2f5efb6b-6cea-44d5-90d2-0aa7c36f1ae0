import { useState } from 'react';
import { Button } from '~/components/ui/Button';
import { toast } from 'react-toastify';

interface HealthCheckResult {
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: string[];
}

export function PlatformHealthCheck() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<HealthCheckResult[]>([]);
  const [showDetails, setShowDetails] = useState(false);

  const runDiagnosis = async () => {
    setIsRunning(true);
    setResults([]);

    try {
      // Import the diagnosis function and security checks
      const [{ comprehensiveDiagnosis }, { verifyDataIntegrity }] = await Promise.all([
        import('~/lib/services/cleanupDuplicates'),
        import('~/lib/sync/dataIntegrity'),
      ]);

      // Capture console output
      const originalLog = console.log;
      const originalError = console.error;
      const logs: string[] = [];

      console.log = (...args) => {
        logs.push(args.join(' '));
        originalLog(...args);
      };

      console.error = (...args) => {
        logs.push(`ERROR: ${args.join(' ')}`);
        originalError(...args);
      };

      // Run comprehensive diagnosis
      await comprehensiveDiagnosis();

      // Run data integrity check
      const integrityResult = await verifyDataIntegrity();
      logs.push(`🔍 Data Integrity Check: ${integrityResult.overall.healthy ? 'HEALTHY' : 'ISSUES DETECTED'}`);
      if (!integrityResult.overall.healthy) {
        integrityResult.overall.issues.forEach((issue) => logs.push(`  ❌ ${issue}`));
      }

      // Restore console
      console.log = originalLog;
      console.error = originalError;

      // Parse results
      const newResults: HealthCheckResult[] = [];

      // Check for critical issues
      const criticalIssues = logs.filter((log) => log.includes('❌ CRITICAL'));
      const countMismatches = logs.filter((log) => log.includes('COUNT MISMATCH'));
      const orphanedProjects = logs.filter((log) => log.includes('ORPHANED PROJECTS'));
      const integrityIssues = logs.filter((log) => log.includes('Data Integrity Check: ISSUES DETECTED'));
      const securityIssues = logs.filter((log) => log.includes('🔒 Security'));

      if (
        criticalIssues.length === 0 &&
        countMismatches.length === 0 &&
        orphanedProjects.length === 0 &&
        integrityIssues.length === 0
      ) {
        newResults.push({
          status: 'success',
          message: '✅ Platform is healthy! No critical issues detected.',
        });
      } else {
        if (criticalIssues.length > 0) {
          newResults.push({
            status: 'error',
            message: `🚨 ${criticalIssues.length} critical storage sync issue(s) detected`,
            details: criticalIssues,
          });
        }

        if (countMismatches.length > 0) {
          newResults.push({
            status: 'warning',
            message: `⚠️ ${countMismatches.length} counting mismatch(es) detected`,
            details: countMismatches,
          });
        }

        if (orphanedProjects.length > 0) {
          newResults.push({
            status: 'warning',
            message: `🔗 ${orphanedProjects.length} project linking issue(s) detected`,
            details: orphanedProjects,
          });
        }

        if (integrityIssues.length > 0) {
          newResults.push({
            status: 'error',
            message: `🔍 Data integrity issues detected`,
            details: integrityIssues,
          });
        }
      }

      setResults(newResults);
      toast.success('Health check completed', {
        position: 'bottom-right',
        autoClose: 3000,
      });
    } catch (error) {
      console.error('Health check failed:', error);
      setResults([
        {
          status: 'error',
          message: '❌ Health check failed to run',
          details: [String(error)],
        },
      ]);
      toast.error('Health check failed', {
        position: 'bottom-right',
        autoClose: 3000,
      });
    } finally {
      setIsRunning(false);
    }
  };

  const runQuickFix = async (fixType: 'sync' | 'counting' | 'linking' | 'integrity') => {
    try {
      const [{ syncSupabaseToIndexedDB, fixAllCounting, fixProjectLinking }, { autoHealDataInconsistencies }] =
        await Promise.all([import('~/lib/services/cleanupDuplicates'), import('~/lib/sync/dataIntegrity')]);

      switch (fixType) {
        case 'sync':
          await syncSupabaseToIndexedDB();
          toast.success('Sidebar sync completed! Refresh the page.', {
            position: 'bottom-right',
            autoClose: 3000,
          });
          break;
        case 'counting':
          await fixAllCounting();
          toast.success('Counting issues fixed!', {
            position: 'bottom-right',
            autoClose: 3000,
          });
          break;
        case 'linking':
          await fixProjectLinking();
          toast.success('Project linking fixed!', {
            position: 'bottom-right',
            autoClose: 3000,
          });
          break;
        case 'integrity':
          const healResult = await autoHealDataInconsistencies();
          if (healResult.success) {
            toast.success(`Data integrity restored! ${healResult.actionsPerformed.join(', ')}`, {
              position: 'bottom-right',
              autoClose: 5000,
            });
          } else {
            toast.warning(`Partial healing completed: ${healResult.error}`, {
              position: 'bottom-right',
              autoClose: 5000,
            });
          }
          break;
      }

      // Re-run diagnosis after fix
      setTimeout(() => runDiagnosis(), 1000);
    } catch (error) {
      console.error('Quick fix failed:', error);
      toast.error('Quick fix failed', {
        position: 'bottom-right',
        autoClose: 3000,
      });
    }
  };

  const runNuclearReset = async () => {
    // Double confirmation for nuclear reset
    const firstConfirm = window.confirm(
      '⚠️ NUCLEAR RESET WARNING ⚠️\n\n' +
        'This will permanently delete ALL your data:\n' +
        '• All conversations and messages\n' +
        '• All projects\n' +
        '• All API keys\n' +
        '• All usage tracking\n' +
        '• All local storage data\n' +
        '• All cookies\n' +
        '\nYou will be logged out and redirected to the homepage.\n\n' +
        'Are you absolutely sure you want to continue?',
    );

    if (!firstConfirm) return;

    const secondConfirm = window.confirm(
      '🚨 FINAL WARNING 🚨\n\n' +
        'This action CANNOT be undone!\n' +
        'All your data will be permanently lost.\n\n' +
        'Type "DELETE EVERYTHING" in the next prompt to confirm.',
    );

    if (!secondConfirm) return;

    const finalConfirm = window.prompt('Type "DELETE EVERYTHING" (without quotes) to confirm the nuclear reset:');

    if (finalConfirm !== 'DELETE EVERYTHING') {
      toast.error('Nuclear reset cancelled - confirmation text did not match', {
        position: 'bottom-right',
        autoClose: 3000,
      });
      return;
    }

    try {
      toast.info('Nuclear reset initiated... This may take a moment.', {
        position: 'bottom-right',
        autoClose: 5000,
      });

      const { nuclearReset } = await import('~/lib/services/cleanupDuplicates');
      await nuclearReset();

      toast.success('Nuclear reset completed! Redirecting...', {
        position: 'bottom-right',
        autoClose: 2000,
      });
    } catch (error) {
      console.error('Nuclear reset failed:', error);
      toast.error('Nuclear reset failed! Check console for details.', {
        position: 'bottom-right',
        autoClose: 5000,
      });
    }
  };

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-white mb-2">🏥 Platform Health Check</h2>
          <p className="text-gray-400">Diagnose and fix storage, sync, and counting issues</p>
        </div>

        <Button onClick={runDiagnosis} disabled={isRunning} className="bg-blue-600 hover:bg-blue-700">
          {isRunning ? '🔍 Running...' : '🔍 Run Diagnosis'}
        </Button>
      </div>

      {results.length > 0 && (
        <div className="space-y-4">
          {results.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                result.status === 'success'
                  ? 'bg-green-900/20 border-green-700 text-green-300'
                  : result.status === 'warning'
                    ? 'bg-yellow-900/20 border-yellow-700 text-yellow-300'
                    : 'bg-red-900/20 border-red-700 text-red-300'
              }`}
            >
              <div className="flex items-center justify-between">
                <p className="font-medium">{result.message}</p>

                {result.details && (
                  <Button onClick={() => setShowDetails(!showDetails)} variant="ghost" size="sm" className="text-xs">
                    {showDetails ? 'Hide Details' : 'Show Details'}
                  </Button>
                )}
              </div>

              {showDetails && result.details && (
                <div className="mt-3 p-3 bg-black/20 rounded text-xs font-mono">
                  {result.details.map((detail, i) => (
                    <div key={i} className="mb-1">
                      {detail}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}

          {/* Quick Fix Buttons */}
          {results.some((r) => r.status !== 'success') && (
            <div className="mt-6 p-4 bg-gray-800 rounded-lg">
              <h3 className="text-white font-medium mb-3">🛠️ Quick Fixes</h3>
              <div className="flex flex-wrap gap-3">
                {results.some((r) => r.message.includes('storage sync')) && (
                  <Button onClick={() => runQuickFix('sync')} className="bg-blue-600 hover:bg-blue-700 text-sm">
                    🔄 Fix Sidebar Sync
                  </Button>
                )}

                {results.some((r) => r.message.includes('counting')) && (
                  <Button onClick={() => runQuickFix('counting')} className="bg-orange-600 hover:bg-orange-700 text-sm">
                    📊 Fix Counting
                  </Button>
                )}

                {results.some((r) => r.message.includes('linking')) && (
                  <Button onClick={() => runQuickFix('linking')} className="bg-purple-600 hover:bg-purple-700 text-sm">
                    🔗 Fix Project Linking
                  </Button>
                )}

                {results.some((r) => r.message.includes('integrity')) && (
                  <Button
                    onClick={() => runQuickFix('integrity')}
                    className="bg-indigo-600 hover:bg-indigo-700 text-sm"
                  >
                    🔍 Auto-Heal Data Integrity
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Nuclear Reset Section */}
      <div className="mt-6 p-4 bg-red-900/20 border border-red-700 rounded-lg">
        <h3 className="text-red-400 font-medium mb-3">💥 Nuclear Reset</h3>
        <p className="text-red-300 text-sm mb-4">
          ⚠️ <strong>DANGER ZONE:</strong> This will permanently delete ALL your data from all storage systems and log
          you out. Use only for complete platform reset during development or troubleshooting.
        </p>
        <Button
          onClick={runNuclearReset}
          className="bg-red-600 hover:bg-red-700 text-sm border-red-500 hover:border-red-400"
        >
          💥 Nuclear Reset (Delete Everything)
        </Button>
      </div>

      {/* Console Access Info */}
      <div className="mt-6 p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-medium mb-2">🔧 Developer Console Access</h3>
        <p className="text-gray-400 text-sm mb-3">For advanced debugging, open browser console and run:</p>
        <div className="bg-black p-3 rounded text-green-400 font-mono text-sm space-y-1">
          <div>comprehensiveDiagnosis() // Full platform diagnosis</div>
          <div>syncSupabaseToIndexedDB() // Fix sidebar sync</div>
          <div>fixAllCounting() // Fix all counting issues</div>
          <div>fixProjectLinking() // Fix project linking</div>
          <div className="text-blue-400">verifyDataIntegrity() // Check data integrity</div>
          <div className="text-indigo-400">autoHealDataInconsistencies() // Auto-heal data issues</div>
          <div className="text-red-400">nuclearReset() // ⚠️ DELETE EVERYTHING</div>
        </div>
      </div>
    </div>
  );
}
