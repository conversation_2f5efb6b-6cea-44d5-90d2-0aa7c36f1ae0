import { useStore } from '@nanostores/react';
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { authState, userActions, isProUser } from '~/lib/stores/user';
import { modalActions } from '~/lib/stores/modals';
import { classNames } from '~/utils/classNames';
import { ChevronDown, Settings, Crown, LogOut, Loader2, LogIn, ArrowRight } from 'lucide-react';
import { useMessageLimits } from '~/lib/hooks/useMessageLimits';

interface UserProfileProps {
  className?: string;
}

export function UserProfile({ className }: UserProfileProps) {
  const auth = useStore(authState);
  const isPro = useStore(isProUser);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Use live message limits from Supabase
  const messageLimits = useMessageLimits();
  const { remaining } = messageLimits;

  // Generate user initials function - must be defined before use
  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return 'U';
  };

  // All hooks must be called before any conditional returns
  const { user, profile } = auth;
  const initials = getInitials(profile?.full_name || user?.user_metadata?.full_name, user?.email);
  const displayName = profile?.full_name || user?.user_metadata?.full_name || user?.email;
  const avatarUrl = profile?.avatar_url || user?.user_metadata?.avatar_url;

  // Reset image error when avatar URL changes
  React.useEffect(() => {
    setImageError(false);
  }, [avatarUrl]);

  // Refresh live message count when component mounts or user changes (EVENT-BASED)
  useEffect(() => {
    if (user) {
      console.log('🔄 LIVE_PROFILE: User changed, refreshing message count...');

      // Internal refresh to avoid circular dependencies
      const refreshData = async () => {
        try {
          // Clear cache and refresh status
          messageLimits.clearCache();
          await messageLimits.refreshStatus();

          // Also force refresh the user profile to get latest subscription tier
          await userActions.forceRefreshProfile();
        } catch (error) {
          console.error('❌ Failed to refresh user data:', error);
        }
      };

      refreshData();
    }
  }, [user?.id]); // Only depend on user ID - no function dependencies

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await userActions.completeSignOut();
      // Redirect will be handled by auth state change
      window.location.href = '/signin';
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  // Show loading state while auth is being determined
  if (auth.isLoading) {
    return (
      <div className={classNames('relative', className)}>
        <div className="flex items-center gap-3 w-full p-3 rounded-lg">
          {/* Loading Icon */}
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white">
            <Loader2 size={16} className="animate-spin" />
          </div>

          {/* Loading Text */}
          <div className="flex-1 text-left">
            <div className="text-sm font-medium text-bolt-elements-textPrimary">Loading...</div>
            <div className="text-xs text-bolt-elements-textSecondary">Checking authentication</div>
          </div>
        </div>
      </div>
    );
  }

  // Show sign-in button for non-authenticated users
  if (!auth.isAuthenticated || !auth.user) {
    return (
      <div className={classNames('relative', className)}>
        <a
          href="/signin"
          className="flex items-center gap-3 w-full p-3 rounded-lg hover:bg-bolt-elements-item-backgroundHover transition-colors duration-200 group"
        >
          {/* Sign-in Icon */}
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white">
            <LogIn size={16} />
          </div>

          {/* Sign-in Text */}
          <div className="flex-1 text-left">
            <div className="text-sm font-medium text-bolt-elements-textPrimary group-hover:text-blue-500 transition-colors">
              Sign In
            </div>
            <div className="text-xs text-bolt-elements-textSecondary">Access your chats and settings</div>
          </div>

          {/* Arrow */}
          <div className="w-4 h-4 text-bolt-elements-textSecondary group-hover:text-blue-500 transition-colors">
            <ArrowRight size={16} />
          </div>
        </a>
      </div>
    );
  }

  return (
    <div className={classNames('relative', className)}>
      {/* User Profile Button */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center gap-3 w-full p-3 rounded-lg bg-transparent hover:bg-bolt-elements-item-backgroundHover transition-colors duration-200"
      >
        {/* Avatar */}
        <div className="relative">
          {avatarUrl && !imageError ? (
            <img
              src={avatarUrl}
              alt={displayName}
              className="w-8 h-8 rounded-full object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
              {initials}
            </div>
          )}

          {/* Pro Badge */}
          {isPro && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full border border-bolt-elements-background-depth-1" />
          )}
        </div>

        {/* User Info */}
        <div className="flex-1 text-left min-w-0">
          <div className="text-sm font-medium text-bolt-elements-textPrimary truncate">{displayName}</div>
          <div className="text-xs text-bolt-elements-textSecondary">{isPro ? 'Pro' : `${remaining} messages left`}</div>
        </div>

        {/* Dropdown Arrow */}
        <div
          className={classNames(
            'w-4 h-4 text-bolt-elements-textSecondary transition-transform duration-200',
            isDropdownOpen ? 'rotate-180' : '',
          )}
        >
          <ChevronDown size={16} />
        </div>
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isDropdownOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute bottom-full left-0 right-0 mb-2 bg-bolt-elements-background-depth-2 border border-bolt-elements-borderColor rounded-lg shadow-lg overflow-hidden z-50"
          >
            {/* User Info Header */}
            <div className="p-4 border-b border-bolt-elements-borderColor bg-bolt-elements-background-depth-1">
              <div className="flex items-center gap-3">
                {avatarUrl && !imageError ? (
                  <img
                    src={avatarUrl}
                    alt={displayName}
                    className="w-10 h-10 rounded-full object-cover"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                    {initials}
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-bolt-elements-textPrimary truncate">{displayName}</div>
                  <div className="text-xs text-bolt-elements-textSecondary truncate">{user?.email}</div>
                </div>
                {isPro && (
                  <div className="px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-medium rounded-full">
                    PRO
                  </div>
                )}
              </div>
            </div>

            {/* Usage Stats */}
            <div className="p-4 border-b border-bolt-elements-borderColor bg-bolt-elements-background-depth-1">
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-bolt-elements-textSecondary">Messages today</span>
                  <span className="text-bolt-elements-textPrimary">{isPro ? 'Unlimited' : `${remaining} left`}</span>
                </div>
                {!isPro && (
                  <div className="w-full bg-bolt-elements-background-depth-1 rounded-full h-1.5">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-1.5 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.max(0, (remaining / (profile?.daily_message_limit || 10)) * 100)}%`,
                      }}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2 bg-white/5 dark:bg-gray-800/20">
              <button
                onClick={() => {
                  setIsDropdownOpen(false);
                  modalActions.openUserSettings();
                }}
                className="w-full px-4 py-2 text-left text-sm text-bolt-elements-textPrimary hover:bg-bolt-elements-item-backgroundHover transition-colors duration-200 flex items-center gap-3 bg-transparent"
              >
                <Settings size={16} className="text-bolt-elements-textSecondary" />
                Settings
              </button>

              {!isPro && (
                <button
                  onClick={() => {
                    setIsDropdownOpen(false);
                    modalActions.openUpgrade();
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-bolt-elements-textPrimary hover:bg-bolt-elements-item-backgroundHover transition-colors duration-200 flex items-center gap-3 bg-transparent"
                >
                  <Crown size={16} className="text-yellow-500" />
                  Upgrade to Pro
                </button>
              )}

              <button
                onClick={handleSignOut}
                disabled={isSigningOut}
                className="w-full px-4 py-2 text-left text-sm text-red-400 hover:bg-red-500/10 transition-colors duration-200 flex items-center gap-3 disabled:opacity-50 bg-transparent"
              >
                {isSigningOut ? <Loader2 size={16} className="animate-spin" /> : <LogOut size={16} />}
                {isSigningOut ? 'Signing out...' : 'Sign out'}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Backdrop */}
      {isDropdownOpen && <div className="fixed inset-0 z-40" onClick={() => setIsDropdownOpen(false)} />}
    </div>
  );
}
