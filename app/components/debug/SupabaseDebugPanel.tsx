import React, { useState } from 'react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { supabaseDebugService } from '~/lib/services/supabaseDebugService';
import { chatStorageService } from '~/lib/services/chatStorageService';
import { projectStorageService } from '~/lib/services/projectStorageService';
import { simpleSupabaseTest } from '~/lib/services/simpleSupabaseTest';
import { toast } from 'react-toastify';

export const SupabaseDebugPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<any>(null);
  const auth = useStore(authState);

  const runDiagnostics = async () => {
    setIsRunning(true);
    try {
      const diagnostics = await supabaseDebugService.runDiagnostics();
      setResults(diagnostics);

      if (diagnostics.auth.status !== 'authenticated') {
        toast.error('Authentication required for database operations');
      } else if (diagnostics.connection.status === 'error') {
        toast.error('Database connection failed');
      } else if (diagnostics.permissions.status === 'some_errors') {
        toast.warning('Some database tables are not accessible');
      } else {
        toast.success('Database diagnostics completed');
      }
    } catch (error) {
      console.error('Diagnostics failed:', error);
      toast.error('Diagnostics failed - check console');
    } finally {
      setIsRunning(false);
    }
  };

  const generateUUID = () => {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const testChatStorage = async () => {
    if (!auth.user) {
      toast.error('Please sign in first');
      return;
    }

    setIsRunning(true);
    try {
      // Test conversation creation with proper UUID
      const testConvId = generateUUID();
      const convId = await chatStorageService.saveConversation(
        testConvId,
        'Debug Test Conversation',
        'Testing chat storage',
        'web',
      );

      if (convId) {
        toast.success('✅ Conversation saved successfully');

        // Test message saving
        const testMessages = [
          {
            id: '1',
            role: 'user' as const,
            content: 'Hello, this is a debug test message',
          },
          {
            id: '2',
            role: 'assistant' as const,
            content: 'This is a test response with code:\n```javascript\nconsole.log("Debug test");\n```',
          },
        ];

        const messagesSaved = await chatStorageService.saveMessages(convId, testMessages);
        if (messagesSaved) {
          toast.success('✅ Messages saved successfully');
        } else {
          toast.error('❌ Failed to save messages');
        }
      } else {
        toast.error('❌ Failed to save conversation');
      }
    } catch (error) {
      console.error('Chat storage test failed:', error);
      toast.error('Chat storage test failed - check console');
    } finally {
      setIsRunning(false);
    }
  };

  const testSimpleStorage = async () => {
    if (!auth.user) {
      toast.error('Please sign in first');
      return;
    }

    setIsRunning(true);
    try {
      const results = await simpleSupabaseTest.runAllSimpleTests();

      if (results.overall.success) {
        toast.success('✅ All simple tests passed!');
      } else {
        toast.error('❌ Some simple tests failed - check console');
      }

      setResults(results);
    } catch (error) {
      console.error('Simple storage test failed:', error);
      toast.error('Simple storage test failed - check console');
    } finally {
      setIsRunning(false);
    }
  };

  const testProjectStorage = async () => {
    if (!auth.user) {
      toast.error('Please sign in first');
      return;
    }

    setIsRunning(true);
    try {
      const testFiles = {
        'package.json': {
          type: 'file' as const,
          content: JSON.stringify(
            {
              name: 'debug-test-project',
              version: '1.0.0',
              dependencies: { react: '^18.0.0' },
            },
            null,
            2,
          ),
        },
        'src/App.tsx': {
          type: 'file' as const,
          content:
            'import React from "react";\n\nfunction App() {\n  return <div>Debug Test</div>;\n}\n\nexport default App;',
        },
      };

      const projectId = await projectStorageService.saveProject(
        null, // No conversation link for test
        'Debug Test Project',
        testFiles,
        'Testing project storage',
      );

      if (projectId) {
        toast.success('✅ Project saved successfully');
      } else {
        toast.error('❌ Failed to save project');
      }
    } catch (error) {
      console.error('Project storage test failed:', error);
      toast.error('Project storage test failed - check console');
    } finally {
      setIsRunning(false);
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-red-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg hover:bg-red-700 transition-colors z-50"
        title="Open Supabase Debug Panel"
      >
        🔧 Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">🔧 Supabase Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          ✕
        </button>
      </div>

      <div className="space-y-3">
        {/* Auth Status */}
        <div className="text-sm">
          <span className="font-medium">Auth Status: </span>
          <span className={auth.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
            {auth.isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
          </span>
          {auth.user && <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">User: {auth.user.email}</div>}
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className="w-full bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? '🔄 Running...' : '🔍 Run Diagnostics'}
          </button>

          <button
            onClick={testSimpleStorage}
            disabled={isRunning || !auth.isAuthenticated}
            className="w-full bg-green-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? '🔄 Testing...' : '✨ Simple Test (Recommended)'}
          </button>

          <button
            onClick={testChatStorage}
            disabled={isRunning || !auth.isAuthenticated}
            className="w-full bg-yellow-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? '🔄 Testing...' : '💬 Advanced Chat Test'}
          </button>

          <button
            onClick={testProjectStorage}
            disabled={isRunning || !auth.isAuthenticated}
            className="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? '🔄 Testing...' : '📁 Test Project Storage'}
          </button>
        </div>

        {/* Results */}
        {results && (
          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded text-xs">
            <div className="font-medium mb-2">Test Results:</div>
            <div className="space-y-1">
              {/* Diagnostic Results */}
              {results.auth && (
                <>
                  <div>
                    Auth:{' '}
                    <span className={results.auth.status === 'authenticated' ? 'text-green-600' : 'text-red-600'}>
                      {results.auth.status}
                    </span>
                  </div>
                  <div>
                    Connection:{' '}
                    <span className={results.connection?.status === 'connected' ? 'text-green-600' : 'text-red-600'}>
                      {results.connection?.status || 'unknown'}
                    </span>
                  </div>
                  <div>
                    Permissions:{' '}
                    <span
                      className={
                        results.permissions?.status === 'all_accessible' ? 'text-green-600' : 'text-yellow-600'
                      }
                    >
                      {results.permissions?.status || 'unknown'}
                    </span>
                  </div>
                  <div>
                    RLS:{' '}
                    <span className={results.rls?.status === 'policies_working' ? 'text-green-600' : 'text-red-600'}>
                      {results.rls?.status || 'unknown'}
                    </span>
                  </div>
                </>
              )}

              {/* Simple Test Results */}
              {results.conversation && (
                <>
                  <div>
                    Conversation:{' '}
                    <span className={results.conversation.success ? 'text-green-600' : 'text-red-600'}>
                      {results.conversation.success ? '✅ Success' : '❌ Failed'}
                    </span>
                  </div>
                  <div>
                    Message:{' '}
                    <span className={results.message?.success ? 'text-green-600' : 'text-red-600'}>
                      {results.message?.success ? '✅ Success' : '❌ Failed'}
                    </span>
                  </div>
                  <div>
                    Usage:{' '}
                    <span className={results.usage?.success ? 'text-green-600' : 'text-red-600'}>
                      {results.usage?.success ? '✅ Success' : '❌ Failed'}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Quick Console Commands */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded text-xs">
          <div className="font-medium mb-2">Console Commands:</div>
          <div className="space-y-1 text-gray-600 dark:text-gray-400">
            <div>
              <code>debugSupabase()</code> - Full diagnostics
            </div>
            <div>
              <code>testStorage()</code> - Test all storage
            </div>
            <div>
              <code>authState.get()</code> - Check auth state
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupabaseDebugPanel;
