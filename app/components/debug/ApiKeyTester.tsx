/**
 * 🧪 API Key Tester - Safe Testing Component
 * 
 * This component allows testing the new secure API key system
 * alongside the existing cookie system without breaking anything.
 */

import React, { useState, useEffect } from 'react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { useSecureApiKeys } from '~/lib/hooks/useSecureApiKeys';
import { getApiKeysFromCookies } from '~/components/chat/APIKeyManager';
import { SecureApiKeyFeatureFlags } from '~/lib/services/secureApiKeyManager';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  data?: any;
}

export function ApiKeyTester() {
  const auth = useStore(authState);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Secure API key hook
  const {
    apiKeys: secureKeys,
    loading: secureLoading,
    error: secureError,
    getApiKey: getSecureKey,
    saveApiKey: saveSecureKey,
    hasApiKeys: hasSecureKeys,
    getCacheStats,
    getApiKeysFromCookies: getSecureCookies
  } = useSecureApiKeys();

  // Test functions
  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    const results: TestResult[] = [];

    try {
      // Test 1: Check authentication
      results.push({
        test: 'Authentication Check',
        status: auth.isAuthenticated ? 'pass' : 'warning',
        message: auth.isAuthenticated 
          ? `✅ User authenticated: ${auth.user?.email}` 
          : '⚠️ User not authenticated - some tests may fail'
      });

      // Test 2: Check feature flags
      results.push({
        test: 'Feature Flags',
        status: 'pass',
        message: `🚩 Secure mode: ${SecureApiKeyFeatureFlags.USE_SECURE_API_KEYS}, Fallback: ${SecureApiKeyFeatureFlags.ENABLE_FALLBACK}`,
        data: SecureApiKeyFeatureFlags
      });

      // Test 3: Load legacy API keys from cookies
      let legacyKeys: Record<string, string> = {};
      try {
        legacyKeys = getApiKeysFromCookies();
        results.push({
          test: 'Legacy Cookie Keys',
          status: Object.keys(legacyKeys).length > 0 ? 'pass' : 'warning',
          message: `🍪 Found ${Object.keys(legacyKeys).length} legacy keys: ${Object.keys(legacyKeys).join(', ')}`,
          data: Object.keys(legacyKeys)
        });
      } catch (error) {
        results.push({
          test: 'Legacy Cookie Keys',
          status: 'fail',
          message: `❌ Failed to load legacy keys: ${error.message}`
        });
      }

      // Test 4: Check secure API keys
      if (auth.isAuthenticated) {
        try {
          const hasKeys = await hasSecureKeys();
          results.push({
            test: 'Secure API Keys Check',
            status: hasKeys ? 'pass' : 'warning',
            message: hasKeys 
              ? `✅ User has secure API keys` 
              : '⚠️ No secure API keys found',
            data: { hasKeys, secureKeys: Object.keys(secureKeys) }
          });
        } catch (error) {
          results.push({
            test: 'Secure API Keys Check',
            status: 'fail',
            message: `❌ Failed to check secure keys: ${error.message}`
          });
        }
      }

      // Test 5: Test individual provider keys
      const providers = ['Google', 'OpenAI', 'Anthropic'];
      for (const provider of providers) {
        try {
          const secureKey = await getSecureKey(provider);
          const legacyKey = legacyKeys[provider];
          
          let status: 'pass' | 'warning' | 'fail' = 'warning';
          let message = '';

          if (secureKey && legacyKey) {
            status = 'pass';
            message = `✅ ${provider}: Both secure and legacy keys available`;
          } else if (secureKey) {
            status = 'pass';
            message = `🔐 ${provider}: Secure key available`;
          } else if (legacyKey) {
            status = 'warning';
            message = `🍪 ${provider}: Only legacy key available`;
          } else {
            status = 'warning';
            message = `⚠️ ${provider}: No keys found`;
          }

          results.push({
            test: `${provider} API Key`,
            status,
            message,
            data: { 
              hasSecure: !!secureKey, 
              hasLegacy: !!legacyKey,
              secureLength: secureKey?.length || 0,
              legacyLength: legacyKey?.length || 0
            }
          });
        } catch (error) {
          results.push({
            test: `${provider} API Key`,
            status: 'fail',
            message: `❌ Error testing ${provider}: ${error.message}`
          });
        }
      }

      // Test 6: Cache statistics
      try {
        const cacheStats = getCacheStats();
        results.push({
          test: 'Cache Statistics',
          status: 'pass',
          message: `📊 Cache size: ${cacheStats.size} entries`,
          data: cacheStats
        });
      } catch (error) {
        results.push({
          test: 'Cache Statistics',
          status: 'fail',
          message: `❌ Failed to get cache stats: ${error.message}`
        });
      }

      // Test 7: Error handling test
      try {
        // Try to get a non-existent key to test error handling
        const nonExistentKey = await getSecureKey('NonExistentProvider');
        results.push({
          test: 'Error Handling',
          status: nonExistentKey === null ? 'pass' : 'warning',
          message: nonExistentKey === null 
            ? '✅ Properly handles non-existent keys' 
            : '⚠️ Unexpected result for non-existent key'
        });
      } catch (error) {
        results.push({
          test: 'Error Handling',
          status: 'pass',
          message: `✅ Properly throws errors: ${error.message}`
        });
      }

    } catch (error) {
      results.push({
        test: 'Overall Test Suite',
        status: 'fail',
        message: `❌ Test suite failed: ${error.message}`
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  // Auto-run tests when component mounts
  useEffect(() => {
    runTests();
  }, [auth.isAuthenticated]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'text-green-600 bg-green-50 border-green-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'fail': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return '✅';
      case 'warning': return '⚠️';
      case 'fail': return '❌';
      default: return '❓';
    }
  };

  const passCount = testResults.filter(r => r.status === 'pass').length;
  const warningCount = testResults.filter(r => r.status === 'warning').length;
  const failCount = testResults.filter(r => r.status === 'fail').length;

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-gray-900">🧪 API Key System Tester</h2>
        <button
          onClick={runTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? '🔄 Running...' : '🧪 Run Tests'}
        </button>
      </div>

      {/* Test Summary */}
      <div className="mb-4 p-3 bg-gray-50 rounded border">
        <div className="flex items-center gap-4 text-sm">
          <span className="text-green-600">✅ Pass: {passCount}</span>
          <span className="text-yellow-600">⚠️ Warning: {warningCount}</span>
          <span className="text-red-600">❌ Fail: {failCount}</span>
          <span className="text-gray-600">Total: {testResults.length}</span>
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-2">
        {testResults.map((result, index) => (
          <div
            key={index}
            className={`p-3 border rounded ${getStatusColor(result.status)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span>{getStatusIcon(result.status)}</span>
                <span className="font-medium">{result.test}</span>
              </div>
              {result.data && (
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-xs px-2 py-1 bg-white border rounded hover:bg-gray-50"
                >
                  {showDetails ? 'Hide' : 'Show'} Details
                </button>
              )}
            </div>
            <p className="text-sm mt-1">{result.message}</p>
            
            {showDetails && result.data && (
              <pre className="mt-2 p-2 bg-white border rounded text-xs overflow-auto">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            )}
          </div>
        ))}
      </div>

      {/* Current State */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
        <h3 className="font-bold text-blue-900 mb-2">📊 Current State</h3>
        <div className="text-sm text-blue-800 space-y-1">
          <p><strong>Authentication:</strong> {auth.isAuthenticated ? '✅ Authenticated' : '❌ Not authenticated'}</p>
          <p><strong>Secure Loading:</strong> {secureLoading ? '⏳ Loading' : '✅ Ready'}</p>
          <p><strong>Secure Error:</strong> {secureError || '✅ None'}</p>
          <p><strong>Secure Keys:</strong> {Object.keys(secureKeys).length} ({Object.keys(secureKeys).join(', ') || 'None'})</p>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
        <h3 className="font-bold text-green-900 mb-2">📋 Testing Instructions</h3>
        <ol className="text-sm text-green-800 space-y-1 list-decimal list-inside">
          <li>Ensure you're authenticated (sign in if needed)</li>
          <li>Add API keys using the existing setup modal</li>
          <li>Run tests to verify both systems work</li>
          <li>Check that fallback works when secure system fails</li>
          <li>Verify no existing functionality is broken</li>
        </ol>
      </div>
    </div>
  );
}

export default ApiKeyTester;
