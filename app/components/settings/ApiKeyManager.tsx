import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { ApiKeyService } from '~/lib/services/apiKeyService';
import { supabase } from '~/lib/supabase/client';
import { classNames } from '~/utils/classNames';

interface ApiKeyData {
  id: string;
  provider_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ProviderConfig {
  name: string;
  displayName: string;
  icon: string;
  getApiKeyUrl: string;
  description: string;
}

interface ApiKeyStatus {
  hasKeys: boolean;
  configuredProviders: string[];
  missingProviders: string[];
  recommendedProvider: string | null;
}

interface ApiKeyManagerProps {
  onKeysChange?: () => void;
}

export function ApiKeyManager({ onKeysChange }: ApiKeyManagerProps) {
  const auth = useStore(authState);
  const [apiKeys, setApiKeys] = useState<ApiKeyData[]>([]);
  const [supportedProviders, setSupportedProviders] = useState<ProviderConfig[]>([]);
  const [status, setStatus] = useState<ApiKeyStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingProvider, setEditingProvider] = useState<string | null>(null);
  const [newApiKey, setNewApiKey] = useState('');
  const [saving, setSaving] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Load API keys and providers
  useEffect(() => {
    if (auth.isAuthenticated) {
      loadApiKeys();
    }
  }, [auth.isAuthenticated]);

  const loadApiKeys = async () => {
    if (!auth.isAuthenticated || !auth.user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Use the new client-side API key service
      const apiKeyData = await ApiKeyService.getUserApiKeys(supabase, auth.user.id);
      const hasKeys = await ApiKeyService.hasApiKeys(supabase, auth.user.id);

      // Set supported providers (hardcoded for now)
      const providers: ProviderConfig[] = [
        {
          name: 'Google',
          displayName: 'Google AI',
          icon: 'i-ph:google-logo',
          getApiKeyUrl: 'https://aistudio.google.com/app/apikey',
          description: 'Fast and cost-effective AI models',
        },
        {
          name: 'OpenAI',
          displayName: 'OpenAI',
          icon: 'i-simple-icons:openai',
          getApiKeyUrl: 'https://platform.openai.com/api-keys',
          description: 'Advanced language models including GPT-4',
        },
        {
          name: 'Anthropic',
          displayName: 'Anthropic',
          icon: 'i-simple-icons:anthropic',
          getApiKeyUrl: 'https://console.anthropic.com/account/keys',
          description: 'Claude models for complex reasoning',
        },
      ];

      const configuredProviders = apiKeyData.map((key) => key.provider_name);
      const missingProviders = providers.map((p) => p.name).filter((name) => !configuredProviders.includes(name));

      setApiKeys(apiKeyData);
      setSupportedProviders(providers);
      setStatus({
        hasKeys,
        configuredProviders,
        missingProviders,
        recommendedProvider: hasKeys ? configuredProviders[0] : 'Google',
      });
    } catch (error) {
      console.error('Error loading API keys:', error);
      toast.error('Failed to load API keys', {
        position: 'bottom-right',
      });
    } finally {
      setLoading(false);
    }
  };

  const saveApiKey = async (providerName: string, apiKey: string) => {
    if (!auth.user) {
      toast.error('Please sign in to save API keys');
      return;
    }

    try {
      setSaving(true);

      // Use the new client-side API key service
      await ApiKeyService.saveApiKey(supabase, auth.user.id, providerName, apiKey);

      // Sync to cookies for compatibility with existing system
      await ApiKeyService.syncApiKeysToCookies(supabase, auth.user.id);

      toast.success('API key saved successfully!', {
        position: 'bottom-right',
      });
      setEditingProvider(null);
      setNewApiKey('');
      await loadApiKeys();
      onKeysChange?.();
    } catch (error) {
      console.error('Error saving API key:', error);
      toast.error('Failed to save API key');
    } finally {
      setSaving(false);
    }
  };

  const deleteApiKey = async (providerName: string) => {
    if (!auth.user) {
      toast.error('Please sign in to delete API keys');
      return;
    }

    try {
      setSaving(true);

      // Use the new client-side API key service
      await ApiKeyService.deleteApiKey(supabase, auth.user.id, providerName);

      toast.success('API key deleted successfully!', {
        position: 'bottom-right',
      });
      await loadApiKeys();
      onKeysChange?.();
    } catch (error) {
      console.error('Error deleting API key:', error);
      toast.error('Failed to delete API key');
    } finally {
      setSaving(false);
    }
  };

  const handleSave = () => {
    if (!editingProvider || !newApiKey.trim()) {
      setValidationError('Please enter a valid API key');
      return;
    }
    setValidationError(null);
    saveApiKey(editingProvider, newApiKey.trim());
  };

  const handleCancel = () => {
    setEditingProvider(null);
    setNewApiKey('');
    setValidationError(null);
  };

  const handleApiKeyChange = (value: string) => {
    setNewApiKey(value);
    setValidationError(null); // Clear validation error when user types
  };

  const getProviderStatus = (providerName: string) => {
    return apiKeys.find((key) => key.provider_name === providerName && key.is_active);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-xl font-semibold text-bolt-elements-textPrimary">API Key Management</h2>
        <p className="text-sm text-bolt-elements-textSecondary">
          Securely manage your AI provider API keys. Keys are encrypted and stored safely.
        </p>
      </div>

      {/* Status Summary */}
      {status && (
        <div className="p-4 rounded-xl border border-bolt-elements-borderColor bg-bolt-elements-background-depth-1">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-bolt-elements-textPrimary">Setup Status</h3>
            {status.hasKeys ? (
              <div className="flex items-center gap-2 text-green-500">
                <div className="i-ph:check-circle-fill w-4 h-4" />
                <span className="text-xs">Ready to use</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-orange-500">
                <div className="i-ph:warning-circle w-4 h-4" />
                <span className="text-xs">Setup required</span>
              </div>
            )}
          </div>

          {status.hasKeys ? (
            <div className="space-y-2">
              <p className="text-sm text-bolt-elements-textSecondary">
                ✅ You have {status.configuredProviders.length} provider
                {status.configuredProviders.length !== 1 ? 's' : ''} configured: {status.configuredProviders.join(', ')}
              </p>
              {status.recommendedProvider && (
                <p className="text-sm text-bolt-elements-textSecondary">
                  🎯 Recommended: <span className="font-medium text-blue-500">{status.recommendedProvider}</span> (will
                  be used by default)
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <p className="text-sm text-bolt-elements-textSecondary">
                To get started, add at least one API key below. We recommend starting with{' '}
                <span className="font-medium text-blue-500">Google AI</span> as it's fast and cost-effective.
              </p>
              <div className="flex flex-wrap gap-2">
                {status.missingProviders.map((provider) => (
                  <span
                    key={provider}
                    className="px-2 py-1 text-xs rounded-md bg-bolt-elements-background-depth-2 text-bolt-elements-textTertiary"
                  >
                    {provider}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="space-y-4">
        {supportedProviders.map((provider) => {
          const hasKey = getProviderStatus(provider.name);
          const isEditing = editingProvider === provider.name;

          return (
            <motion.div
              key={provider.name}
              layout
              className="border border-bolt-elements-borderColor rounded-xl p-4 bg-bolt-elements-background-depth-1"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center`}
                  >
                    <div className={`${provider.icon} w-5 h-5 text-white`} />
                  </div>
                  <div>
                    <h3 className="font-medium text-bolt-elements-textPrimary">{provider.displayName}</h3>
                    <p className="text-xs text-bolt-elements-textSecondary">{provider.description}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {hasKey ? (
                    <div className="flex items-center gap-2 text-green-500">
                      <div className="i-ph:check-circle-fill w-4 h-4" />
                      <span className="text-xs">Configured</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-orange-500">
                      <div className="i-ph:warning-circle w-4 h-4" />
                      <span className="text-xs">Not configured</span>
                    </div>
                  )}
                </div>
              </div>

              <AnimatePresence>
                {isEditing ? (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    <div className="space-y-2">
                      <input
                        type="password"
                        value={newApiKey}
                        onChange={(e) => handleApiKeyChange(e.target.value)}
                        placeholder={`Enter your ${provider.displayName} API key`}
                        className={classNames(
                          'w-full px-3 py-2 text-sm rounded-lg border',
                          'bg-bolt-elements-background-depth-2 text-bolt-elements-textPrimary',
                          'focus:outline-none focus:ring-2',
                          validationError
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-bolt-elements-borderColor focus:ring-blue-500',
                        )}
                      />
                      {validationError && (
                        <p className="text-xs text-red-500 flex items-center gap-1">
                          <div className="i-ph:warning-circle w-3 h-3" />
                          {validationError}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <a
                        href={provider.getApiKeyUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-500 hover:text-blue-600 flex items-center gap-1"
                      >
                        <div className="i-ph:external-link w-3 h-3" />
                        Get API Key
                      </a>
                      <div className="flex gap-2">
                        <button
                          onClick={handleCancel}
                          disabled={saving}
                          className="px-3 py-1 text-xs rounded-lg border border-bolt-elements-borderColor
                                   text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary
                                   transition-colors"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleSave}
                          disabled={saving || !newApiKey.trim()}
                          className={classNames(
                            'px-3 py-1 text-xs rounded-lg font-medium transition-all',
                            'bg-gradient-to-r from-blue-500 to-purple-600 text-white',
                            'hover:from-blue-600 hover:to-purple-700',
                            'disabled:opacity-50 disabled:cursor-not-allowed',
                          )}
                        >
                          {saving ? 'Saving...' : 'Save'}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        setEditingProvider(provider.name);
                        setNewApiKey('');
                      }}
                      className="px-3 py-1.5 text-xs rounded-lg border border-bolt-elements-borderColor
                               text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary
                               hover:border-blue-500 transition-all"
                    >
                      {hasKey ? 'Update' : 'Add'} API Key
                    </button>
                    {hasKey && (
                      <button
                        onClick={() => deleteApiKey(provider.name)}
                        disabled={saving}
                        className="px-3 py-1.5 text-xs rounded-lg border border-red-500/20
                                 text-red-500 hover:bg-red-500/10 transition-all"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </div>

      <div className="text-center">
        <p className="text-xs text-bolt-elements-textTertiary">
          🔒 Your API keys are encrypted and stored securely. GenVibe never stores or logs your actual keys.
        </p>
      </div>
    </div>
  );
}
