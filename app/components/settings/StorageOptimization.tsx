import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@nanostores/react';
import { migrationState, migrationUtils, STORAGE_LIMITS } from '~/lib/stores/chatMigration';
import { authState } from '~/lib/stores/user';
import { db, getAll } from '~/lib/persistence';
import { Button } from '~/components/ui/Button';
import { toast } from 'react-toastify';

interface StorageStats {
  totalChats: number;
  totalMessages: number;
  estimatedSize: number;
  migrateableChats: number;
  localSnapshots: number;
  localArtifacts: number;
}

export function StorageOptimization() {
  const auth = useStore(authState);
  const migration = useStore(migrationState);
  const [stats, setStats] = useState<StorageStats | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Analyze current storage usage
  const analyzeStorage = async () => {
    if (!db) return;

    setIsAnalyzing(true);
    try {
      const allChats = await getAll(db);

      let totalMessages = 0;
      let estimatedSize = 0;
      let migrateableChats = 0;
      let localSnapshots = 0;
      let localArtifacts = 0;

      for (const chat of allChats) {
        totalMessages += chat.messages.length;

        // Calculate estimated size
        const chatSize = chat.messages.reduce((sum, msg) => {
          const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
          return sum + content.length;
        }, 0);
        estimatedSize += chatSize;

        // Check if migratable
        if (migrationUtils.shouldMigrateConversation(chat)) {
          migrateableChats++;
        }

        // Check for local snapshots
        if (localStorage.getItem(`snapshot:${chat.id}`)) {
          localSnapshots++;
        }

        // Check for artifacts
        if (chat.messages.some((m) => typeof m.content === 'string' && m.content.includes('boltArtifact'))) {
          localArtifacts++;
        }
      }

      setStats({
        totalChats: allChats.length,
        totalMessages,
        estimatedSize,
        migrateableChats,
        localSnapshots,
        localArtifacts,
      });
    } catch (error) {
      console.error('Error analyzing storage:', error);
      toast.error('Failed to analyze storage');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Run migration
  const handleMigration = async () => {
    if (!auth.isAuthenticated) {
      toast.error('Please sign in to migrate your data');
      return;
    }

    try {
      await migrationUtils.runMigration();
      toast.success('Migration completed successfully!');
      analyzeStorage(); // Refresh stats
    } catch (error) {
      console.error('Migration failed:', error);
      toast.error('Migration failed. Please try again.');
    }
  };

  // Format file size
  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  useEffect(() => {
    analyzeStorage();
  }, []);

  if (!stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-3">
          <div className="i-ph:spinner-gap w-5 h-5 animate-spin" />
          <span>Analyzing storage...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Storage Overview */}
      <div className="bg-bolt-elements-background-depth-2 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-bolt-elements-textPrimary mb-4">Storage Overview</h3>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-500">{stats.totalChats}</div>
            <div className="text-sm text-bolt-elements-textSecondary">Total Chats</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">{stats.totalMessages}</div>
            <div className="text-sm text-bolt-elements-textSecondary">Messages</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-500">{formatSize(stats.estimatedSize)}</div>
            <div className="text-sm text-bolt-elements-textSecondary">Estimated Size</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-500">{stats.migrateableChats}</div>
            <div className="text-sm text-bolt-elements-textSecondary">Migratable</div>
          </div>
        </div>
      </div>

      {/* Storage Strategy */}
      <div className="bg-bolt-elements-background-depth-2 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-bolt-elements-textPrimary mb-4">Smart Storage Strategy</h3>

        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
            <div>
              <div className="font-medium text-bolt-elements-textPrimary">Lightweight Cloud Storage</div>
              <div className="text-sm text-bolt-elements-textSecondary">
                Chat metadata, message summaries, and user messages stored in Supabase
              </div>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
            <div>
              <div className="font-medium text-bolt-elements-textPrimary">Local Heavy Data</div>
              <div className="text-sm text-bolt-elements-textSecondary">
                File snapshots, artifacts, and large responses kept locally for performance
              </div>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-2" />
            <div>
              <div className="font-medium text-bolt-elements-textPrimary">Intelligent Compression</div>
              <div className="text-sm text-bolt-elements-textSecondary">
                Messages over {STORAGE_LIMITS.MAX_MESSAGE_SIZE} characters are summarized
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Migration Section */}
      {auth.isAuthenticated && stats.migrateableChats > 0 && (
        <div className="bg-bolt-elements-background-depth-2 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-bolt-elements-textPrimary mb-4">Data Migration</h3>

          <div className="space-y-4">
            <div className="text-sm text-bolt-elements-textSecondary">
              Migrate your chat history to the cloud for better sync and backup. Large files and snapshots will remain
              local for optimal performance.
            </div>

            {migration.isRunning ? (
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="i-ph:spinner-gap w-5 h-5 animate-spin" />
                  <span className="text-sm">{migration.currentStep}</span>
                </div>

                {migration.total > 0 && (
                  <div className="w-full bg-bolt-elements-background-depth-1 rounded-full h-2">
                    <motion.div
                      className="bg-blue-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${(migration.progress / migration.total) * 100}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                )}

                <div className="text-xs text-bolt-elements-textSecondary">
                  {migration.progress} of {migration.total} conversations processed
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Button onClick={handleMigration} disabled={migration.isRunning}>
                  Migrate {stats.migrateableChats} Conversations
                </Button>

                <Button variant="ghost" onClick={analyzeStorage} disabled={isAnalyzing}>
                  {isAnalyzing ? 'Analyzing...' : 'Refresh Analysis'}
                </Button>
              </div>
            )}

            {migration.error && (
              <div className="text-sm text-red-400 bg-red-500/10 p-3 rounded-lg">{migration.error}</div>
            )}
          </div>
        </div>
      )}

      {/* Storage Tips */}
      <div className="bg-bolt-elements-background-depth-2 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-bolt-elements-textPrimary mb-4">Storage Tips</h3>

        <div className="space-y-3 text-sm text-bolt-elements-textSecondary">
          <div>• Large conversations (&gt;50KB) are kept local to save cloud storage</div>
          <div>• File snapshots and project artifacts remain local for fast access</div>
          <div>• Message summaries are created for responses over 5KB</div>
          <div>• Pro users get unlimited cloud storage for all conversations</div>
        </div>
      </div>
    </div>
  );
}
