import { memo } from 'react';
import { Settings } from 'lucide-react';

interface SettingsButtonProps {
  onClick: () => void;
}

export const SettingsButton = memo(({ onClick }: SettingsButtonProps) => {
  return (
    <button
      onClick={onClick}
      title="Settings"
      data-testid="settings-button"
      className="flex items-center justify-center w-8 h-8 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary hover:bg-bolt-elements-item-backgroundActive/10 transition-colors rounded-lg bg-transparent"
    >
      <Settings size={18} />
    </button>
  );
});
