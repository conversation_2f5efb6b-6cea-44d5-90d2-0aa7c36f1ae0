import { useStore } from '@nanostores/react';
import { memo } from 'react';
import { themeStore, toggleTheme } from '~/lib/stores/theme';
import { Sun, Moon } from 'lucide-react';

interface ThemeSwitchProps {
  className?: string;
}

export const ThemeSwitch = memo(({ className }: ThemeSwitchProps) => {
  const theme = useStore(themeStore);

  return (
    <button
      className={`flex items-center justify-center w-8 h-8 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary hover:bg-bolt-elements-item-backgroundActive/10 transition-colors rounded-lg bg-transparent ${className || ''}`}
      title="Toggle Theme"
      onClick={toggleTheme}
    >
      {theme === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
    </button>
  );
});
