import React from 'react';
import { classNames } from '~/utils/classNames';

interface ModernCheckIconProps {
  /** Size of the check icon */
  size?: 'sm' | 'md' | 'lg';
  /** Color variant */
  variant?: 'success' | 'blue' | 'purple' | 'custom';
  /** Custom background color class */
  bgColor?: string;
  /** Custom check color class */
  checkColor?: string;
  /** Additional class name */
  className?: string;
}

const SIZE_CLASSES = {
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
};

const ICON_SIZE_CLASSES = {
  sm: 'w-2.5 h-2.5',
  md: 'w-3 h-3',
  lg: 'w-3.5 h-3.5',
};

const VARIANT_CLASSES = {
  success: {
    bg: 'bg-green-500',
    check: 'text-white',
  },
  blue: {
    bg: 'bg-blue-500',
    check: 'text-white',
  },
  purple: {
    bg: 'bg-purple-500',
    check: 'text-white',
  },
  custom: {
    bg: '',
    check: '',
  },
};

/**
 * ModernCheckIcon component
 * 
 * A modern check icon with a rounded background
 */
export function ModernCheckIcon({
  size = 'md',
  variant = 'blue',
  bgColor,
  checkColor,
  className,
}: ModernCheckIconProps) {
  const sizeClass = SIZE_CLASSES[size];
  const iconSizeClass = ICON_SIZE_CLASSES[size];
  const variantClasses = VARIANT_CLASSES[variant];

  const backgroundClass = bgColor || variantClasses.bg;
  const checkColorClass = checkColor || variantClasses.check;

  return (
    <div
      className={classNames(
        'flex items-center justify-center rounded-full',
        sizeClass,
        backgroundClass,
        className,
      )}
    >
      <span className={classNames('i-ph:check-bold', iconSizeClass, checkColorClass)} />
    </div>
  );
}
