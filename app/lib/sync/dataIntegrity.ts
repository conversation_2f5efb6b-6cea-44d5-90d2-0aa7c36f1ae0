import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';

/**
 * Enhanced Data Integrity System
 * Ensures consistency between IndexedDB, localStorage, and Supabase
 */

interface DataSnapshot {
  timestamp: number;
  checksum: string;
  source: 'indexeddb' | 'supabase' | 'localstorage';
  data: any;
}

interface SyncConflict {
  field: string;
  localValue: any;
  remoteValue: any;
  lastModified: {
    local: number;
    remote: number;
  };
}

// Generate data checksum for integrity verification
const generateDataChecksum = (data: any): string => {
  const normalized = JSON.stringify(data, Object.keys(data).sort());
  return btoa(normalized).slice(0, 32);
};

// Compare data snapshots and detect conflicts
const detectConflicts = (local: DataSnapshot, remote: DataSnapshot): SyncConflict[] => {
  const conflicts: SyncConflict[] = [];
  
  if (local.checksum !== remote.checksum) {
    // Deep comparison to find specific conflicts
    const localData = local.data;
    const remoteData = remote.data;
    
    for (const key in { ...localData, ...remoteData }) {
      if (JSON.stringify(localData[key]) !== JSON.stringify(remoteData[key])) {
        conflicts.push({
          field: key,
          localValue: localData[key],
          remoteValue: remoteData[key],
          lastModified: {
            local: local.timestamp,
            remote: remote.timestamp,
          },
        });
      }
    }
  }
  
  return conflicts;
};

// Resolve conflicts using last-write-wins strategy
const resolveConflicts = (conflicts: SyncConflict[]): any => {
  const resolved: any = {};
  
  for (const conflict of conflicts) {
    // Use the most recently modified value
    if (conflict.lastModified.local > conflict.lastModified.remote) {
      resolved[conflict.field] = conflict.localValue;
    } else {
      resolved[conflict.field] = conflict.remoteValue;
    }
  }
  
  return resolved;
};

// Sync conversations with conflict resolution
export const syncConversationsWithIntegrity = async (): Promise<{
  success: boolean;
  conflicts: number;
  resolved: number;
  error?: string;
}> => {
  try {
    const user = authState.get().user;
    if (!user) {
      return { success: false, conflicts: 0, resolved: 0, error: 'User not authenticated' };
    }

    console.log('🔄 Starting integrity-aware conversation sync...');

    // Get local data from IndexedDB
    const { openDatabase } = await import('~/lib/persistence/db');
    const db = await openDatabase();
    
    let localConversations: any[] = [];
    if (db) {
      const transaction = db.transaction('chats', 'readonly');
      const store = transaction.objectStore('chats');
      const getAllRequest = store.getAll();

      localConversations = await new Promise<any[]>((resolve, reject) => {
        getAllRequest.onsuccess = () => resolve(getAllRequest.result);
        getAllRequest.onerror = () => reject(getAllRequest.error);
      });
    }

    // Get remote data from Supabase
    const { data: remoteConversations, error } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Failed to fetch remote conversations:', error);
      return { success: false, conflicts: 0, resolved: 0, error: error.message };
    }

    // Create data snapshots
    const localSnapshot: DataSnapshot = {
      timestamp: Date.now(),
      checksum: generateDataChecksum(localConversations),
      source: 'indexeddb',
      data: localConversations,
    };

    const remoteSnapshot: DataSnapshot = {
      timestamp: Date.now(),
      checksum: generateDataChecksum(remoteConversations || []),
      source: 'supabase',
      data: remoteConversations || [],
    };

    // Detect conflicts
    const conflicts = detectConflicts(localSnapshot, remoteSnapshot);
    console.log(`🔍 Detected ${conflicts.length} data conflicts`);

    if (conflicts.length === 0) {
      console.log('✅ No conflicts detected, data is in sync');
      return { success: true, conflicts: 0, resolved: 0 };
    }

    // Resolve conflicts
    const resolvedData = resolveConflicts(conflicts);
    console.log(`🔧 Resolved ${conflicts.length} conflicts`);

    // Apply resolved data to both local and remote
    // This is a simplified approach - in production, you'd want more sophisticated merging
    const mergedConversations = [...(remoteConversations || [])];
    
    // Update IndexedDB with resolved data
    if (db) {
      const transaction = db.transaction('chats', 'readwrite');
      const store = transaction.objectStore('chats');
      
      // Clear existing data
      await store.clear();
      
      // Add merged conversations
      for (const conv of mergedConversations) {
        const chatData = {
          id: conv.metadata?.original_id || conv.id,
          urlId: conv.metadata?.url_id || conv.id,
          description: conv.title,
          messages: [
            {
              id: '1',
              role: 'user',
              content: `Project: ${conv.title}`,
            },
            {
              id: '2',
              role: 'assistant', 
              content: conv.description || 'Project generated successfully',
            },
          ],
          timestamp: conv.created_at,
        };
        
        await store.add(chatData);
      }
    }

    console.log('✅ Data integrity sync completed successfully');
    return { success: true, conflicts: conflicts.length, resolved: conflicts.length };

  } catch (error) {
    console.error('❌ Data integrity sync failed:', error);
    return { 
      success: false, 
      conflicts: 0, 
      resolved: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

// Verify data integrity across all storage systems
export const verifyDataIntegrity = async (): Promise<{
  indexeddb: { healthy: boolean; count: number; checksum: string };
  supabase: { healthy: boolean; count: number; checksum: string };
  localStorage: { healthy: boolean; keys: number; size: number };
  overall: { healthy: boolean; issues: string[] };
}> => {
  const issues: string[] = [];
  
  try {
    const user = authState.get().user;
    if (!user) {
      issues.push('User not authenticated');
      return {
        indexeddb: { healthy: false, count: 0, checksum: '' },
        supabase: { healthy: false, count: 0, checksum: '' },
        localStorage: { healthy: false, keys: 0, size: 0 },
        overall: { healthy: false, issues },
      };
    }

    // Check IndexedDB
    let indexeddbData = { healthy: false, count: 0, checksum: '' };
    try {
      const { openDatabase } = await import('~/lib/persistence/db');
      const db = await openDatabase();
      
      if (db) {
        const transaction = db.transaction('chats', 'readonly');
        const store = transaction.objectStore('chats');
        const getAllRequest = store.getAll();

        const conversations = await new Promise<any[]>((resolve, reject) => {
          getAllRequest.onsuccess = () => resolve(getAllRequest.result);
          getAllRequest.onerror = () => reject(getAllRequest.error);
        });

        indexeddbData = {
          healthy: true,
          count: conversations.length,
          checksum: generateDataChecksum(conversations),
        };
      } else {
        issues.push('IndexedDB not available');
      }
    } catch (error) {
      issues.push(`IndexedDB error: ${error}`);
    }

    // Check Supabase
    let supabaseData = { healthy: false, count: 0, checksum: '' };
    try {
      const { data: conversations, error } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('user_id', user.id);

      if (error) {
        issues.push(`Supabase error: ${error.message}`);
      } else {
        supabaseData = {
          healthy: true,
          count: conversations?.length || 0,
          checksum: generateDataChecksum(conversations || []),
        };
      }
    } catch (error) {
      issues.push(`Supabase connection error: ${error}`);
    }

    // Check localStorage
    let localStorageData = { healthy: false, keys: 0, size: 0 };
    try {
      if (typeof window !== 'undefined') {
        let totalSize = 0;
        let keyCount = 0;
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            keyCount++;
            const value = localStorage.getItem(key);
            totalSize += (key.length + (value?.length || 0)) * 2; // Rough size in bytes
          }
        }
        
        localStorageData = {
          healthy: true,
          keys: keyCount,
          size: totalSize,
        };
      } else {
        issues.push('localStorage not available (server-side)');
      }
    } catch (error) {
      issues.push(`localStorage error: ${error}`);
    }

    // Check for sync issues
    if (indexeddbData.healthy && supabaseData.healthy) {
      if (indexeddbData.checksum !== supabaseData.checksum) {
        issues.push('IndexedDB and Supabase data checksums do not match');
      }
      
      if (Math.abs(indexeddbData.count - supabaseData.count) > 0) {
        issues.push(`Conversation count mismatch: IndexedDB(${indexeddbData.count}) vs Supabase(${supabaseData.count})`);
      }
    }

    const overall = {
      healthy: issues.length === 0,
      issues,
    };

    return {
      indexeddb: indexeddbData,
      supabase: supabaseData,
      localStorage: localStorageData,
      overall,
    };

  } catch (error) {
    issues.push(`Integrity check failed: ${error}`);
    return {
      indexeddb: { healthy: false, count: 0, checksum: '' },
      supabase: { healthy: false, count: 0, checksum: '' },
      localStorage: { healthy: false, keys: 0, size: 0 },
      overall: { healthy: false, issues },
    };
  }
};

// Auto-heal data inconsistencies
export const autoHealDataInconsistencies = async (): Promise<{
  success: boolean;
  actionsPerformed: string[];
  error?: string;
}> => {
  const actionsPerformed: string[] = [];
  
  try {
    console.log('🔧 Starting auto-heal process...');
    
    // First, verify current state
    const integrity = await verifyDataIntegrity();
    
    if (integrity.overall.healthy) {
      console.log('✅ No healing required, data is healthy');
      return { success: true, actionsPerformed: ['No action needed - data is healthy'] };
    }

    // Attempt to sync conversations with conflict resolution
    const syncResult = await syncConversationsWithIntegrity();
    if (syncResult.success) {
      actionsPerformed.push(`Resolved ${syncResult.resolved} data conflicts`);
    }

    // Re-verify after healing
    const postHealIntegrity = await verifyDataIntegrity();
    
    if (postHealIntegrity.overall.healthy) {
      actionsPerformed.push('Data integrity restored successfully');
      console.log('✅ Auto-heal completed successfully');
      return { success: true, actionsPerformed };
    } else {
      actionsPerformed.push('Partial healing completed, manual intervention may be required');
      console.log('⚠️ Auto-heal partially successful');
      return { success: false, actionsPerformed, error: 'Could not fully restore data integrity' };
    }

  } catch (error) {
    console.error('❌ Auto-heal failed:', error);
    return { 
      success: false, 
      actionsPerformed, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};
