import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';
import { atom } from 'nanostores';
import { openDatabase, setMessages, getAll } from '~/lib/persistence/db';
import type { ChatHistoryItem } from '~/lib/persistence/useChatHistory';

/**
 * Real-Time Synchronization System
 * Like Discord, Slack, and other modern platforms
 */

// Real-time sync state
export const syncState = atom({
  isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
  lastSync: Date.now(),
  syncInProgress: false,
  pendingOperations: 0,
});

// Event emitter for real-time updates
class SyncEventEmitter {
  private listeners: Map<string, Function[]> = new Map();

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event: string, data?: any) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }
}

export const syncEvents = new SyncEventEmitter();

// Real-time sync for conversations/projects
export class RealTimeSync {
  private syncInterval: NodeJS.Timeout | null = null;
  private retryTimeout: NodeJS.Timeout | null = null;
  private maxRetries = 3;
  private retryDelay = 1000;

  constructor() {
    this.setupEventListeners();
    this.startPeriodicSync();
  }

  private setupEventListeners() {
    // Listen for online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        console.log('🌐 Connection restored, syncing...');
        syncState.set({ ...syncState.get(), isOnline: true });
        this.syncNow();
      });

      window.addEventListener('offline', () => {
        console.log('🌐 Connection lost');
        syncState.set({ ...syncState.get(), isOnline: false });
      });

      // Listen for visibility changes (tab focus)
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && syncState.get().isOnline) {
          this.syncNow();
        }
      });
    }
  }

  private startPeriodicSync() {
    // Sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (syncState.get().isOnline && !syncState.get().syncInProgress) {
        this.syncNow();
      }
    }, 30000);
  }

  public async syncNow(force = false): Promise<void> {
    const currentState = syncState.get();

    if (currentState.syncInProgress && !force) {
      console.log('🔄 Sync already in progress, skipping...');
      return;
    }

    const user = authState.get().user;
    if (!user) {
      console.log('🔄 No user authenticated, skipping sync');
      return;
    }

    try {
      syncState.set({ ...currentState, syncInProgress: true });
      console.log('🔄 Starting real-time sync...');

      // Sync conversations from Supabase to IndexedDB
      await this.syncConversationsToIndexedDB();

      // Update sync state
      syncState.set({
        ...syncState.get(),
        syncInProgress: false,
        lastSync: Date.now(),
      });

      // Emit sync complete event
      syncEvents.emit('sync:complete');
      console.log('✅ Real-time sync completed');

    } catch (error) {
      console.error('❌ Real-time sync failed:', error);
      syncState.set({ ...syncState.get(), syncInProgress: false });

      // Retry with exponential backoff
      this.scheduleRetry();
    }
  }

  private async syncConversationsToIndexedDB(): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      // Fetch latest conversations from Supabase
      const { data: conversations, error } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })
        .limit(50); // Limit to recent conversations

      if (error) {
        console.error('❌ Failed to fetch conversations:', error);
        return;
      }

      if (!conversations || conversations.length === 0) {
        console.log('📭 No conversations found in Supabase');
        return;
      }

      console.log(`🔄 Syncing ${conversations.length} conversations to IndexedDB...`);

      // Open IndexedDB
      const db = await openDatabase();
      if (!db) {
        console.error('❌ IndexedDB not available');
        return;
      }

      // Get existing conversations from IndexedDB
      const existingConversations = await getAll(db);
      const existingIds = new Set(existingConversations.map(c => c.id));

      let syncedCount = 0;

      // Sync each conversation
      for (const conv of conversations) {
        try {
          // Check if this conversation exists in IndexedDB using multiple ID strategies
          const possibleIds = [
            conv.metadata?.original_id,
            conv.metadata?.url_id,
            conv.id
          ].filter(Boolean);

          const existing = existingConversations.find(e =>
            possibleIds.includes(e.id) || possibleIds.includes(e.urlId)
          );

          // Skip if already exists and hasn't been updated
          if (existing && existing.timestamp >= conv.updated_at) {
            continue;
          }

          // IMPORTANT: Don't re-add if it was recently deleted from IndexedDB
          // This prevents the sync from undoing user deletions
          if (existing && !conv.updated_at) {
            console.log(`⏭️ Skipping sync for ${conv.title} - may have been deleted locally`);
            continue;
          }

          // Create chat data for IndexedDB
          const chatData: ChatHistoryItem = {
            id: conv.metadata?.original_id || conv.id,
            urlId: conv.metadata?.url_id || conv.id,
            description: conv.title,
            messages: [
              {
                id: '1',
                role: 'user',
                content: `Project: ${conv.title}`,
                createdAt: new Date(conv.created_at),
              },
              {
                id: '2',
                role: 'assistant',
                content: conv.description || 'Project generated successfully',
                createdAt: new Date(conv.created_at),
              },
            ],
            timestamp: conv.updated_at || conv.created_at,
            metadata: conv.metadata || {},
          };

          // Save to IndexedDB
          await setMessages(
            db,
            chatData.id,
            chatData.messages,
            chatData.urlId,
            chatData.description,
            chatData.timestamp,
            chatData.metadata
          );

          syncedCount++;
          console.log(`✅ Synced conversation: ${conv.title}`);

        } catch (error) {
          console.error(`❌ Failed to sync conversation ${conv.id}:`, error);
        }
      }

      if (syncedCount > 0) {
        console.log(`🎉 Successfully synced ${syncedCount} conversations`);
        // Emit event to update UI
        syncEvents.emit('conversations:updated', { count: syncedCount });
      }

    } catch (error) {
      console.error('❌ Conversation sync failed:', error);
      throw error;
    }
  }

  private scheduleRetry(): void {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    this.retryTimeout = setTimeout(() => {
      console.log('🔄 Retrying sync...');
      this.syncNow();
    }, this.retryDelay);

    // Exponential backoff
    this.retryDelay = Math.min(this.retryDelay * 2, 30000);
  }

  public destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }
}

// Global sync instance
let globalSync: RealTimeSync | null = null;

// Initialize real-time sync
export const initializeRealTimeSync = (): RealTimeSync => {
  if (!globalSync) {
    globalSync = new RealTimeSync();
    console.log('🚀 Real-time sync initialized');
  }
  return globalSync;
};

// Get current sync instance
export const getRealTimeSync = (): RealTimeSync | null => {
  return globalSync;
};

// Manual sync trigger
export const triggerSync = async (): Promise<void> => {
  if (globalSync) {
    await globalSync.syncNow(true);
  } else {
    console.warn('⚠️ Real-time sync not initialized');
  }
};

// Sync status
export const getSyncStatus = () => {
  return syncState.get();
};

// Subscribe to sync events
export const onSyncEvent = (event: string, callback: Function) => {
  syncEvents.on(event, callback);

  // Return unsubscribe function
  return () => {
    syncEvents.off(event, callback);
  };
};

// Available events:
// - 'sync:complete' - Sync operation completed
// - 'conversations:updated' - Conversations were updated
// - 'sync:error' - Sync operation failed
