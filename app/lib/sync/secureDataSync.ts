import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';
import { encrypt<PERSON>pi<PERSON><PERSON>, decrypt<PERSON><PERSON><PERSON><PERSON> } from '~/lib/security/apiKeyEncryption';
import { checkRateLimit } from '~/lib/security/requestValidation';

/**
 * Secure Data Synchronization System
 * Ensures data integrity between local storage and Supabase with security
 */

interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: number;
  userId: string;
  checksum: string;
}

// Sync queue for offline operations
const syncQueue: SyncOperation[] = [];
let syncInProgress = false;

// Generate checksum for data integrity
const generateChecksum = (data: any): string => {
  const dataString = JSON.stringify(data, Object.keys(data).sort());
  return btoa(dataString).slice(0, 16); // Simple checksum
};

// Verify data integrity
const verifyDataIntegrity = (data: any, expectedChecksum: string): boolean => {
  return generateChecksum(data) === expectedChecksum;
};

// Secure conversation sync
export const syncConversationToSupabase = async (
  conversationId: string,
  title: string,
  messages: any[],
  metadata: any = {}
): Promise<{ success: boolean; error?: string }> => {
  try {
    const user = authState.get().user;
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Rate limiting
    const rateLimit = checkRateLimit(user.id, 'chat');
    if (!rateLimit.allowed) {
      return { success: false, error: 'Rate limit exceeded' };
    }

    // Data validation
    if (!conversationId || !title || !Array.isArray(messages)) {
      return { success: false, error: 'Invalid conversation data' };
    }

    // Generate checksum for integrity
    const conversationData = { title, metadata, messageCount: messages.length };
    const checksum = generateChecksum(conversationData);

    // Upsert conversation
    const { data: conversation, error: convError } = await supabase
      .from('chat_conversations')
      .upsert({
        id: conversationId,
        user_id: user.id,
        title: title.slice(0, 255), // Truncate title
        description: metadata.description?.slice(0, 1000) || null,
        message_count: messages.length,
        metadata: {
          ...metadata,
          checksum,
          lastSync: new Date().toISOString(),
        },
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'id',
      })
      .select()
      .single();

    if (convError) {
      console.error('Conversation sync error:', convError);
      return { success: false, error: 'Failed to sync conversation' };
    }

    // Sync messages in batches to avoid overwhelming the database
    const batchSize = 50;
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize);
      
      const messagesToInsert = batch.map((msg, index) => ({
        id: `${conversationId}_${i + index}`,
        conversation_id: conversationId,
        user_id: user.id,
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content.slice(0, 100000) : JSON.stringify(msg.content).slice(0, 100000),
        metadata: {
          originalIndex: i + index,
          timestamp: msg.timestamp || new Date().toISOString(),
        },
        created_at: new Date().toISOString(),
      }));

      const { error: msgError } = await supabase
        .from('chat_messages')
        .upsert(messagesToInsert, {
          onConflict: 'id',
        });

      if (msgError) {
        console.error('Messages sync error:', msgError);
        return { success: false, error: 'Failed to sync messages' };
      }
    }

    // Update usage tracking
    await updateUsageTracking(user.id, 'message', messages.length);

    return { success: true };

  } catch (error) {
    console.error('Sync error:', error);
    return { success: false, error: 'Sync operation failed' };
  }
};

// Secure API key sync
export const syncApiKeysToSupabase = async (
  apiKeys: Record<string, string>
): Promise<{ success: boolean; error?: string }> => {
  try {
    const user = authState.get().user;
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Rate limiting
    const rateLimit = checkRateLimit(user.id, 'apiKey');
    if (!rateLimit.allowed) {
      return { success: false, error: 'Rate limit exceeded' };
    }

    const sessionId = user.id + Date.now(); // Simple session ID

    // Encrypt and store API keys
    const encryptedKeys = [];
    for (const [provider, apiKey] of Object.entries(apiKeys)) {
      if (apiKey && apiKey.trim()) {
        const encryptedKey = encryptApiKey(apiKey, user.id, sessionId);
        
        encryptedKeys.push({
          user_id: user.id,
          provider,
          encrypted_key: encryptedKey,
          key_hash: generateChecksum(apiKey),
          session_id: sessionId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }
    }

    if (encryptedKeys.length > 0) {
      // Delete existing keys first
      await supabase
        .from('user_api_keys')
        .delete()
        .eq('user_id', user.id);

      // Insert new encrypted keys
      const { error } = await supabase
        .from('user_api_keys')
        .insert(encryptedKeys);

      if (error) {
        console.error('API keys sync error:', error);
        return { success: false, error: 'Failed to sync API keys' };
      }
    }

    return { success: true };

  } catch (error) {
    console.error('API key sync error:', error);
    return { success: false, error: 'API key sync failed' };
  }
};

// Load API keys from Supabase
export const loadApiKeysFromSupabase = async (): Promise<Record<string, string>> => {
  try {
    const user = authState.get().user;
    if (!user) return {};

    const { data: encryptedKeys, error } = await supabase
      .from('user_api_keys')
      .select('provider, encrypted_key, session_id')
      .eq('user_id', user.id);

    if (error || !encryptedKeys) {
      console.error('Failed to load API keys:', error);
      return {};
    }

    const apiKeys: Record<string, string> = {};
    
    for (const keyData of encryptedKeys) {
      try {
        const decryptedKey = decryptApiKey(
          keyData.encrypted_key,
          user.id,
          keyData.session_id
        );
        
        if (decryptedKey) {
          apiKeys[keyData.provider] = decryptedKey;
        }
      } catch (error) {
        console.warn(`Failed to decrypt key for ${keyData.provider}:`, error);
      }
    }

    return apiKeys;

  } catch (error) {
    console.error('Load API keys error:', error);
    return {};
  }
};

// Update usage tracking securely
const updateUsageTracking = async (
  userId: string,
  type: 'message' | 'project' | 'api_call',
  count: number = 1
): Promise<void> => {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    const { data: existing } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('date', today)
      .single();

    const updateData = {
      user_id: userId,
      date: today,
      messages_sent: existing?.messages_sent || 0,
      tokens_used: existing?.tokens_used || 0,
      api_calls_made: existing?.api_calls_made || 0,
      projects_created: existing?.projects_created || 0,
      subscription_tier: 'free',
      updated_at: new Date().toISOString(),
    };

    // Update specific counter
    switch (type) {
      case 'message':
        updateData.messages_sent += count;
        updateData.api_calls_made += count;
        break;
      case 'project':
        updateData.projects_created += count;
        break;
      case 'api_call':
        updateData.api_calls_made += count;
        break;
    }

    await supabase
      .from('usage_tracking')
      .upsert(updateData, {
        onConflict: 'user_id,date',
      });

  } catch (error) {
    console.error('Usage tracking update failed:', error);
  }
};

// Sync offline operations when connection restored
export const syncOfflineOperations = async (): Promise<void> => {
  if (syncInProgress || syncQueue.length === 0) return;

  syncInProgress = true;
  
  try {
    const user = authState.get().user;
    if (!user) return;

    console.log(`🔄 Syncing ${syncQueue.length} offline operations...`);

    while (syncQueue.length > 0) {
      const operation = syncQueue.shift();
      if (!operation) continue;

      // Verify operation integrity
      if (!verifyDataIntegrity(operation.data, operation.checksum)) {
        console.warn('Data integrity check failed for operation:', operation.id);
        continue;
      }

      // Execute operation based on type
      try {
        switch (operation.type) {
          case 'create':
          case 'update':
            await supabase
              .from(operation.table)
              .upsert(operation.data);
            break;
          case 'delete':
            await supabase
              .from(operation.table)
              .delete()
              .eq('id', operation.data.id);
            break;
        }
        
        console.log(`✅ Synced operation: ${operation.id}`);
      } catch (error) {
        console.error(`❌ Failed to sync operation ${operation.id}:`, error);
        // Re-queue failed operation (with retry limit)
        if (!operation.data._retryCount || operation.data._retryCount < 3) {
          operation.data._retryCount = (operation.data._retryCount || 0) + 1;
          syncQueue.push(operation);
        }
      }
    }

  } finally {
    syncInProgress = false;
  }
};

// Add operation to sync queue for offline handling
export const queueSyncOperation = (
  type: SyncOperation['type'],
  table: string,
  data: any
): void => {
  const user = authState.get().user;
  if (!user) return;

  const operation: SyncOperation = {
    id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    table,
    data,
    timestamp: Date.now(),
    userId: user.id,
    checksum: generateChecksum(data),
  };

  syncQueue.push(operation);
  
  // Try to sync immediately if online
  if (navigator.onLine) {
    setTimeout(syncOfflineOperations, 1000);
  }
};

// Monitor connection and sync when restored
if (typeof window !== 'undefined') {
  window.addEventListener('online', syncOfflineOperations);
}
