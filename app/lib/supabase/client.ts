import { createClient } from '@supabase/supabase-js';

// Get environment variables with Cloudflare Workers compatibility
function getEnvVar(key: string, cloudflareEnv?: Record<string, any>): string | undefined {
  // Client-side: try window.ENV first, then import.meta.env
  if (typeof window !== 'undefined') {
    return (window as any).ENV?.[key] || import.meta.env[key];
  }

  // Server-side: try Cloudflare env first, then process.env, then globalThis
  return cloudflareEnv?.[key] || process.env[key] || (globalThis as any)[key];
}

// Create Supabase client factory that can handle Cloudflare environment
export function createSupabaseClient(cloudflareEnv?: Record<string, any>) {
  const supabaseUrl = getEnvVar('VITE_SUPABASE_URL', cloudflareEnv);
  const supabaseAnonKey = getEnvVar('VITE_SUPABASE_ANON_KEY', cloudflareEnv);

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase URL:', supabaseUrl);
    console.error('Supabase Anon Key:', supabaseAnonKey ? '[REDACTED]' : 'undefined');
    console.error('Environment check:', {
      isWindow: typeof window !== 'undefined',
      hasProcessEnv: typeof process !== 'undefined' && !!process.env,
      hasGlobalThis: typeof globalThis !== 'undefined',
      hasCloudflareEnv: !!cloudflareEnv,
      cloudflareEnvKeys: cloudflareEnv ? Object.keys(cloudflareEnv) : [],
    });

    // In server environments without proper env vars, return a mock client to prevent crashes
    if (typeof window === 'undefined') {
      console.warn('Creating mock Supabase client due to missing environment variables');
      return {
        auth: {
          getSession: () => Promise.resolve({ data: { session: null }, error: null }),
          signInWithPassword: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
          signUp: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
          exchangeCodeForSession: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
          signInWithOAuth: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
          signOut: () => Promise.resolve({ error: null }),
          onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })
        }
      } as any;
    }

    throw new Error('Missing Supabase environment variables. Please check your environment configuration.');
  }

  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      storageKey: 'genvibe-auth-token', // Custom storage key
      storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    },
  });
}

// Default client for client-side usage (fallback to process.env)
export const supabase = createSupabaseClient();

// Auth helper functions
export const auth = {
  signUp: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });
    return { data, error };
  },

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });
    return { data, error };
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  getUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    return { session, error };
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  },
};

export default supabase;
