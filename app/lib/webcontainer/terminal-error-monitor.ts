/**
 * Real-time terminal error monitoring
 * Monitors terminal output for errors and triggers alerts
 */

export interface TerminalError {
  type: string;
  description: string;
  output: string;
  timestamp: number;
}

export class TerminalErrorMonitor {
  private outputBuffer: string = '';
  private errorCallback?: (error: TerminalError) => void;
  private isMonitoring: boolean = false;
  private bufferSize: number = 10000; // Keep last 10KB of output

  constructor(onError?: (error: TerminalError) => void) {
    this.errorCallback = onError;
  }

  /**
   * Start monitoring terminal output
   */
  startMonitoring(onError?: (error: TerminalError) => void) {
    this.isMonitoring = true;
    if (onError) {
      this.errorCallback = onError;
    }
    console.log('[Terminal Monitor] Started monitoring terminal output for errors');
  }

  /**
   * Stop monitoring terminal output
   */
  stopMonitoring() {
    this.isMonitoring = false;
    this.outputBuffer = '';
    console.log('[Terminal Monitor] Stopped monitoring terminal output');
  }

  /**
   * Process new terminal output
   */
  processOutput(data: string) {
    if (!this.isMonitoring) return;

    // Add to buffer
    this.outputBuffer += data;

    // Trim buffer if too large
    if (this.outputBuffer.length > this.bufferSize) {
      this.outputBuffer = this.outputBuffer.slice(-this.bufferSize);
    }

    // Check for errors in the new data
    const error = this.detectErrors(data);
    if (error && this.errorCallback) {
      this.errorCallback({
        ...error,
        output: this.outputBuffer,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Comprehensive error detection
   */
  private detectErrors(output: string): { type: string; description: string } | null {
    const lowerOutput = output.toLowerCase();
    const originalOutput = output;

    // Import/Module Resolution Errors
    if (
      originalOutput.includes('Failed to resolve import') ||
      originalOutput.includes('Cannot resolve module') ||
      originalOutput.includes('Module not found') ||
      originalOutput.includes('Cannot find module') ||
      lowerOutput.includes('modulenotfounderror') ||
      originalOutput.includes('ERR_MODULE_NOT_FOUND') ||
      originalOutput.includes('Does the file exist?')
    ) {
      return {
        description: 'Missing dependencies or import errors detected',
        type: 'import'
      };
    }

    // Package/Dependency Errors
    if (
      lowerOutput.includes('npm err!') ||
      lowerOutput.includes('yarn error') ||
      lowerOutput.includes('pnpm err') ||
      originalOutput.includes('Package not found') ||
      originalOutput.includes('dependency not found') ||
      lowerOutput.includes('enoent') ||
      lowerOutput.includes('enotdir')
    ) {
      return {
        description: 'Package installation or dependency errors detected',
        type: 'dependency'
      };
    }

    // Build/Compilation Errors
    if (
      originalOutput.includes('Build failed') ||
      originalOutput.includes('Compilation failed') ||
      originalOutput.includes('TypeScript error') ||
      originalOutput.includes('Syntax error') ||
      lowerOutput.includes('syntaxerror') ||
      (originalOutput.includes('[plugin:') && lowerOutput.includes('error'))
    ) {
      return {
        description: 'Build or compilation errors detected',
        type: 'build'
      };
    }

    // Vite-specific Errors
    if (
      (originalOutput.includes('[vite]') && lowerOutput.includes('error')) ||
      originalOutput.includes('Pre-transform error') ||
      originalOutput.includes('Transform failed') ||
      originalOutput.includes('[plugin:vite:') ||
      originalOutput.includes('Internal server error') ||
      originalOutput.includes('[plugin:@tailwindcss/vite:generate:serve]')
    ) {
      return {
        description: 'Vite development server errors detected',
        type: 'vite'
      };
    }

    // React/JSX Errors
    if (
      (originalOutput.includes('JSX element') && lowerOutput.includes('error')) ||
      originalOutput.includes('React is not defined') ||
      originalOutput.includes('Invalid hook call') ||
      lowerOutput.includes('react error')
    ) {
      return {
        description: 'React or JSX errors detected',
        type: 'react'
      };
    }

    // Network/Port Errors
    if (
      lowerOutput.includes('eaddrinuse') ||
      lowerOutput.includes('port already in use') ||
      lowerOutput.includes('address already in use') ||
      lowerOutput.includes('econnrefused')
    ) {
      return {
        description: 'Network or port configuration errors detected',
        type: 'network'
      };
    }

    // Permission Errors
    if (
      lowerOutput.includes('eacces') ||
      lowerOutput.includes('permission denied') ||
      lowerOutput.includes('eperm')
    ) {
      return {
        description: 'Permission or access errors detected',
        type: 'permission'
      };
    }

    // Generic Error Patterns
    if (
      lowerOutput.includes('error:') ||
      lowerOutput.includes('failed:') ||
      lowerOutput.includes('exception:') ||
      originalOutput.includes('✘ [ERROR]') ||
      originalOutput.includes('× Error') ||
      (lowerOutput.includes('error') && (lowerOutput.includes('failed') || lowerOutput.includes('cannot')))
    ) {
      return {
        description: 'Terminal errors detected',
        type: 'general'
      };
    }

    return null;
  }

  /**
   * Get current output buffer
   */
  getOutputBuffer(): string {
    return this.outputBuffer;
  }

  /**
   * Clear output buffer
   */
  clearBuffer() {
    this.outputBuffer = '';
  }
}

// Global instance for terminal monitoring
export const terminalErrorMonitor = new TerminalErrorMonitor();
