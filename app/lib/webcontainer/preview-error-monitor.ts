/**
 * Preview Error Monitor Script
 * This script is injected into preview iframes to monitor for errors
 * and send them back to the parent window for handling.
 */

export const PREVIEW_ERROR_MONITOR_SCRIPT = `
(function() {
  'use strict';

  // Prevent multiple injections
  if (window.__previewErrorMonitorInjected) {
    return;
  }
  window.__previewErrorMonitorInjected = true;

  console.log('[Preview Monitor] Error monitoring initialized');

  // Store original console methods
  const originalConsole = {
    error: console.error,
    warn: console.warn,
    log: console.log
  };

  // Function to send error to parent
  function sendErrorToParent(error, errorType, stack) {
    try {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          type: 'preview-error',
          error: error,
          errorType: errorType,
          stack: stack,
          url: window.location.href,
          timestamp: Date.now()
        }, '*');
      }
    } catch (e) {
      originalConsole.error('[Preview Monitor] Failed to send error to parent:', e);
    }
  }

  // Function to send ready signal
  function sendReadyToParent() {
    try {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          type: 'preview-ready',
          url: window.location.href,
          timestamp: Date.now()
        }, '*');
      }
    } catch (e) {
      originalConsole.error('[Preview Monitor] Failed to send ready signal to parent:', e);
    }
  }

  // Override console.error to catch logged errors
  console.error = function(...args) {
    originalConsole.error.apply(console, args);

    const errorMessage = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    sendErrorToParent(errorMessage, 'runtime', new Error().stack);
  };

  // Override console.warn for warnings
  console.warn = function(...args) {
    originalConsole.warn.apply(console, args);

    const warnMessage = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    // Only send critical warnings as errors
    if (warnMessage.toLowerCase().includes('error') ||
        warnMessage.toLowerCase().includes('failed') ||
        warnMessage.toLowerCase().includes('cannot')) {
      sendErrorToParent(warnMessage, 'runtime', new Error().stack);
    }
  };

  // Monitor for Vite error overlay (build-time errors)
  function checkForViteErrors() {
    console.log('[Preview Monitor] Checking for Vite errors...');

    // Check for Vite error overlay
    const viteErrorOverlay = document.querySelector('vite-error-overlay');
    if (viteErrorOverlay) {
      console.log('[Preview Monitor] Found vite-error-overlay element');
      const errorContent = viteErrorOverlay.shadowRoot?.textContent || viteErrorOverlay.textContent || 'Vite build error detected';
      sendErrorToParent(errorContent, 'build', 'Vite error overlay');
      return true;
    }

    // Check for error elements in the DOM
    const errorElements = document.querySelectorAll('[data-vite-error], .error-overlay, #error-overlay');
    if (errorElements.length > 0) {
      console.log('[Preview Monitor] Found error elements:', errorElements.length);
      const errorText = Array.from(errorElements).map(el => el.textContent).join('\\n');
      sendErrorToParent(errorText, 'build', 'DOM error element');
      return true;
    }

    // Check for common error text patterns in the DOM
    const bodyText = document.body?.textContent || '';
    if (bodyText.includes('[plugin:@tailwindcss/vite:generate:serve]') ||
        bodyText.includes('Cannot apply unknown utility class') ||
        bodyText.includes('Failed to resolve import') ||
        bodyText.includes('Internal server error')) {
      console.log('[Preview Monitor] Found build error in DOM text');
      sendErrorToParent(bodyText.substring(0, 500), 'build', 'Build error in DOM');
      return true;
    }

    return false;
  }

  // Monitor for DOM changes that might indicate errors
  let errorCheckInterval;
  function startErrorMonitoring() {
    // Check immediately
    checkForViteErrors();

    // Set up periodic checking
    errorCheckInterval = setInterval(checkForViteErrors, 1000);

    // Also check when DOM changes
    if (window.MutationObserver) {
      const observer = new MutationObserver((mutations) => {
        let shouldCheck = false;
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any added nodes contain error-related content
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                if (element.tagName === 'VITE-ERROR-OVERLAY' ||
                    element.className?.includes('error') ||
                    element.id?.includes('error')) {
                  shouldCheck = true;
                }
              }
            });
          }
        });

        if (shouldCheck) {
          setTimeout(checkForViteErrors, 100);
        }
      });

      observer.observe(document.body || document.documentElement, {
        childList: true,
        subtree: true
      });
    }
  }

  // Global error handler for uncaught exceptions
  window.addEventListener('error', function(event) {
    const error = event.error || event.message || 'Unknown error';
    const stack = event.error ? event.error.stack : event.filename + ':' + event.lineno + ':' + event.colno;

    sendErrorToParent(
      error.toString(),
      'runtime',
      stack
    );
  });

  // Global handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    const error = event.reason || 'Unhandled promise rejection';
    const stack = event.reason && event.reason.stack ? event.reason.stack : 'No stack trace available';

    sendErrorToParent(
      error.toString(),
      'runtime',
      stack
    );
  });

  // Monitor for network errors by intercepting fetch
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    return originalFetch.apply(this, args)
      .then(response => {
        if (!response.ok) {
          sendErrorToParent(
            \`Network error: \${response.status} \${response.statusText} for \${args[0]}\`,
            'network',
            new Error().stack
          );
        }
        return response;
      })
      .catch(error => {
        sendErrorToParent(
          \`Fetch error: \${error.message} for \${args[0]}\`,
          'network',
          error.stack
        );
        throw error;
      });
  };

  // Monitor for XMLHttpRequest errors
  const originalXHROpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(...args) {
    this.addEventListener('error', function() {
      sendErrorToParent(
        \`XMLHttpRequest error for \${args[1]}\`,
        'network',
        new Error().stack
      );
    });

    this.addEventListener('load', function() {
      if (this.status >= 400) {
        sendErrorToParent(
          \`XMLHttpRequest error: \${this.status} \${this.statusText} for \${args[1]}\`,
          'network',
          new Error().stack
        );
      }
    });

    return originalXHROpen.apply(this, args);
  };

  // Send ready signal when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      sendReadyToParent();
      startErrorMonitoring();
    });
  } else {
    // DOM is already loaded
    setTimeout(() => {
      sendReadyToParent();
      startErrorMonitoring();
    }, 100);
  }

  // Also send ready signal when window is fully loaded
  if (document.readyState === 'complete') {
    setTimeout(() => {
      sendReadyToParent();
      startErrorMonitoring();
    }, 100);
  } else {
    window.addEventListener('load', () => {
      sendReadyToParent();
      startErrorMonitoring();
    });
  }

  console.log('[Preview Monitor] Error monitoring setup complete');
})();
`;

/**
 * Injects the error monitoring script into an iframe
 */
export function injectErrorMonitor(iframe: HTMLIFrameElement) {
  try {
    if (!iframe.contentWindow || !iframe.contentDocument) {
      console.warn('[Preview] Cannot inject error monitor - iframe not ready');
      return;
    }

    // Create script element
    const script = iframe.contentDocument.createElement('script');
    script.textContent = PREVIEW_ERROR_MONITOR_SCRIPT;
    script.type = 'text/javascript';

    // Inject into head or body
    const target = iframe.contentDocument.head || iframe.contentDocument.body;
    if (target) {
      target.appendChild(script);
      console.log('[Preview] Error monitor injected successfully');
    } else {
      console.warn('[Preview] Cannot inject error monitor - no head or body found');
    }
  } catch (error) {
    console.error('[Preview] Failed to inject error monitor:', error);
  }
}

/**
 * Attempts to inject error monitor with retry logic
 */
export function injectErrorMonitorWithRetry(iframe: HTMLIFrameElement, maxRetries = 3) {
  let retries = 0;

  const tryInject = () => {
    try {
      injectErrorMonitor(iframe);
    } catch (error) {
      retries++;
      if (retries < maxRetries) {
        console.log(`[Preview] Retrying error monitor injection (${retries}/${maxRetries})`);
        setTimeout(tryInject, 500);
      } else {
        console.error('[Preview] Failed to inject error monitor after retries:', error);
      }
    }
  };

  // Try immediate injection
  tryInject();
}
