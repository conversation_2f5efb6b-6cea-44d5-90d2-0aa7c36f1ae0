import { WebContainer } from '@webcontainer/api';
import { WORK_DIR_NAME } from '~/utils/constants';
import { cleanStackTrace } from '~/utils/stacktrace';

interface WebContainerContext {
  loaded: boolean;
}

export const webcontainerContext: WebContainerContext = import.meta.hot?.data.webcontainerContext ?? {
  loaded: false,
};

if (import.meta.hot) {
  import.meta.hot.data.webcontainerContext = webcontainerContext;
}

export let webcontainer: Promise<WebContainer> = new Promise(() => {
  // noop for ssr
});

if (!import.meta.env.SSR) {
  webcontainer =
    import.meta.hot?.data.webcontainer ??
    Promise.resolve()
      .then(() => {
        return WebContainer.boot({
          coep: 'credentialless',
          workdirName: WORK_DIR_NAME,
          forwardPreviewErrors: true, // Enable error forwarding from iframes
        });
      })
      .then(async (webcontainer) => {
        webcontainerContext.loaded = true;

        const { workbenchStore } = await import('~/lib/stores/workbench');

        // Listen for preview errors
        webcontainer.on('preview-message', (message) => {
          console.log('WebContainer preview message:', message);

          // Handle both uncaught exceptions and unhandled promise rejections
          if (message.type === 'PREVIEW_UNCAUGHT_EXCEPTION' || message.type === 'PREVIEW_UNHANDLED_REJECTION') {
            const isPromise = message.type === 'PREVIEW_UNHANDLED_REJECTION';
            const title = isPromise ? 'Unhandled Promise Rejection' : 'Uncaught Exception';

            // Enhanced error information
            const errorDetails = {
              type: message.type,
              message: 'message' in message ? message.message : 'Unknown error',
              pathname: message.pathname || 'Unknown path',
              port: message.port || 'Unknown port',
              stack: cleanStackTrace(message.stack || ''),
              timestamp: new Date().toISOString(),
            };

            // Get error-specific suggestions
            const suggestions = getWebContainerErrorSuggestions(errorDetails.message, message.type);

            workbenchStore.actionAlert.set({
              type: 'preview',
              title,
              description: errorDetails.message,
              content: `Error Type: ${errorDetails.type}
Location: ${errorDetails.pathname}${message.search || ''}${message.hash || ''}
Port: ${errorDetails.port}
Time: ${new Date().toLocaleTimeString()}

Stack trace:
${errorDetails.stack}

Suggested fixes:
${suggestions}`,
              source: 'preview',
            });
          }
        });

        // Helper function for WebContainer-specific error suggestions
        function getWebContainerErrorSuggestions(error: string, errorType: string): string {
          const suggestions = [];

          if (errorType === 'PREVIEW_UNCAUGHT_EXCEPTION') {
            if (error.includes('SyntaxError')) {
              suggestions.push('- Check for syntax errors in your JavaScript/TypeScript code');
              suggestions.push('- Verify all brackets, parentheses, and quotes are properly closed');
              suggestions.push('- Check for missing semicolons or commas');
            } else if (error.includes('ReferenceError')) {
              suggestions.push('- Check if all variables and functions are properly declared');
              suggestions.push('- Verify import statements are correct');
              suggestions.push('- Check for typos in variable or function names');
            } else if (error.includes('TypeError')) {
              suggestions.push('- Check if you\'re calling methods on undefined/null objects');
              suggestions.push('- Verify object properties exist before accessing them');
              suggestions.push('- Check function parameter types');
            }
          } else if (errorType === 'PREVIEW_UNHANDLED_REJECTION') {
            suggestions.push('- Add proper error handling to your async functions');
            suggestions.push('- Use try-catch blocks around async operations');
            suggestions.push('- Check if all promises are properly awaited');
            suggestions.push('- Verify API endpoints and network requests');
          }

          if (error.includes('Module not found') || error.includes('Cannot resolve')) {
            suggestions.push('- Run `npm install` to install missing dependencies');
            suggestions.push('- Check if the module name is spelled correctly');
            suggestions.push('- Verify the module exists in package.json');
          }

          if (suggestions.length === 0) {
            suggestions.push('- Check the browser console for more details');
            suggestions.push('- Try refreshing the preview');
            suggestions.push('- Restart the development server');
          }

          return suggestions.join('\n');
        }

        return webcontainer;
      });

  if (import.meta.hot) {
    import.meta.hot.data.webcontainer = webcontainer;
  }
}
