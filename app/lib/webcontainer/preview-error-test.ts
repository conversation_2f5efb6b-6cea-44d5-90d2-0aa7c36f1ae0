/**
 * Test utilities for preview error handling
 * This file contains functions to test the error detection and auto-fix system
 */

export interface ErrorTestCase {
  name: string;
  description: string;
  errorType: 'syntax' | 'reference' | 'type' | 'network' | 'timeout' | 'module';
  errorMessage: string;
  expectedSuggestions: string[];
  testCode?: string;
}

export const ERROR_TEST_CASES: ErrorTestCase[] = [
  {
    name: 'Tailwind CSS Build Error',
    description: 'Unknown utility class in Tailwind CSS',
    errorType: 'syntax',
    errorMessage: '[plugin:@tailwindcss/vite:generate:serve] Cannot apply unknown utility class: border-border',
    expectedSuggestions: [
      'Check Tailwind CSS class names for typos',
      'Verify the class exists in your Tailwind configuration',
      'Make sure Tailwind CSS is properly installed',
    ],
    testCode: `
<div className="border-border p-4">
  <!-- border-border is not a valid Tailwind class -->
</div>
`,
  },
  {
    name: 'Syntax Error',
    description: 'Missing closing bracket',
    errorType: 'syntax',
    errorMessage: 'SyntaxError: Unexpected end of input',
    expectedSuggestions: [
      'Check for syntax errors in your JavaScript/TypeScript code',
      'Verify all brackets, parentheses, and quotes are properly closed',
    ],
    testCode: `
function test() {
  console.log("Hello World"
  // Missing closing bracket
`,
  },
  {
    name: 'Reference Error',
    description: 'Undefined variable',
    errorType: 'reference',
    errorMessage: 'ReferenceError: undefinedVariable is not defined',
    expectedSuggestions: [
      'Check if all variables and functions are properly declared',
      'Verify import statements are correct',
    ],
    testCode: `
function test() {
  console.log(undefinedVariable);
}
`,
  },
  {
    name: 'Type Error',
    description: 'Calling method on null',
    errorType: 'type',
    errorMessage: 'TypeError: Cannot read properties of null (reading \'method\')',
    expectedSuggestions: [
      'Check if you\'re calling methods on undefined/null objects',
      'Verify object properties exist before accessing them',
    ],
    testCode: `
const obj = null;
obj.method(); // Will throw TypeError
`,
  },
  {
    name: 'Module Not Found',
    description: 'Missing dependency',
    errorType: 'module',
    errorMessage: 'Module not found: Can\'t resolve \'missing-package\'',
    expectedSuggestions: [
      'Run `npm install` to install missing dependencies',
      'Check if the module name is spelled correctly',
    ],
    testCode: `
import { something } from 'missing-package';
`,
  },
  {
    name: 'Network Error',
    description: 'Failed to fetch resource',
    errorType: 'network',
    errorMessage: 'Network error: 404 Not Found for /api/missing-endpoint',
    expectedSuggestions: [
      'Check if the development server is running',
      'Verify the port is not blocked by firewall',
    ],
    testCode: `
fetch('/api/missing-endpoint')
  .then(response => response.json())
  .then(data => console.log(data));
`,
  },
  {
    name: 'Timeout Error',
    description: 'Server unresponsive',
    errorType: 'timeout',
    errorMessage: 'Preview load timeout - server may be unresponsive',
    expectedSuggestions: [
      'Check if the development server is running',
      'Try refreshing the preview',
    ],
  },
];

/**
 * Simulates an error in the preview iframe for testing
 */
export function simulatePreviewError(errorCase: ErrorTestCase): void {
  console.log(`🧪 Simulating error: ${errorCase.name}`);

  // Determine error type based on the error message
  let errorType = 'runtime';
  if (errorCase.errorMessage.includes('[plugin:@tailwindcss/vite:generate:serve]') ||
      errorCase.errorMessage.includes('Cannot apply unknown utility class') ||
      errorCase.errorMessage.includes('Failed to resolve import')) {
    errorType = 'build';
  } else if (errorCase.errorMessage.includes('net::ERR_') ||
             errorCase.errorMessage.includes('Network error')) {
    errorType = 'network';
  } else if (errorCase.errorMessage.includes('timeout')) {
    errorType = 'timeout';
  }

  // Send error message to parent window (simulating iframe error)
  if (window.parent && window.parent !== window) {
    window.parent.postMessage({
      type: 'preview-error',
      error: errorCase.errorMessage,
      errorType: errorType,
      stack: `Error: ${errorCase.errorMessage}\n    at test (test.js:1:1)`,
      url: window.location.href,
      timestamp: Date.now(),
    }, '*');
  }
}

/**
 * Specifically tests the Tailwind CSS build error detection
 */
export function testTailwindError(): void {
  console.log('🧪 Testing Tailwind CSS build error detection...');

  const tailwindError = ERROR_TEST_CASES.find(tc => tc.name === 'Tailwind CSS Build Error');
  if (tailwindError) {
    simulatePreviewError(tailwindError);
  }
}

/**
 * Tests the error detection system
 */
export function testErrorDetection(): void {
  console.log('🧪 Testing error detection system...');

  ERROR_TEST_CASES.forEach((testCase, index) => {
    setTimeout(() => {
      simulatePreviewError(testCase);
    }, index * 2000); // Stagger tests by 2 seconds
  });
}

/**
 * Validates that error suggestions are working correctly
 */
export function validateErrorSuggestions(error: string, suggestions: string[]): boolean {
  const lowerError = error.toLowerCase();

  // Check if suggestions are relevant to the error type
  if (lowerError.includes('syntax')) {
    return suggestions.some(s => s.toLowerCase().includes('syntax') || s.toLowerCase().includes('bracket'));
  }

  if (lowerError.includes('reference')) {
    return suggestions.some(s => s.toLowerCase().includes('variable') || s.toLowerCase().includes('declared'));
  }

  if (lowerError.includes('type')) {
    return suggestions.some(s => s.toLowerCase().includes('null') || s.toLowerCase().includes('undefined'));
  }

  if (lowerError.includes('module') || lowerError.includes('resolve')) {
    return suggestions.some(s => s.toLowerCase().includes('install') || s.toLowerCase().includes('dependency'));
  }

  if (lowerError.includes('network') || lowerError.includes('404')) {
    return suggestions.some(s => s.toLowerCase().includes('server') || s.toLowerCase().includes('port'));
  }

  return suggestions.length > 0; // At least some suggestions provided
}

/**
 * Creates a test HTML page that will trigger errors
 */
export function createErrorTestPage(): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Test Page</title>
</head>
<body>
    <h1>Preview Error Testing</h1>
    <div id="content">
        <p>This page will test various error scenarios.</p>
        <button onclick="testSyntaxError()">Test Syntax Error</button>
        <button onclick="testReferenceError()">Test Reference Error</button>
        <button onclick="testTypeError()">Test Type Error</button>
        <button onclick="testNetworkError()">Test Network Error</button>
    </div>

    <script>
        // Test functions that will trigger errors
        function testSyntaxError() {
            eval('function broken() { console.log("missing bracket"'); // Syntax error
        }

        function testReferenceError() {
            console.log(undefinedVariable); // Reference error
        }

        function testTypeError() {
            const obj = null;
            obj.method(); // Type error
        }

        function testNetworkError() {
            fetch('/nonexistent-endpoint')
                .then(response => response.json())
                .catch(error => console.error('Network error:', error));
        }

        // Auto-run tests after page load
        window.addEventListener('load', () => {
            console.log('Error test page loaded');

            // Test each error type with delays
            setTimeout(testSyntaxError, 1000);
            setTimeout(testReferenceError, 3000);
            setTimeout(testTypeError, 5000);
            setTimeout(testNetworkError, 7000);
        });
    </script>
</body>
</html>
  `;
}

// Export for use in development/testing
if (typeof window !== 'undefined') {
  (window as any).previewErrorTest = {
    simulatePreviewError,
    testErrorDetection,
    testTailwindError,
    validateErrorSuggestions,
    createErrorTestPage,
    ERROR_TEST_CASES,
  };
}
