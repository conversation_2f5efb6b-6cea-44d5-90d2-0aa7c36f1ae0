import type { Message } from 'ai';
import type { IChatMetadata } from '~/lib/persistence/db';

/**
 * Simple Chat Loader - IndexedDB only (Supabase integration removed)
 * This ensures stable chat loading without external dependencies
 */

export interface LoadedChat {
  messages: Message[];
  description: string;
  metadata?: IChatMetadata;
  urlId: string;
  id: string;
}

/**
 * Load chat messages from IndexedDB only
 */
export async function loadChatMessages(chatId: string): Promise<LoadedChat | null> {
  console.log('🔄 INDEXEDDB LOAD: Loading chat messages for:', chatId);

  try {
    const indexedDBChat = await loadFromIndexedDB(chatId);
    if (indexedDBChat) {
      console.log('✅ INDEXEDDB LOAD: Loaded successfully:', chatId);
      return indexedDBChat;
    }

    console.log('ℹ️ INDEXEDDB LOAD: No chat found:', chatId);
    return null;

  } catch (error) {
    console.error('❌ INDEXEDDB LOAD: Failed to load chat:', error);
    return null;
  }
}



/**
 * Load chat from IndexedDB
 */
async function loadFromIndexedDB(chatId: string): Promise<LoadedChat | null> {
  try {
    console.log('🔍 IndexedDB: Attempting to load chat:', chatId);

    // Import IndexedDB functions dynamically to avoid issues if not available
    const { openDatabase, getMessages } = await import('~/lib/persistence/db');

    const db = await openDatabase();
    if (!db) {
      console.log('❌ IndexedDB: Database not available');
      return null;
    }

    const chatData = await getMessages(db, chatId);
    console.log('🔍 IndexedDB: Chat data result:', {
      found: !!chatData,
      hasMessages: chatData?.messages?.length > 0,
      messageCount: chatData?.messages?.length || 0,
      description: chatData?.description
    });

    if (!chatData || !chatData.messages || chatData.messages.length === 0) {
      console.log('❌ IndexedDB: No valid chat data found');
      return null;
    }

    console.log('✅ IndexedDB: Successfully loaded chat data');
    return {
      id: chatData.id,
      urlId: chatData.urlId || chatData.id,
      description: chatData.description || 'Untitled',
      messages: chatData.messages,
      metadata: chatData.metadata,
    };

  } catch (error) {
    console.error('❌ Error loading from IndexedDB:', error);
    return null;
  }
}

/**
 * Load chat snapshot (files and workbench state) from IndexedDB
 */
export async function loadChatSnapshot(chatId: string): Promise<any> {
  try {
    const { openDatabase, getSnapshot } = await import('~/lib/persistence/db');

    const db = await openDatabase();
    if (!db) {
      return null;
    }

    const snapshot = await getSnapshot(db, chatId);
    if (snapshot) {
      console.log('✅ INDEXEDDB LOAD: Loaded snapshot from IndexedDB:', chatId);
      return snapshot;
    }

    return null;

  } catch (error) {
    console.error('❌ Error loading snapshot:', error);
    return null;
  }
}

/**
 * Check if a chat exists in IndexedDB
 */
export async function chatExists(chatId: string): Promise<boolean> {
  const chat = await loadChatMessages(chatId);
  return chat !== null;
}

/**
 * Get chat metadata only (lightweight check) from IndexedDB
 */
export async function getChatMetadata(chatId: string): Promise<{ title: string; id: string } | null> {
  try {
    const chat = await loadFromIndexedDB(chatId);
    if (chat) {
      return {
        id: chat.id,
        title: chat.description || 'Untitled'
      };
    }

    return null;

  } catch (error) {
    console.error('❌ Error getting chat metadata:', error);
    return null;
  }
}

// Make functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).loadChatMessages = loadChatMessages;
  (window as any).loadChatSnapshot = loadChatSnapshot;
  (window as any).chatExists = chatExists;
  (window as any).getChatMetadata = getChatMetadata;
}
