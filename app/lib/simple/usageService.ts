import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';

/**
 * Simple Usage Service - Single Source of Truth (Supabase)
 * No complex sync, no local storage, just direct database operations
 */

export interface UsageData {
  dailyMessageCount: number;
  dailyMessageLimit: number;
  remainingMessages: number;
  totalMessages: number;
  totalProjects: number;
}

/**
 * Get current usage data directly from Supabase
 */
export async function getCurrentUsage(): Promise<UsageData> {
  const user = authState.get().user;
  if (!user) {
    throw new Error('User not authenticated');
  }

  try {
    const today = new Date().toISOString().split('T')[0];

    // Get usage tracking for today
    const { data: usage } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    // Get user profile
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    const dailyMessageCount = usage?.messages_sent || 0;
    const dailyMessageLimit = profile?.daily_message_limit || 10;
    const remainingMessages = Math.max(0, dailyMessageLimit - dailyMessageCount);

    const result: UsageData = {
      dailyMessageCount,
      dailyMessageLimit,
      remainingMessages,
      totalMessages: profile?.total_messages_sent || 0,
      totalProjects: profile?.total_projects_created || 0,
    };

    console.log('📊 SIMPLE USAGE: Current usage from database:', result);
    return result;

  } catch (error) {
    console.error('❌ SIMPLE USAGE: Failed to get usage:', error);
    throw error;
  }
}

/**
 * Update local state with fresh usage data from database
 */
export async function refreshUsageDisplay(): Promise<void> {
  try {
    const usage = await getCurrentUsage();
    
    // Update local state to match database
    const currentState = authState.get();
    authState.set({
      ...currentState,
      remainingMessages: usage.remainingMessages,
      profile: {
        ...currentState.profile,
        daily_message_count: usage.dailyMessageCount,
        daily_message_limit: usage.dailyMessageLimit,
        total_messages_sent: usage.totalMessages,
        total_projects_created: usage.totalProjects,
      },
    });

    console.log('✅ SIMPLE USAGE: Display refreshed with database data');
  } catch (error) {
    console.error('❌ SIMPLE USAGE: Failed to refresh display:', error);
  }
}

/**
 * Track a message (increment usage)
 */
export async function trackMessage(tokensUsed: number = 0, modelUsed: string = ''): Promise<void> {
  const user = authState.get().user;
  if (!user) {
    throw new Error('User not authenticated');
  }

  try {
    const today = new Date().toISOString().split('T')[0];

    console.log('📊 SIMPLE TRACK: Tracking message usage...', { tokensUsed, modelUsed });

    // Get existing usage for today
    const { data: existing } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (existing) {
      // Update existing record
      await supabase
        .from('usage_tracking')
        .update({
          messages_sent: (existing.messages_sent || 0) + 1,
          tokens_used: (existing.tokens_used || 0) + tokensUsed,
          api_calls_made: (existing.api_calls_made || 0) + 1,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id)
        .eq('date', today);
    } else {
      // Create new record
      await supabase
        .from('usage_tracking')
        .insert({
          user_id: user.id,
          date: today,
          messages_sent: 1,
          tokens_used: tokensUsed,
          api_calls_made: 1,
          projects_created: 0,
          subscription_tier: 'free',
        });
    }

    // Update user profile
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profile) {
      await supabase
        .from('user_profiles')
        .update({
          daily_message_count: (profile.daily_message_count || 0) + 1,
          total_messages_sent: (profile.total_messages_sent || 0) + 1,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);
    }

    // Refresh display with new data
    await refreshUsageDisplay();

    console.log('✅ SIMPLE TRACK: Message tracked successfully');

  } catch (error) {
    console.error('❌ SIMPLE TRACK: Failed to track message:', error);
    throw error;
  }
}

/**
 * Track a project creation
 */
export async function trackProject(): Promise<void> {
  const user = authState.get().user;
  if (!user) {
    throw new Error('User not authenticated');
  }

  try {
    const today = new Date().toISOString().split('T')[0];

    console.log('📊 SIMPLE TRACK: Tracking project creation...');

    // Get existing usage for today
    const { data: existing } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (existing) {
      // Update existing record
      await supabase
        .from('usage_tracking')
        .update({
          projects_created: (existing.projects_created || 0) + 1,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id)
        .eq('date', today);
    } else {
      // Create new record
      await supabase
        .from('usage_tracking')
        .insert({
          user_id: user.id,
          date: today,
          messages_sent: 0,
          tokens_used: 0,
          api_calls_made: 0,
          projects_created: 1,
          subscription_tier: 'free',
        });
    }

    // Update user profile
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profile) {
      await supabase
        .from('user_profiles')
        .update({
          total_projects_created: (profile.total_projects_created || 0) + 1,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);
    }

    // Refresh display with new data
    await refreshUsageDisplay();

    console.log('✅ SIMPLE TRACK: Project tracked successfully');

  } catch (error) {
    console.error('❌ SIMPLE TRACK: Failed to track project:', error);
    throw error;
  }
}

// Make functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).getCurrentUsage = getCurrentUsage;
  (window as any).refreshUsageDisplay = refreshUsageDisplay;
  (window as any).trackMessage = trackMessage;
  (window as any).trackProject = trackProject;
}
