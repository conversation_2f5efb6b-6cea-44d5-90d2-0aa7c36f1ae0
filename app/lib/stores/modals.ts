import { atom } from 'nanostores';

// Modal state store
export const modalStore = atom({
  userSettings: false,
  upgrade: false,
});

// Actions to control modals
export const modalActions = {
  openUserSettings: () => {
    modalStore.set({ ...modalStore.get(), userSettings: true });
  },
  closeUserSettings: () => {
    modalStore.set({ ...modalStore.get(), userSettings: false });
  },
  openUpgrade: () => {
    modalStore.set({ ...modalStore.get(), upgrade: true });
  },
  closeUpgrade: () => {
    modalStore.set({ ...modalStore.get(), upgrade: false });
  },
  closeAll: () => {
    modalStore.set({ userSettings: false, upgrade: false });
  },
};
