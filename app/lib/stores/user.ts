import { atom, computed } from 'nanostores';
import type { User, Session } from '@supabase/supabase-js';
import { supabase } from '~/lib/supabase/client';
import { toast } from 'react-toastify';
import { ApiKeyService } from '~/lib/services/apiKeyService';

// Security imports
let checkRateLimit: any;
let logSecurityEvent: any;

// Lazy load security modules to avoid circular dependencies
const loadSecurityModules = async () => {
  if (!checkRateLimit || !logSecurityEvent) {
    try {
      const [requestValidation, apiKeyEncryption] = await Promise.all([
        import('~/lib/security/requestValidation'),
        import('~/lib/security/apiKeyEncryption'),
      ]);
      checkRateLimit = requestValidation.checkRateLimit;
      logSecurityEvent = apiKeyEncryption.logSecurityEvent;
    } catch (error) {
      console.warn('Security modules not available:', error);
      // Fallback implementations
      checkRateLimit = () => ({ allowed: true });
      logSecurityEvent = () => {};
    }
  }
};

// Types
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  username?: string;
  subscription_tier: 'free' | 'pro';
  subscription_status: 'active' | 'cancelled' | 'expired';
  subscription_started_at?: string;
  subscription_expires_at?: string;
  daily_message_limit: number;
  last_message_reset_date?: string;
  total_projects_created: number;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  remainingMessages: number;
}

// Stores
export const authState = atom<AuthState>({
  user: null,
  session: null,
  profile: null,
  isLoading: false, // Start with false to prevent infinite loading
  isAuthenticated: false,
  remainingMessages: 0,
});

// Computed values
export const isAuthenticated = computed(authState, (state) => state.isAuthenticated);
export const currentUser = computed(authState, (state) => state.user);
export const userProfile = computed(authState, (state) => state.profile);
export const isProUser = computed(authState, (state) => state.profile?.subscription_tier === 'pro');
export const remainingMessages = computed(authState, (state) => state.remainingMessages);

// Internal flag to coordinate between initialize() and auth state listener
let isInitializing = false;

// Actions
export const userActions = {
  // Initialize auth state - simplified approach with timeout
  async initialize() {
    if (isInitializing) {
      console.log('🚀 INITIALIZE: Already initializing, skipping...');
      return;
    }

    try {
      isInitializing = true;
      console.log('🚀 INITIALIZE: Starting auth initialization...');

      // Load security modules
      await loadSecurityModules();

      // Set loading state initially
      authState.set({ ...authState.get(), isLoading: true });

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Auth initialization timeout')), 5000);
      });

      // Check for existing session with timeout
      const sessionPromise = supabase.auth.getSession();

      const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]) as any;

      if (error) {
        console.error('🚀 INITIALIZE: Error getting session:', error);
        authState.set({
          user: null,
          session: null,
          profile: null,
          isLoading: false,
          isAuthenticated: false,
          remainingMessages: 0,
        });
        return;
      }

      if (session?.user) {
        console.log('🚀 INITIALIZE: Found existing session, setting user');
        await this.setUser(session.user, session, false);
      } else {
        console.log('🚀 INITIALIZE: No existing session found');
        authState.set({
          user: null,
          session: null,
          profile: null,
          isLoading: false,
          isAuthenticated: false,
          remainingMessages: 0,
        });
      }

    } catch (error) {
      console.error('🚀 INITIALIZE: Auth initialization error:', error);
      authState.set({
        user: null,
        session: null,
        profile: null,
        isLoading: false,
        isAuthenticated: false,
        remainingMessages: 0,
      });
    } finally {
      isInitializing = false;
      console.log('🚀 INITIALIZE: Initialization complete');
    }
  },

  // Set user and fetch profile - simplified for faster loading
  async setUser(user: User, session: Session, showSuccessToast = false) {
    try {
      console.log('🔧 SET_USER: Starting setUser for:', user.email);

      // Create profile from auth data immediately (no database blocking)
      console.log('🔧 SET_USER: Creating profile from auth user data...');

      // Handle avatar URL properly (especially for Google profile images)
      let avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture || null;
      if (avatarUrl) {
        // For Google profile images, ensure we get a reasonable size and remove problematic parameters
        if (avatarUrl.includes('googleusercontent.com')) {
          // Remove size parameters and add a standard size
          avatarUrl = avatarUrl.split('=')[0] + '=s200-c';
        }
        console.log('🔧 SET_USER: Processed avatar URL:', avatarUrl);
      }

      // Create profile from auth user data (fast, no database blocking)
      const profile: UserProfile = {
        id: user.id,
        email: user.email || '',
        full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
        avatar_url: avatarUrl,
        username: user.user_metadata?.username || null,
        subscription_tier: 'free', // Default to free tier - will be updated from database
        subscription_status: 'active',
        daily_message_limit: 10,
        total_projects_created: 0,
        preferences: {},
        created_at: user.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('🔧 SET_USER: Profile created from auth data:', profile);

      // Set auth state immediately (don't wait for database)
      const newAuthState = {
        user,
        session,
        profile,
        isLoading: false,
        isAuthenticated: true,
        remainingMessages: 10,
      };

      console.log('🔧 SET_USER: Setting auth state immediately:', newAuthState);
      authState.set(newAuthState);

      // Sync with database in background (non-blocking)
      this.syncProfileToDatabase(user, profile).catch(error => {
        console.error('🔧 SET_USER: Background database sync failed:', error);
      });

      // Sync API keys from Supabase to cookies for compatibility
      this.syncApiKeysToCookies(user.id).catch(error => {
        console.error('🔧 SET_USER: Background API key sync failed:', error);
      });

      // Note: Success toast is handled by the landing page when login=success parameter is present
      // This prevents duplicate toasts on every page reload

      console.log('🔧 SET_USER: setUser completed successfully');
    } catch (error) {
      console.error('🔧 SET_USER: Set user error:', error);

      // Fallback auth state - still set user as authenticated
      const fallbackAuthState = {
        user,
        session,
        profile: null,
        isLoading: false,
        isAuthenticated: true,
        remainingMessages: 10,
      };
      console.log('🔧 SET_USER: Setting fallback auth state:', fallbackAuthState);
      authState.set(fallbackAuthState);
    }
  },

  // Force refresh profile from database (clears cache)
  async forceRefreshProfile() {
    const state = authState.get();
    if (!state.user) {
      console.log('🔄 FORCE_REFRESH: No user to refresh');
      return;
    }

    try {
      console.log('🔄 FORCE_REFRESH: Force refreshing profile from database...');

      // Clear any cached data in message limit service
      const { messageLimitService } = await import('~/lib/services/messageLimitService');
      messageLimitService.clearCachedStatus(state.user.id);

      // Fetch fresh profile from database
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', state.user.id)
        .single();

      if (fetchError) {
        console.error('🔄 FORCE_REFRESH: Error fetching profile:', fetchError);
        return;
      }

      if (existingProfile) {
        // For pro users, remaining messages should be -1 (unlimited)
        // For free users, we need to get the count from usage_tracking table
        let remainingMessages = -1;

        if (existingProfile.subscription_tier === 'pro') {
          remainingMessages = -1; // Unlimited
        } else {
          // Get today's usage from usage_tracking table
          const today = new Date().toISOString().split('T')[0];
          const { data: usageData } = await supabase
            .from('usage_tracking')
            .select('messages_sent')
            .eq('user_id', state.user.id)
            .eq('date', today)
            .single();

          const dailyCount = usageData?.messages_sent || 0;
          remainingMessages = Math.max(0, (existingProfile.daily_message_limit || 10) - dailyCount);
        }

        authState.set({
          ...state,
          profile: existingProfile,
          remainingMessages: remainingMessages,
        });

        console.log('🔄 FORCE_REFRESH: Profile refreshed successfully', {
          subscriptionTier: existingProfile.subscription_tier,
          remaining: remainingMessages,
          isPro: existingProfile.subscription_tier === 'pro'
        });
      }
    } catch (error) {
      console.error('🔄 FORCE_REFRESH: Error force refreshing profile:', error);
    }
  },

  // Background database sync (non-blocking)
  async syncProfileToDatabase(user: User, profile: UserProfile) {
    try {
      console.log('🔄 SYNC_PROFILE: Starting background database sync...');

      // Try to fetch existing profile first
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('🔄 SYNC_PROFILE: Error fetching existing profile:', fetchError);

        // If profile doesn't exist (PGRST116), we'll handle it below
        // For other errors, try to create profile as fallback
        if (fetchError.code !== 'PGRST116') {
          console.log('🔄 SYNC_PROFILE: Attempting to create profile as fallback...');
          await this.createProfileWithRetry(user, profile);
        }
        return;
      }

      if (existingProfile) {
        console.log('🔄 SYNC_PROFILE: Profile exists in database, updating local state...');
        // Update local state with database data and calculate remaining messages
        const currentState = authState.get();

        // For pro users, remaining messages should be -1 (unlimited)
        // For free users, we need to get the count from usage_tracking table
        let remainingMessages = -1;

        if (existingProfile.subscription_tier === 'pro') {
          remainingMessages = -1; // Unlimited
        } else {
          // Get today's usage from usage_tracking table
          const today = new Date().toISOString().split('T')[0];
          const { data: usageData } = await supabase
            .from('usage_tracking')
            .select('messages_sent')
            .eq('user_id', user.id)
            .eq('date', today)
            .single();

          const dailyCount = usageData?.messages_sent || 0;
          remainingMessages = Math.max(0, (existingProfile.daily_message_limit || 10) - dailyCount);
        }

        authState.set({
          ...currentState,
          profile: existingProfile,
          remainingMessages: remainingMessages,
        });

        console.log('🔄 SYNC_PROFILE: Updated with database data', {
          dailyLimit: existingProfile.daily_message_limit,
          subscriptionTier: existingProfile.subscription_tier,
          remaining: remainingMessages,
          isPro: existingProfile.subscription_tier === 'pro'
        });
      } else {
        console.log('🔄 SYNC_PROFILE: Creating new profile in database...');
        await this.createProfileWithRetry(user, profile);
      }
    } catch (error) {
      console.error('🔄 SYNC_PROFILE: Background sync error:', error);
    }
  },

  // Helper function to create profile with retry logic
  async createProfileWithRetry(user: User, profile: UserProfile, maxRetries: number = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 CREATE_PROFILE: Attempt ${attempt}/${maxRetries} to create profile...`);

        const { error: insertError } = await supabase
          .from('user_profiles')
          .insert([profile]);

        if (insertError) {
          console.error(`🔄 CREATE_PROFILE: Attempt ${attempt} failed:`, insertError);

          // If profile already exists (duplicate key error), that's actually success
          if (insertError.code === '23505' || insertError.message?.includes('duplicate key')) {
            console.log('🔄 CREATE_PROFILE: Profile already exists (race condition), this is OK');
            return;
          }

          // If it's the last attempt, log the final error
          if (attempt === maxRetries) {
            console.error('🔄 CREATE_PROFILE: All attempts failed. Profile creation unsuccessful.');
            // Don't throw error - user can still use the app with local profile
            return;
          }

          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        } else {
          console.log('🔄 CREATE_PROFILE: Profile created successfully in database');
          return;
        }
      } catch (error) {
        console.error(`🔄 CREATE_PROFILE: Attempt ${attempt} exception:`, error);

        if (attempt === maxRetries) {
          console.error('🔄 CREATE_PROFILE: All attempts failed with exceptions.');
          return;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  },

  // Ensure user profile exists (for OAuth callback)
  async ensureUserProfile(user: User, session: Session) {
    try {
      console.log('🔄 ENSURE_PROFILE: Checking if user profile exists...');

      // Try to fetch existing profile first
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (existingProfile) {
        console.log('✅ ENSURE_PROFILE: Profile already exists');
        return existingProfile;
      }

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('🔄 ENSURE_PROFILE: Error fetching profile:', fetchError);
        // Continue to create profile anyway
      }

      console.log('🔄 ENSURE_PROFILE: Profile does not exist, creating...');

      // Extract avatar URL with fallback
      const avatarUrl = user.user_metadata?.avatar_url ||
                       user.user_metadata?.picture ||
                       `https://ui-avatars.com/api/?name=${encodeURIComponent(user.user_metadata?.full_name || user.user_metadata?.name || user.email || 'User')}&background=6366f1&color=ffffff&size=128`;

      // Create profile from auth user data
      const profile = {
        id: user.id,
        email: user.email || '',
        full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
        avatar_url: avatarUrl,
        username: user.user_metadata?.username || null,
        subscription_tier: 'free' as const,
        subscription_status: 'active' as const,
        daily_message_limit: 10,
        total_projects_created: 0,
        preferences: {},
        created_at: user.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Create profile with retry logic
      await this.createProfileWithRetry(user, profile, 5); // More retries for OAuth

      console.log('✅ ENSURE_PROFILE: Profile creation completed');
      return profile;
    } catch (error) {
      console.error('🔄 ENSURE_PROFILE: Error ensuring profile:', error);
      throw error;
    }
  },

  // Background API key sync to cookies (non-blocking)
  async syncApiKeysToCookies(userId: string) {
    try {
      console.log('🔄 SYNC_API_KEYS: Starting background API key sync to cookies...');
      await ApiKeyService.syncApiKeysToCookies(supabase, userId);
      console.log('🔄 SYNC_API_KEYS: API keys synced to cookies successfully');
    } catch (error) {
      console.error('🔄 SYNC_API_KEYS: Background API key sync error:', error);
    }
  },

  // Clear user state
  clearUser() {
    console.log('🧹 CLEAR_USER: Clearing user state...');
    authState.set({
      user: null,
      session: null,
      profile: null,
      isLoading: false,
      isAuthenticated: false,
      remainingMessages: 0,
    });
    console.log('🧹 CLEAR_USER: User state cleared');
  },

  // Complete auth cleanup - clears all auth data including localStorage
  async completeSignOut() {
    console.log('🧹 COMPLETE_SIGNOUT: Starting complete sign out...');

    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('🧹 COMPLETE_SIGNOUT: Supabase signOut error:', error);
      }

      // Clear localStorage auth data
      if (typeof window !== 'undefined') {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('genvibe-auth') || key.includes('supabase'))) {
            keysToRemove.push(key);
          }
        }

        keysToRemove.forEach(key => {
          console.log('🧹 COMPLETE_SIGNOUT: Removing localStorage key:', key);
          localStorage.removeItem(key);
        });
      }

      // Clear user state
      this.clearUser();

      console.log('🧹 COMPLETE_SIGNOUT: Complete sign out finished');
      return { error: null };
    } catch (error) {
      console.error('🧹 COMPLETE_SIGNOUT: Error during complete sign out:', error);
      return { error };
    }
  },

  // Get remaining messages for today (LIVE from Supabase)
  async getRemainingMessages(userId: string): Promise<number> {
    try {
      // Import message limit service dynamically to avoid circular dependency
      const { messageLimitService } = await import('~/lib/services/messageLimitService');

      const result = await messageLimitService.getMessageLimitStatus(userId);

      if (result.success) {
        console.log('📊 LIVE_REMAINING: Fetched from Supabase:', {
          remaining: result.status.remaining,
          isPro: result.status.isPro
        });
        return result.status.remaining;
      }

      // Fallback to current state on error
      const currentState = authState.get();
      return currentState.remainingMessages || 10;
    } catch (error) {
      console.error('❌ LIVE_REMAINING: Error fetching live count:', error);

      // Fallback to current state
      const currentState = authState.get();
      if (currentState.profile?.subscription_tier === 'pro') {
        return -1; // Unlimited for pro users
      }
      return currentState.remainingMessages || 10;
    }
  },

  // Check if user can send message
  async canSendMessage(userId: string): Promise<boolean> {
    try {
      const remaining = await userActions.getRemainingMessages(userId);

      // Pro users (remaining = -1) or users with messages left can send
      return remaining === -1 || remaining > 0;
    } catch (error) {
      console.error('Check message limit error:', error);
      return true; // Allow message if check fails (graceful degradation)
    }
  },

  // Update profile
  async updateProfile(updates: Partial<UserProfile>) {
    const state = authState.get();
    if (!state.user || !state.profile) return { error: 'No user or profile found' };

    try {
      const updatedProfile = {
        ...state.profile,
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Update in database first
      const { data: savedProfile, error: updateError } = await supabase
        .from('user_profiles')
        .update(updatedProfile)
        .eq('id', state.user.id)
        .select()
        .single();

      if (updateError) {
        console.error('🔧 UPDATE_PROFILE: Database update error:', updateError);
        // Still update local state as fallback
        authState.set({
          ...state,
          profile: updatedProfile,
        });
        return { data: updatedProfile, warning: 'Updated locally but failed to save to database' };
      }

      // Update local state with saved data
      authState.set({
        ...state,
        profile: savedProfile,
      });

      console.log('🔧 Profile updated in database and locally:', savedProfile);
      return { data: savedProfile };
    } catch (error) {
      console.error('Update profile error:', error);
      return { error };
    }
  },

  // Track message usage with atomic database functions (LIVE from Supabase)
  async trackMessageUsage(userId: string, tokensUsed: number = 0, modelUsed: string = '') {
    try {
      console.log('🔧 TRACK_MESSAGE: Using atomic database function for message tracking...', { userId, tokensUsed, modelUsed });

      // Import message limit service dynamically to avoid circular dependency
      const { messageLimitService } = await import('~/lib/services/messageLimitService');

      const result = await messageLimitService.consumeMessage(userId);

      if (result.success) {
        console.log('✅ TRACK_MESSAGE: Message consumed atomically via database function:', {
          remaining: result.status.remaining,
          dailyCount: result.status.dailyCount,
          isPro: result.status.isPro,
          consumed: true
        });

        // Update local state with the fresh data from database
        const state = authState.get();
        authState.set({
          ...state,
          remainingMessages: result.status.remaining,
          profile: {
            ...state.profile,
            daily_message_count: result.status.dailyCount,
            daily_message_limit: result.status.dailyLimit,
            subscription_tier: result.status.isPro ? 'pro' : 'free',
          }
        });

        console.log('🔄 LOCAL_STATE: Updated with live database data');
      } else {
        console.error('❌ TRACK_MESSAGE: Failed to consume message via database function:', result.error);
        throw new Error(result.error || 'Failed to track message usage');
      }
    } catch (error) {
      console.error('🔧 TRACK_MESSAGE: Error:', error);
      throw error; // Re-throw to let caller handle the error
    }
  },

  // Track project creation with atomic updates
  async trackProjectCreation(userId: string, projectData: any) {
    try {
      const today = new Date().toISOString().split('T')[0];

      console.log('🔧 TRACK_PROJECT: Starting atomic project tracking...', { userId, projectData });

      // Use atomic increment to prevent race conditions
      const { data: existing, error: fetchError } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('🔧 TRACK_PROJECT: Error fetching existing usage:', fetchError);
        return;
      }

      if (existing) {
        // Atomic increment of existing record - only increment projects_created
        const { error: updateError } = await supabase
          .from('usage_tracking')
          .update({
            projects_created: (existing.projects_created || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', userId)
          .eq('date', today);

        if (updateError) {
          console.error('🔧 TRACK_PROJECT: Error updating usage:', updateError);
        } else {
          console.log('✅ TRACK_PROJECT: Usage updated successfully');
        }
      } else {
        // Create new record
        const { error: insertError } = await supabase
          .from('usage_tracking')
          .insert({
            user_id: userId,
            date: today,
            messages_sent: 0,
            tokens_used: 0,
            api_calls_made: 0,
            projects_created: 1,
            subscription_tier: authState.get().profile?.subscription_tier || 'free',
          });

        if (insertError) {
          console.error('🔧 TRACK_PROJECT: Error inserting usage:', insertError);
        } else {
          console.log('✅ TRACK_PROJECT: New usage record created');
        }
      }

      // Update user profile project count atomically
      const state = authState.get();
      if (state.profile) {
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .update({
            total_projects_created: (state.profile.total_projects_created || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId)
          .select()
          .single();

        if (profileError) {
          console.error('🔧 TRACK_PROJECT: Error updating profile:', profileError);
        } else {
          // Update local state with fresh data
          authState.set({
            ...state,
            profile: profileData,
          });
          console.log('✅ TRACK_PROJECT: Profile updated successfully');
        }
      }

      console.log('🔧 TRACK_PROJECT: Project creation tracked');
    } catch (error) {
      console.error('🔧 TRACK_PROJECT: Error:', error);
    }
  },

  // Sign out
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign out error:', error);
      }
      userActions.clearUser();
      return { error };
    } catch (error) {
      console.error('Sign out error:', error);
      userActions.clearUser();
      return { error };
    }
  },

  // Debug utility to check localStorage auth data
  debugAuthStorage() {
    if (typeof window === 'undefined') return;

    console.log('🔍 DEBUG: Checking localStorage for auth data...');
    const authKeys = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('genvibe-auth') || key.includes('supabase'))) {
        const value = localStorage.getItem(key);
        authKeys.push({ key, hasValue: !!value, valueLength: value?.length || 0 });
      }
    }

    console.log('🔍 DEBUG: Auth-related localStorage keys:', authKeys);
    return authKeys;
  },
};

// Initialize auth state listener - handles auth state changes after initialization
if (typeof window !== 'undefined') {
  console.log('🔧 Setting up auth state listener...');

  const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
    console.log('🔄 Auth state changed:', event, session?.user?.email);

    // Skip INITIAL_SESSION since we handle it in initialize()
    if (event === 'INITIAL_SESSION') {
      console.log('🚀 INITIAL_SESSION event - skipping (handled in initialize)');
      return;
    }

    // Track if this is a fresh sign-in vs session restoration
    const currentState = authState.get();
    const wasAuthenticated = currentState.isAuthenticated;

    if (event === 'SIGNED_IN' && session?.user) {
      // Only show toast for actual new sign-ins, not session restoration
      const isNewSignIn = !wasAuthenticated && !isInitializing;
      console.log('✅ SIGNED_IN event - isNewSignIn:', isNewSignIn);
      await userActions.setUser(session.user, session, isNewSignIn);
    } else if (event === 'SIGNED_OUT') {
      console.log('❌ SIGNED_OUT event - clearing user');
      userActions.clearUser();
    } else if (event === 'TOKEN_REFRESHED' && session?.user) {
      console.log('🔄 TOKEN_REFRESHED event - updating user');
      await userActions.setUser(session.user, session, false);
    } else {
      console.log('🔄 Unhandled auth event:', event);
    }

    console.log('🔄 Auth state after handling event:', authState.get());
  });

  // Clean up subscription on page unload
  window.addEventListener('beforeunload', () => {
    subscription.unsubscribe();
  });
}
