import { atom } from 'nanostores';
import { supabase } from '~/lib/supabase/client';
import { authState } from './user';
import { db, getAll } from '~/lib/persistence';
import type { ChatHistoryItem } from '~/lib/persistence/useChatHistory';
import type { Message } from 'ai';

// Migration state
export const migrationState = atom({
  isRunning: false,
  progress: 0,
  total: 0,
  currentStep: '',
  error: null as string | null,
});

// Storage optimization settings
export const STORAGE_LIMITS = {
  // What to store in Supabase (lightweight)
  STORE_IN_DB: {
    chatMetadata: true,        // Title, description, timestamps
    messageHeaders: true,      // Role, timestamp, model used
    messageContent: true,      // BUT with size limits
    userMessages: true,        // Always store user messages
    assistantSummaries: true,  // Store summaries of long assistant messages
  },
  
  // What to keep local only (heavy data)
  KEEP_LOCAL: {
    snapshots: true,          // File system snapshots (HUGE)
    artifacts: true,          // Generated files/code (LARGE)
    fullAssistantContent: true, // Full assistant responses if > limit
  },
  
  // Size limits (in characters)
  MAX_MESSAGE_SIZE: 5000,     // ~5KB per message
  MAX_CONVERSATION_SIZE: 50000, // ~50KB per conversation
  SUMMARY_LENGTH: 500,        // Summary length for large messages
};

export interface OptimizedMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  content_type: 'full' | 'summary' | 'reference';
  original_size?: number;
  summary?: string;
  local_reference?: string; // Reference to local storage
  timestamp: string;
  model_used?: string;
  tokens_used?: number;
}

export interface OptimizedConversation {
  id: string;
  title: string;
  description?: string;
  message_count: number;
  total_size: number;
  last_message_at: string;
  metadata: {
    project_type?: string;
    has_artifacts: boolean;
    has_snapshots: boolean;
    local_storage_keys: string[];
  };
}

// Migration utilities
export const migrationUtils = {
  // Optimize message for database storage
  optimizeMessage(message: Message, conversationId: string): OptimizedMessage {
    const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
    const size = content.length;
    
    let optimized: OptimizedMessage = {
      id: message.id,
      role: message.role as 'user' | 'assistant' | 'system',
      content,
      content_type: 'full',
      timestamp: new Date().toISOString(),
    };

    // If message is too large, create summary and store reference
    if (size > STORAGE_LIMITS.MAX_MESSAGE_SIZE) {
      const summary = this.createMessageSummary(content);
      const localKey = `msg_${conversationId}_${message.id}`;
      
      // Store full content locally
      localStorage.setItem(localKey, content);
      
      optimized = {
        ...optimized,
        content: summary,
        content_type: 'summary',
        original_size: size,
        summary,
        local_reference: localKey,
      };
    }

    return optimized;
  },

  // Create intelligent summary of large messages
  createMessageSummary(content: string): string {
    // Extract key information
    const lines = content.split('\n');
    const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
    const fileActions = content.match(/Create.*?\.[\w]+/g) || [];
    
    let summary = '';
    
    // Add first few lines if they contain instructions
    const firstLines = lines.slice(0, 3).join(' ').substring(0, 200);
    if (firstLines.trim()) {
      summary += firstLines + '... ';
    }
    
    // Add file creation info
    if (fileActions.length > 0) {
      summary += `Created ${fileActions.length} files: ${fileActions.slice(0, 3).join(', ')}`;
      if (fileActions.length > 3) summary += ` and ${fileActions.length - 3} more`;
      summary += '. ';
    }
    
    // Add code block info
    if (codeBlocks.length > 0) {
      summary += `Contains ${codeBlocks.length} code blocks. `;
    }
    
    return summary.substring(0, STORAGE_LIMITS.SUMMARY_LENGTH);
  },

  // Check if conversation should be migrated
  shouldMigrateConversation(chat: ChatHistoryItem): boolean {
    // Skip very large conversations that would exceed limits
    const totalSize = chat.messages.reduce((sum, msg) => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
      return sum + content.length;
    }, 0);
    
    return totalSize < STORAGE_LIMITS.MAX_CONVERSATION_SIZE * 2; // Allow 2x limit for optimization
  },

  // Migrate single conversation
  async migrateConversation(chat: ChatHistoryItem, userId: string): Promise<boolean> {
    try {
      // Create conversation record
      const { data: conversation, error: convError } = await supabase
        .from('chat_conversations')
        .insert({
          user_id: userId,
          title: chat.description || 'Untitled Chat',
          description: chat.description,
          message_count: chat.messages.length,
          last_message_at: chat.timestamp,
          metadata: {
            original_id: chat.id,
            url_id: chat.urlId,
            has_artifacts: chat.messages.some(m => 
              typeof m.content === 'string' && m.content.includes('boltArtifact')
            ),
            has_snapshots: !!localStorage.getItem(`snapshot:${chat.id}`),
            migrated_from: 'indexeddb',
          },
        })
        .select()
        .single();

      if (convError) throw convError;

      // Migrate messages
      const optimizedMessages = chat.messages.map(msg => 
        this.optimizeMessage(msg, conversation.id)
      );

      const { error: msgError } = await supabase
        .from('chat_messages')
        .insert(
          optimizedMessages.map(msg => ({
            conversation_id: conversation.id,
            user_id: userId,
            role: msg.role,
            content: msg.content,
            content_type: msg.content_type,
            metadata: {
              original_size: msg.original_size,
              local_reference: msg.local_reference,
              summary: msg.summary,
            },
          }))
        );

      if (msgError) throw msgError;

      return true;
    } catch (error) {
      console.error('Migration error for conversation:', chat.id, error);
      return false;
    }
  },

  // Run full migration
  async runMigration(): Promise<void> {
    const auth = authState.get();
    if (!auth.isAuthenticated || !auth.user || !db) {
      throw new Error('User not authenticated or database not available');
    }

    migrationState.set({
      isRunning: true,
      progress: 0,
      total: 0,
      currentStep: 'Loading conversations...',
      error: null,
    });

    try {
      // Get all local conversations
      const allChats = await getAll(db);
      const migrateableChats = allChats.filter(this.shouldMigrateConversation);
      
      migrationState.set({
        ...migrationState.get(),
        total: migrateableChats.length,
        currentStep: 'Migrating conversations...',
      });

      let migrated = 0;
      let failed = 0;

      for (const chat of migrateableChats) {
        migrationState.set({
          ...migrationState.get(),
          progress: migrated + failed,
          currentStep: `Migrating: ${chat.description || 'Untitled'}`,
        });

        const success = await this.migrateConversation(chat, auth.user.id);
        if (success) {
          migrated++;
        } else {
          failed++;
        }
      }

      migrationState.set({
        isRunning: false,
        progress: migrated + failed,
        total: migrateableChats.length,
        currentStep: `Migration complete: ${migrated} migrated, ${failed} failed`,
        error: failed > 0 ? `${failed} conversations failed to migrate` : null,
      });

    } catch (error) {
      migrationState.set({
        isRunning: false,
        progress: 0,
        total: 0,
        currentStep: 'Migration failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  },
};
