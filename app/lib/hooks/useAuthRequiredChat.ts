import { useCallback } from 'react';
import { useNavigate } from '@remix-run/react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { toast } from 'react-toastify';
import { useMessageLimits } from '~/lib/hooks/useMessageLimits';

interface UseAuthRequiredChatOptions {
  onAuthRequired?: () => void;
  redirectTo?: string;
}

export function useAuthRequiredChat(options: UseAuthRequiredChatOptions = {}) {
  const { onAuthRequired, redirectTo = '/signin' } = options;
  const navigate = useNavigate();
  const auth = useStore(authState);
  const messageLimits = useMessageLimits();

  // Check if user can send a message
  const checkCanSendMessage = useCallback(async (): Promise<boolean> => {
    // Check if user is authenticated
    if (!auth.isAuthenticated || !auth.user) {
      // Show auth required message with bottom-right positioning
      toast.info('Please sign in to start chatting with GenVibe AI', {
        position: 'bottom-right',
        autoClose: 2000,
      });

      // Call custom callback if provided
      if (onAuthRequired) {
        onAuthRequired();
      }

      // Always redirect to sign in after a short delay to show the toast
      setTimeout(() => {
        const currentPath = window.location.pathname + window.location.search;
        const signInUrl = redirectTo + (currentPath !== '/' ? `?redirect=${encodeURIComponent(currentPath)}` : '');

        // Use window.location for immediate redirect
        window.location.href = signInUrl;
      }, 1000); // Slightly longer delay to ensure toast is seen

      return false;
    }

    // Use the new solid message limit system
    return await messageLimits.checkCanSend();
  }, [auth.isAuthenticated, auth.user, navigate, redirectTo, onAuthRequired, messageLimits]);

  // Wrapper for sending messages that checks auth first
  const sendMessageWithAuth = useCallback(async (
    sendMessageFn: () => Promise<void> | void,
    options?: { skipLimitCheck?: boolean }
  ): Promise<boolean> => {
    // Skip limit check if requested (for system messages, etc.)
    if (options?.skipLimitCheck) {
      if (!auth.isAuthenticated || !auth.user) {
        return await checkCanSendMessage();
      }
      await sendMessageFn();
      return true;
    }

    // Check if user can send message (auth and limits)
    const canSend = await checkCanSendMessage();
    if (!canSend) {
      return false;
    }

    try {
      // Send the message (consumption happens in onFinish callback for successful responses)
      await sendMessageFn();
      console.log('✅ Message sent successfully with auth and limit check');
      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.', {
        position: 'bottom-right',
        autoClose: 4000,
      });
      return false;
    }
  }, [checkCanSendMessage]);

  // Get user's current message status
  const getMessageStatus = useCallback(() => {
    if (!auth.isAuthenticated) {
      return {
        canSend: false,
        reason: 'not_authenticated',
        remaining: 0,
        isPro: false,
      };
    }

    // Use the new solid message limit system
    const status = messageLimits.status;
    if (!status) {
      return {
        canSend: false,
        reason: 'loading',
        remaining: 0,
        isPro: false,
      };
    }

    return {
      canSend: status.canSend,
      reason: status.reason,
      remaining: status.remaining,
      isPro: status.isPro,
    };
  }, [auth.isAuthenticated, messageLimits.status]);

  return {
    isAuthenticated: auth.isAuthenticated,
    user: auth.user,
    profile: auth.profile,
    checkCanSendMessage,
    sendMessageWithAuth,
    getMessageStatus,
    remainingMessages: messageLimits.remaining,
    isPro: messageLimits.isPro,
    messageLimits, // Expose the full message limits hook
  };
}

// Specific hook for chat input components
export function useChatAuth() {
  return useAuthRequiredChat({
    onAuthRequired: () => {
      // Custom behavior for chat components
      console.log('Authentication required for chat');
    },
  });
}

// Hook for checking auth status without side effects
export function useAuthStatus() {
  const auth = useStore(authState);
  const messageLimits = useMessageLimits();

  return {
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    user: auth.user,
    profile: auth.profile,
    remainingMessages: messageLimits.remaining,
    isPro: messageLimits.isPro,
    messageLimits, // Expose message limits for advanced usage
  };
}
