import { useEffect } from 'react';
import { useNavigate, useLocation } from '@remix-run/react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';

interface UseAuthGuardOptions {
  redirectTo?: string;
  requireAuth?: boolean;
  redirectIfAuthenticated?: boolean;
}

export function useAuthGuard(options: UseAuthGuardOptions = {}) {
  const {
    redirectTo = '/signin',
    requireAuth = true,
    redirectIfAuthenticated = false,
  } = options;

  const navigate = useNavigate();
  const location = useLocation();
  const auth = useStore(authState);

  useEffect(() => {
    // Don't redirect while loading
    if (auth.isLoading) return;

    // If user should be authenticated but isn't
    if (requireAuth && !auth.isAuthenticated) {
      const currentPath = location.pathname + location.search;
      const redirectUrl = redirectTo + (currentPath !== '/' ? `?redirect=${encodeURIComponent(currentPath)}` : '');
      navigate(redirectUrl, { replace: true });
      return;
    }

    // If user shouldn't be authenticated but is (e.g., on login page)
    if (redirectIfAuthenticated && auth.isAuthenticated) {
      const urlParams = new URLSearchParams(location.search);
      const redirectPath = urlParams.get('redirect') || '/';
      navigate(redirectPath, { replace: true });
      return;
    }
  }, [auth.isAuthenticated, auth.isLoading, navigate, location, redirectTo, requireAuth, redirectIfAuthenticated]);

  return {
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    user: auth.user,
    profile: auth.profile,
  };
}

// Specific hooks for common use cases
export function useRequireAuth(redirectTo?: string) {
  return useAuthGuard({ requireAuth: true, redirectTo });
}

export function useRedirectIfAuthenticated(redirectTo?: string) {
  const auth = useStore(authState);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Only redirect if we're sure the user is authenticated (not loading)
    if (!auth.isLoading && auth.isAuthenticated) {
      const urlParams = new URLSearchParams(location.search);
      const redirectPath = urlParams.get('redirect') || (redirectTo || '/');
      navigate(redirectPath, { replace: true });
    }
  }, [auth.isAuthenticated, auth.isLoading, navigate, location, redirectTo]);

  return {
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    user: auth.user,
    profile: auth.profile,
  };
}
