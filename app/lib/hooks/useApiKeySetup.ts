import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { ApiKeyService } from '~/lib/services/apiKeyService';
import { supabase } from '~/lib/supabase/client';
import type { ProviderInfo } from '~/types/model';

interface ApiKeyStatus {
  hasKeys: boolean;
  configuredProviders: string[];
  missingProviders: string[];
  recommendedProvider: string | null;
}

interface UseApiKeySetupOptions {
  requiredForChat?: boolean;
  autoShowOnFirstVisit?: boolean;
}

export function useApiKeySetup(options: UseApiKeySetupOptions = {}) {
  const { requiredForChat = true, autoShowOnFirstVisit = true } = options;
  const auth = useStore(authState);

  const [isSetupModalOpen, setIsSetupModalOpen] = useState(false);
  const [hasValidApiKey, setHasValidApiKey] = useState(false);
  const [isFirstVisit, setIsFirstVisit] = useState(false);
  const [checkedApiKeys, setCheckedApiKeys] = useState(false);
  const [apiKeyStatus, setApiKeyStatus] = useState<ApiKeyStatus | null>(null);
  const [loading, setLoading] = useState(false);

  // Check if user has any valid API keys from Supabase
  const checkApiKeys = useCallback(async () => {
    if (!auth.isAuthenticated || !auth.user) {
      setHasValidApiKey(false);
      setCheckedApiKeys(true);
      setApiKeyStatus(null);
      return false;
    }

    try {
      setLoading(true);

      // Use the new client-side API key service
      const hasKeys = await ApiKeyService.hasApiKeys(supabase, auth.user.id);

      // Get detailed status for better UI feedback
      const detailedStatus = await ApiKeyService.getApiKeyStatus(supabase, auth.user.id);

      setApiKeyStatus(detailedStatus);
      setHasValidApiKey(hasKeys);
      setCheckedApiKeys(true);
      return hasKeys;
    } catch (error) {
      console.error('Error checking API keys:', error);
      setHasValidApiKey(false);
      setCheckedApiKeys(true);
      setApiKeyStatus({
        hasKeys: false,
        configuredProviders: [],
        missingProviders: ['Google', 'OpenAI', 'Anthropic'],
        recommendedProvider: 'Google',
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [auth.isAuthenticated, auth.user]);

  // Check if this is user's first visit
  const checkFirstVisit = useCallback(() => {
    const hasVisited = localStorage.getItem('genvibe-has-visited');
    const isFirst = !hasVisited;
    setIsFirstVisit(isFirst);

    if (isFirst) {
      localStorage.setItem('genvibe-has-visited', 'true');
    }

    return isFirst;
  }, []);

  // Initialize on mount and when auth changes
  useEffect(() => {
    if (auth.isAuthenticated) {
      const initializeSetup = async () => {
        const hasKeys = await checkApiKeys();
        const isFirst = checkFirstVisit();

        // Auto-show setup modal for first-time users without API keys
        if (autoShowOnFirstVisit && isFirst && !hasKeys) {
          // Small delay to let the page load
          setTimeout(() => {
            setIsSetupModalOpen(true);
          }, 1000);
        }
      };

      initializeSetup();
    } else {
      // Reset state when not authenticated
      setHasValidApiKey(false);
      setCheckedApiKeys(false);
      setApiKeyStatus(null);
    }
  }, [auth.isAuthenticated, checkApiKeys, checkFirstVisit, autoShowOnFirstVisit]);

  // Check if user can send a message (has API keys)
  const canSendMessage = useCallback(async (showToastIfNot = true): Promise<boolean> => {
    // Check authentication first
    if (!auth.isAuthenticated) {
      if (showToastIfNot) {
        toast.error('Please sign in to start chatting', {
          position: 'bottom-right',
          autoClose: 3000,
        });

        // Direct redirect to signin page instead of showing modal
        setTimeout(() => {
          const currentPath = window.location.pathname + window.location.search;
          const signInUrl = '/signin' + (currentPath !== '/' ? `?redirect=${encodeURIComponent(currentPath)}` : '');
          window.location.href = signInUrl;
        }, 1000);
      }
      return false;
    }

    // Always ensure we have checked API keys
    if (!checkedApiKeys) {
      const hasKeys = await checkApiKeys();
      if (!hasKeys && requiredForChat) {
        if (showToastIfNot) {
          toast.error('Please add at least one API key to start chatting. We recommend Google AI for fast and cost-effective results.', {
            position: 'bottom-right',
            autoClose: 7000,
            onClick: () => setIsSetupModalOpen(true),
          });
        }
        return false;
      }
      return hasKeys;
    }

    if (!hasValidApiKey && requiredForChat) {
      if (showToastIfNot) {
        toast.error('Please add at least one API key to start chatting. We recommend Google AI for fast and cost-effective results.', {
          position: 'bottom-right',
          autoClose: 7000,
          onClick: () => setIsSetupModalOpen(true),
        });
      }
      return false;
    }

    return true;
  }, [auth.isAuthenticated, hasValidApiKey, requiredForChat, checkedApiKeys, checkApiKeys]);

  // Validate API key for specific provider
  const validateProviderKey = useCallback((provider: ProviderInfo): boolean => {
    if (!apiKeyStatus) return false;
    return apiKeyStatus.configuredProviders.includes(provider.name);
  }, [apiKeyStatus]);

  // Show setup modal manually
  const showSetupModal = useCallback(() => {
    setIsSetupModalOpen(true);
  }, []);

  // Hide setup modal
  const hideSetupModal = useCallback(() => {
    setIsSetupModalOpen(false);
  }, []);

  // Handle setup completion
  const handleSetupComplete = useCallback(async () => {
    setIsSetupModalOpen(false);
    // Re-check API keys after setup
    await checkApiKeys();

    // Ensure API keys are synced to cookies for model fetching
    if (auth.user) {
      try {
        await ApiKeyService.syncApiKeysToCookies(supabase, auth.user.id);
        console.log('🔄 API keys synced to cookies after setup completion');
      } catch (error) {
        console.error('Failed to sync API keys to cookies:', error);
      }
    }

    toast.success('Great! You can now start building with GenVibe AI.', {
      position: 'bottom-right',
      autoClose: 3000,
    });
  }, [checkApiKeys, auth.user]);

  // Get setup status for UI
  const getSetupStatus = useCallback(() => {
    return {
      hasAnyKey: hasValidApiKey,
      configuredProviders: apiKeyStatus?.configuredProviders || [],
      missingProviders: apiKeyStatus?.missingProviders || [],
      recommendedProvider: apiKeyStatus?.recommendedProvider || null,
      totalConfigured: apiKeyStatus?.configuredProviders.length || 0,
      isFirstVisit,
      needsSetup: !hasValidApiKey && requiredForChat,
      loading,
      isAuthenticated: auth.isAuthenticated,
    };
  }, [hasValidApiKey, apiKeyStatus, isFirstVisit, requiredForChat, loading, auth.isAuthenticated]);

  // Wrapper for actions that require API keys
  const withApiKeyCheck = useCallback(<T extends any[]>(
    action: (...args: T) => void | Promise<void>,
    showModalOnFail = true
  ) => {
    return async (...args: T) => {
      const canProceed = await canSendMessage(false);
      if (canProceed) {
        await action(...args);
      } else if (showModalOnFail) {
        setIsSetupModalOpen(true);
      }
    };
  }, [canSendMessage]);

  return {
    // State
    isSetupModalOpen,
    hasValidApiKey,
    isFirstVisit,
    checkedApiKeys,
    loading,
    apiKeyStatus,

    // Actions
    showSetupModal,
    hideSetupModal,
    handleSetupComplete,
    checkApiKeys,
    canSendMessage,
    validateProviderKey,
    withApiKeyCheck,

    // Status
    getSetupStatus,
  };
}
