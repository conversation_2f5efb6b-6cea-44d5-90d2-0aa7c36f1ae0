import { useState, useEffect } from 'react';
import { pexelsService, type CachedImage, type AppType } from '~/lib/services/pexelsService';

interface UsePexelsImagesOptions {
  appType?: AppType | string;
  query?: string;
  count?: number;
  autoFetch?: boolean;
}

interface UsePexelsImagesReturn {
  images: CachedImage[];
  loading: boolean;
  error: string | null;
  fetchImages: () => Promise<void>;
  refetch: () => Promise<void>;
}

/**
 * 🖼️ React hook for fetching Pexels images
 *
 * Usage examples:
 *
 * // RECOMMENDED: Intelligent search with specific terms
 * const { images, loading } = usePexelsImages({ query: 'modern coffee shop interior', count: 5 });
 * const { images, loading } = usePexelsImages({ query: 'professional business team meeting', count: 3 });
 *
 * // FALLBACK: Generic app type (less flexible)
 * const { images, loading } = usePexelsImages({ appType: 'ecommerce', count: 5 });
 *
 * // Manual fetching
 * const { images, fetchImages } = usePexelsImages({ autoFetch: false });
 */
export function usePexelsImages({
  appType,
  query,
  count = 5,
  autoFetch = true,
}: UsePexelsImagesOptions = {}): UsePexelsImagesReturn {
  const [images, setImages] = useState<CachedImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchImages = async (): Promise<void> => {
    if (!appType && !query) {
      setError('Either appType or query must be provided');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let fetchedImages: CachedImage[];

      if (query) {
        fetchedImages = await pexelsService.searchImages(query, count);
      } else if (appType) {
        fetchedImages = await pexelsService.getImagesForAppType(appType, count);
      } else {
        throw new Error('No search criteria provided');
      }

      setImages(fetchedImages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch images';
      setError(errorMessage);
      console.error('🖼️ usePexelsImages: Error fetching images:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async (): Promise<void> => {
    await fetchImages();
  };

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch && (appType || query)) {
      fetchImages();
    }
  }, [appType, query, count, autoFetch]);

  return {
    images,
    loading,
    error,
    fetchImages,
    refetch,
  };
}

/**
 * 🖼️ Simplified hook for app type images
 *
 * Usage: const { images, loading } = useAppTypeImages('ecommerce', 5);
 */
export function useAppTypeImages(appType: AppType | string, count: number = 5) {
  return usePexelsImages({ appType, count });
}

/**
 * 🖼️ Simplified hook for search images
 *
 * Usage: const { images, loading } = useSearchImages('coffee shop', 3);
 */
export function useSearchImages(query: string, count: number = 5) {
  return usePexelsImages({ query, count });
}
