/**
 * 🛡️ Secure API Keys Hook - Safe Integration
 * 
 * This hook provides secure API key management with backward compatibility.
 * It works alongside the existing cookie-based system without breaking changes.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { createSupabaseClient } from '~/lib/supabase/client';
import { SecureApiKeyManager, createSecureApiKeyManager } from '~/lib/services/secureApiKeyManager';

interface UseSecureApiKeysOptions {
  enableSecureMode?: boolean;    // Enable secure API key management
  enableFallback?: boolean;      // Enable cookie fallback
  cacheTimeout?: number;         // Cache timeout in milliseconds
  autoSync?: boolean;            // Auto-sync on auth changes
}

interface ApiKeyState {
  keys: Record<string, string>;
  loading: boolean;
  error: string | null;
  source: 'supabase' | 'cookie' | 'cache' | 'mixed';
  lastUpdated: number;
}

export function useSecureApiKeys(options: UseSecureApiKeysOptions = {}) {
  const {
    enableSecureMode = true,
    enableFallback = true,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    autoSync = true
  } = options;

  const auth = useStore(authState);
  const supabase = createSupabaseClient();
  
  // State management
  const [state, setState] = useState<ApiKeyState>({
    keys: {},
    loading: false,
    error: null,
    source: 'cache',
    lastUpdated: 0
  });

  // Secure API key manager instance
  const managerRef = useRef<SecureApiKeyManager | null>(null);

  // Initialize manager
  useEffect(() => {
    if (!managerRef.current) {
      managerRef.current = createSecureApiKeyManager(supabase, {
        cacheTimeout,
        enableFallback,
        enableLogging: true
      });
    }

    // Update user ID when auth changes
    if (auth.user?.id) {
      managerRef.current.setUserId(auth.user.id);
    } else {
      managerRef.current.setUserId(null);
    }
  }, [auth.user?.id, supabase, cacheTimeout, enableFallback]);

  /**
   * Load all API keys securely
   */
  const loadApiKeys = useCallback(async (): Promise<Record<string, string>> => {
    if (!managerRef.current) {
      return {};
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const keys = await managerRef.current.getAllApiKeys();
      const source = Object.keys(keys).length > 0 ? 'supabase' : 'cookie';

      setState(prev => ({
        ...prev,
        keys,
        loading: false,
        source,
        lastUpdated: Date.now()
      }));

      return keys;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load API keys';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));

      console.error('🚨 Failed to load API keys:', error);
      return {};
    }
  }, []);

  /**
   * Get a specific API key
   */
  const getApiKey = useCallback(async (provider: string): Promise<string | null> => {
    if (!managerRef.current) {
      return null;
    }

    try {
      const key = await managerRef.current.getApiKey(provider);
      
      // Update state if we got a new key
      if (key && !state.keys[provider]) {
        setState(prev => ({
          ...prev,
          keys: { ...prev.keys, [provider]: key },
          lastUpdated: Date.now()
        }));
      }

      return key;
    } catch (error) {
      console.error(`🚨 Failed to get API key for ${provider}:`, error);
      return null;
    }
  }, [state.keys]);

  /**
   * Save an API key securely
   */
  const saveApiKey = useCallback(async (provider: string, apiKey: string): Promise<void> => {
    if (!managerRef.current) {
      throw new Error('API key manager not initialized');
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      await managerRef.current.saveApiKey(provider, apiKey);

      // Update local state
      setState(prev => ({
        ...prev,
        keys: { ...prev.keys, [provider]: apiKey },
        loading: false,
        source: 'supabase',
        lastUpdated: Date.now()
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save API key';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));

      throw error;
    }
  }, []);

  /**
   * Check if user has any API keys
   */
  const hasApiKeys = useCallback(async (): Promise<boolean> => {
    if (!managerRef.current) {
      return false;
    }

    try {
      return await managerRef.current.hasApiKeys();
    } catch (error) {
      console.error('🚨 Failed to check API keys:', error);
      return false;
    }
  }, []);

  /**
   * Clear API key cache
   */
  const clearCache = useCallback((): void => {
    if (managerRef.current) {
      managerRef.current.clearCache();
      setState(prev => ({
        ...prev,
        keys: {},
        source: 'cache',
        lastUpdated: 0
      }));
    }
  }, []);

  /**
   * Get cache statistics for debugging
   */
  const getCacheStats = useCallback(() => {
    if (!managerRef.current) {
      return { size: 0, entries: [] };
    }

    return managerRef.current.getCacheStats();
  }, []);

  /**
   * Refresh API keys from source
   */
  const refresh = useCallback(async (): Promise<void> => {
    clearCache();
    await loadApiKeys();
  }, [clearCache, loadApiKeys]);

  // Auto-load API keys when authenticated
  useEffect(() => {
    if (auth.isAuthenticated && autoSync) {
      loadApiKeys();
    } else if (!auth.isAuthenticated) {
      // Clear state when not authenticated
      setState({
        keys: {},
        loading: false,
        error: null,
        source: 'cache',
        lastUpdated: 0
      });
    }
  }, [auth.isAuthenticated, autoSync, loadApiKeys]);

  // Provide backward compatibility with existing cookie-based system
  const getApiKeysFromCookies = useCallback((): Record<string, string> => {
    try {
      const storedApiKeys = document.cookie
        .split('; ')
        .find(row => row.startsWith('apiKeys='))
        ?.split('=')[1];

      if (storedApiKeys) {
        return JSON.parse(decodeURIComponent(storedApiKeys));
      }
    } catch (error) {
      console.error('🚨 Failed to parse API keys from cookies:', error);
    }

    return {};
  }, []);

  return {
    // State
    apiKeys: state.keys,
    loading: state.loading,
    error: state.error,
    source: state.source,
    lastUpdated: state.lastUpdated,
    isAuthenticated: auth.isAuthenticated,

    // Actions
    loadApiKeys,
    getApiKey,
    saveApiKey,
    hasApiKeys,
    clearCache,
    refresh,

    // Utilities
    getCacheStats,
    getApiKeysFromCookies, // Backward compatibility

    // Status
    isEmpty: Object.keys(state.keys).length === 0,
    count: Object.keys(state.keys).length,
    providers: Object.keys(state.keys),
  };
}

export type { UseSecureApiKeysOptions, ApiKeyState };
