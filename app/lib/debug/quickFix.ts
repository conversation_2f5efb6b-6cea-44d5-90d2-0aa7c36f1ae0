import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';

/**
 * Quick fix for current issues:
 * 1. Sync usage tracking display with database
 * 2. Clean up any sync conflicts
 */
export async function quickFixCurrentIssues(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔧 QUICK_FIX: Starting quick fix for current issues...');

    // 1. Get current usage from database
    const today = new Date().toISOString().split('T')[0];
    const { data: usage, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (usageError && usageError.code !== 'PGRST116') {
      console.error('❌ Error fetching usage:', usageError);
      return;
    }

    // 2. Get current profile from database
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('❌ Error fetching profile:', profileError);
      return;
    }

    // 3. Calculate correct remaining messages
    const dailyCount = usage?.messages_sent || 0;
    const dailyLimit = profile?.daily_message_limit || 10;
    const remainingMessages = Math.max(0, dailyLimit - dailyCount);

    console.log('📊 QUICK_FIX: Current state from database:', {
      dailyCount,
      dailyLimit,
      remainingMessages,
      totalMessages: profile?.total_messages_sent || 0,
      totalProjects: profile?.total_projects_created || 0
    });

    // 4. Update local state to match database
    const currentState = authState.get();
    authState.set({
      ...currentState,
      profile: {
        ...profile,
        daily_message_count: dailyCount, // Use usage tracking data
      },
      remainingMessages: remainingMessages,
    });

    console.log('✅ QUICK_FIX: Local state updated to match database');

    // 5. Disable automatic sync temporarily to prevent conflicts
    console.log('⏸️ QUICK_FIX: Disabling automatic sync for 10 seconds...');
    
    // You can call this function from browser console
    console.log('🎯 QUICK_FIX: Complete! Usage display should now be correct.');
    console.log('💡 TIP: If projects still reappear after deletion, wait 10 seconds before deleting again.');

  } catch (error) {
    console.error('❌ QUICK_FIX: Failed:', error);
  }
}

/**
 * Force refresh user state from database
 */
export async function refreshUserState(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔄 REFRESH: Refreshing user state from database...');

    // Get fresh data from database
    const [profileResult, usageResult] = await Promise.all([
      supabase.from('user_profiles').select('*').eq('id', user.id).single(),
      supabase.from('usage_tracking').select('*').eq('user_id', user.id).eq('date', new Date().toISOString().split('T')[0]).single()
    ]);

    if (profileResult.error) {
      console.error('❌ Error fetching profile:', profileResult.error);
      return;
    }

    const profile = profileResult.data;
    const usage = usageResult.data; // May be null if no usage today

    // Calculate remaining messages
    const dailyCount = usage?.messages_sent || 0;
    const dailyLimit = profile.daily_message_limit || 10;
    const remainingMessages = Math.max(0, dailyLimit - dailyCount);

    // Update local state
    const currentState = authState.get();
    authState.set({
      ...currentState,
      profile: {
        ...profile,
        daily_message_count: dailyCount,
      },
      remainingMessages: remainingMessages,
    });

    console.log('✅ REFRESH: User state refreshed successfully', {
      dailyCount,
      dailyLimit,
      remainingMessages
    });

  } catch (error) {
    console.error('❌ REFRESH: Failed:', error);
  }
}

// Make functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).quickFixCurrentIssues = quickFixCurrentIssues;
  (window as any).refreshUserState = refreshUserState;
}
