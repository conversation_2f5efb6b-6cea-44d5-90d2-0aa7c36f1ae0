import { toast } from 'react-toastify';

export interface GeminiQuotaError {
  isQuotaError: boolean;
  modelName?: string;
  errorMessage?: string;
  suggestedFallback?: string;
}

// Free tier models that don't require billing
const FREE_TIER_MODELS = [
  'gemini-2.0-flash',
  'gemini-1.5-flash',
  'gemini-1.5-flash-8b'
];

// Paid tier models that require billing
const PAID_TIER_MODELS = [
  'gemini-2.5-flash-preview-05-20',
  'gemini-2.5-pro-preview-05-06',
  'gemini-2.5-flash-preview',
  'gemini-2.5-pro-preview',
  'gemini-2.5-flash',
  'gemini-2.5-pro'
];

/**
 * Parse Gemini API error to check if it's a quota/billing error
 */
export function parseGeminiError(error: any): GeminiQuotaError {
  try {
    console.log('🔍 Parsing Gemini error:', error);

    // Convert error to string for comprehensive checking
    const errorString = JSON.stringify(error).toLowerCase();
    const errorMessage = error?.message || error?.toString() || '';

    // Check if it's a quota error - be more comprehensive
    const isQuotaError = error?.status === 429 ||
                        error?.code === 429 ||
                        errorString.includes('quota') ||
                        errorString.includes('free tier') ||
                        errorString.includes('resource_exhausted') ||
                        errorString.includes('billing') ||
                        errorString.includes('doesn\'t have a free quota tier') ||
                        errorMessage.toLowerCase().includes('quota') ||
                        errorMessage.toLowerCase().includes('free tier') ||
                        errorMessage.toLowerCase().includes('billing');

    console.log('🔍 Is quota error:', isQuotaError, { errorString: errorString.substring(0, 200) });

    if (!isQuotaError) {
      return { isQuotaError: false };
    }

    // Extract model name from error
    let modelName = '';
    const modelMatch = errorString.match(/gemini-[\w\.-]+/i);
    if (modelMatch) {
      modelName = modelMatch[0];
    }

    // Determine suggested fallback
    let suggestedFallback = '';
    if (PAID_TIER_MODELS.some(model => modelName.includes(model.split('-')[0]) || errorString.includes(model.toLowerCase()))) {
      // If paid model failed, suggest best free model
      suggestedFallback = 'gemini-2.5-flash-preview-05-20';
    }

    console.log('🔍 Parsed quota error:', { modelName, suggestedFallback });

    return {
      isQuotaError: true,
      modelName,
      errorMessage: errorMessage || 'Quota exceeded',
      suggestedFallback
    };

  } catch (parseError) {
    console.error('Error parsing Gemini error:', parseError);
    return { isQuotaError: false };
  }
}

/**
 * Handle Gemini quota error with user-friendly message and auto-fallback
 */
export function handleGeminiQuotaError(
  error: any,
  currentModel: string,
  onModelSwitch?: (newModel: string) => void
): boolean {
  console.log('🔍 Handling potential Gemini error for model:', currentModel);

  const quotaError = parseGeminiError(error);

  if (!quotaError.isQuotaError) {
    console.log('❌ Not a quota error, passing to default handler');
    return false; // Not a quota error, let other error handlers deal with it
  }

  console.log('✅ Confirmed quota error, handling...');

  // Check if current model is a paid model or if error mentions paid models
  const isPaidModel = PAID_TIER_MODELS.some(model =>
    currentModel.toLowerCase().includes(model.split('-')[0]) ||
    currentModel.toLowerCase().includes('2.5')
  );

  const errorMentionsPaidModel = quotaError.suggestedFallback && quotaError.suggestedFallback.length > 0;

  if (isPaidModel || errorMentionsPaidModel) {
    // Auto-switch to free model
    const fallbackModel = quotaError.suggestedFallback || 'gemini-2.5-flash-preview-05-20';

    console.log('🔄 Auto-switching from paid to free model:', { currentModel, fallbackModel });

    toast.error(
      `${currentModel} requires billing. Switching to ${fallbackModel}...`,
      {
        position: 'bottom-right',
        autoClose: 4000,
      }
    );

    // Auto-switch model after a short delay
    setTimeout(() => {
      if (onModelSwitch) {
        onModelSwitch(fallbackModel);
        toast.success(`Switched to ${fallbackModel}. Please try your request again.`, {
          position: 'bottom-right',
          autoClose: 4000,
        });
      }
    }, 1500);

  } else {
    // General quota error for free models
    console.log('⚠️ Quota error for free model:', currentModel);
    toast.error(
      `Quota exceeded for ${currentModel}. Please try again later or use a different model.`,
      {
        position: 'bottom-right',
        autoClose: 5000,
      }
    );
  }

  return true; // Error was handled
}

/**
 * Get the best fallback model for a given model
 */
export function getBestFallbackModel(currentModel: string): string {
  // If current model is paid, fallback to best free model
  if (PAID_TIER_MODELS.some(model => currentModel.includes(model.split('-')[0]))) {
    return 'gemini-2.5-flash-preview-05-20';
  }

  // If current model is already free, try another free model
  if (currentModel.includes('2.5-flash')) {
    return 'gemini-2.0-flash';
  }

  if (currentModel.includes('2.0-flash')) {
    return 'gemini-1.5-flash';
  }

  if (currentModel.includes('1.5-flash')) {
    return 'gemini-1.5-flash-8b';
  }

  // Default fallback
  return 'gemini-2.5-flash-preview-05-20';
}

/**
 * Check if a model requires billing
 */
export function isModelPaid(modelName: string): boolean {
  return PAID_TIER_MODELS.some(model =>
    modelName.includes(model.split('-')[0])
  );
}

/**
 * Get user-friendly model tier description
 */
export function getModelTierDescription(modelName: string): string {
  if (isModelPaid(modelName)) {
    return 'Pro (Requires Billing)';
  }
  return 'Free';
}
