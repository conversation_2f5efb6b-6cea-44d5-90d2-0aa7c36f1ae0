import { createScopedLogger } from '~/utils/logger';

const logger = createScopedLogger('ai-response-validator');

/**
 * Validates and sanitizes AI SDK responses to prevent type validation errors
 */
export class AIResponseValidator {
  /**
   * Validates a Gemini API response structure
   */
  static validateGeminiResponse(response: any): { isValid: boolean; sanitized?: any; error?: string } {
    try {
      if (!response) {
        return { isValid: false, error: 'Empty response' };
      }

      // Check if response has candidates array
      if (!response.candidates || !Array.isArray(response.candidates)) {
        logger.warn('Response missing candidates array:', response);
        return { isValid: false, error: 'Missing candidates array' };
      }

      // Validate each candidate
      const sanitizedCandidates = response.candidates.map((candidate: any, index: number) => {
        if (!candidate) {
          logger.warn(`Candidate ${index} is null/undefined`);
          return null;
        }

        // Ensure content exists and has parts array
        if (!candidate.content) {
          logger.warn(`Candidate ${index} missing content`);
          candidate.content = { role: 'model', parts: [] };
        }

        if (!candidate.content.parts || !Array.isArray(candidate.content.parts)) {
          logger.warn(`Candidate ${index} missing content.parts array`);
          candidate.content.parts = [];
        }

        // Ensure role is set
        if (!candidate.content.role) {
          candidate.content.role = 'model';
        }

        // Ensure finishReason exists
        if (!candidate.finishReason) {
          candidate.finishReason = 'STOP';
        }

        // Ensure index exists
        if (typeof candidate.index !== 'number') {
          candidate.index = index;
        }

        return candidate;
      }).filter(Boolean); // Remove null candidates

      // If no valid candidates, create a default one
      if (sanitizedCandidates.length === 0) {
        logger.warn('No valid candidates found, creating default');
        sanitizedCandidates.push({
          content: {
            role: 'model',
            parts: []
          },
          finishReason: 'STOP',
          index: 0
        });
      }

      const sanitizedResponse = {
        ...response,
        candidates: sanitizedCandidates
      };

      logger.info('Response validation successful');
      return { isValid: true, sanitized: sanitizedResponse };

    } catch (error) {
      logger.error('Response validation error:', error);
      return { 
        isValid: false, 
        error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Creates a safe wrapper around AI SDK responses
   */
  static wrapResponse(response: any, provider: string = 'unknown'): any {
    if (provider.toLowerCase() === 'google') {
      const validation = this.validateGeminiResponse(response);
      if (!validation.isValid) {
        logger.error('Invalid Gemini response:', validation.error);
        // Return a minimal valid response structure
        return {
          candidates: [{
            content: {
              role: 'model',
              parts: []
            },
            finishReason: 'STOP',
            index: 0
          }],
          usageMetadata: response?.usageMetadata || {},
          modelVersion: response?.modelVersion || 'unknown',
          responseId: response?.responseId || 'unknown'
        };
      }
      return validation.sanitized;
    }

    // For other providers, return as-is for now
    return response;
  }

  /**
   * Validates streaming response chunks
   */
  static validateStreamChunk(chunk: any, provider: string = 'unknown'): { isValid: boolean; sanitized?: any } {
    try {
      if (!chunk) {
        return { isValid: false };
      }

      if (provider.toLowerCase() === 'google') {
        // For streaming, chunks might have different structure
        if (chunk.candidates) {
          const validation = this.validateGeminiResponse(chunk);
          return { isValid: validation.isValid, sanitized: validation.sanitized };
        }
      }

      return { isValid: true, sanitized: chunk };
    } catch (error) {
      logger.error('Stream chunk validation error:', error);
      return { isValid: false };
    }
  }
}

/**
 * Error recovery strategies for different error types
 */
export class AIErrorRecovery {
  /**
   * Attempts to recover from AI SDK type validation errors
   */
  static async recoverFromValidationError(
    error: any,
    originalRequest: any,
    retryFunction: (request: any) => Promise<any>
  ): Promise<any> {
    logger.warn('Attempting recovery from validation error:', error);

    // Strategy 1: Retry with simplified request
    try {
      const simplifiedRequest = {
        ...originalRequest,
        // Remove potentially problematic parameters
        temperature: 0.7,
        maxTokens: Math.min(originalRequest.maxTokens || 4096, 4096)
      };

      logger.info('Retrying with simplified request');
      const result = await retryFunction(simplifiedRequest);
      logger.info('Recovery successful with simplified request');
      return result;
    } catch (retryError) {
      logger.warn('Simplified request retry failed:', retryError);
    }

    // Strategy 2: Return a safe fallback response
    logger.warn('All recovery strategies failed, returning fallback');
    throw new Error('AI response validation failed and recovery was unsuccessful. Please try again with a different model.');
  }
}

/**
 * Middleware to wrap AI SDK calls with validation
 */
export function withResponseValidation<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  provider: string = 'unknown'
): T {
  return (async (...args: any[]) => {
    try {
      const result = await fn(...args);
      
      // If result has a response property, validate it
      if (result && typeof result === 'object' && result.response) {
        const validation = AIResponseValidator.wrapResponse(result.response, provider);
        return { ...result, response: validation };
      }

      return result;
    } catch (error) {
      // Check if it's a validation error
      if (error?.name === 'AI_TypeValidationError' || 
          (error?.message && error.message.includes('Type validation failed'))) {
        logger.error('AI SDK validation error detected:', error);
        
        // Try to recover
        return AIErrorRecovery.recoverFromValidationError(error, args[0], fn);
      }
      
      // Re-throw other errors
      throw error;
    }
  }) as T;
}
