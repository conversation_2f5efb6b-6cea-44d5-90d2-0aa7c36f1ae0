import CryptoJS from 'crypto-js';

// We'll use the client-side supabase instance passed from the component

// This ensures we use the authenticated user's session

// Encryption key - using a default for now (can be improved later)
const ENCRYPTION_KEY = 'genvibe-api-key-encryption-v1-change-in-production';

export interface ApiKeyData {
  id: string;
  provider_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProviderConfig {
  name: string;
  displayName: string;
  icon: string;
  getApiKeyUrl: string;
  description: string;
}

export const SUPPORTED_PROVIDERS: ProviderConfig[] = [
  {
    name: 'Google',
    displayName: 'Google AI',
    icon: 'Brain', // Lucide React icon for Google AI
    getApiKeyUrl: 'https://aistudio.google.com/app/apikey',
    description: 'Gemini models - Fast and efficient (Default)'
  },
  {
    name: 'OpenAI',
    displayName: 'OpenAI',
    icon: 'Zap', // Lucide React icon for OpenAI
    getApiKeyUrl: 'https://platform.openai.com/api-keys',
    description: 'GPT models - Versatile and powerful'
  },
  {
    name: 'Anthropic',
    displayName: 'Anthropic',
    icon: 'Bot', // Lucide React icon for Anthropic
    getApiKeyUrl: 'https://console.anthropic.com/settings/keys',
    description: 'Claude models - Great for coding'
  }
];

// Simple API key validation - only check basic requirements
export function validateApiKeyFormat(provider: string, apiKey: any): { isValid: boolean; error?: string } {
  // Enhanced type handling to prevent validation errors
  let keyString: string;

  if (apiKey === null || apiKey === undefined) {
    keyString = '';
  } else if (typeof apiKey === 'string') {
    keyString = apiKey;
  } else if (typeof apiKey === 'object') {
    // Handle React SyntheticEvent or other objects
    if (apiKey.target && typeof apiKey.target.value === 'string') {
      keyString = apiKey.target.value;
    } else if (apiKey.value && typeof apiKey.value === 'string') {
      keyString = apiKey.value;
    } else if (apiKey.key && typeof apiKey.key === 'string') {
      keyString = apiKey.key;
    } else {
      keyString = String(apiKey);
    }
  } else {
    keyString = String(apiKey);
  }

  const trimmedKey = keyString.trim();

  console.log('🔍 Enhanced API key validation:', {
    provider,
    originalType: typeof apiKey,
    originalValue: apiKey,
    processedKeyLength: trimmedKey.length,
    hasContent: trimmedKey.length > 0,
    keyPreview: trimmedKey.length > 8 ? `${trimmedKey.substring(0, 8)}...` : 'too short or empty',
  });

  // Only check if the key is not empty and has reasonable length
  if (!trimmedKey || trimmedKey.length === 0) {
    console.log('❌ API key validation failed: empty key');
    return { isValid: false, error: 'API key cannot be empty' };
  }

  if (trimmedKey.length < 10) {
    console.log('❌ API key validation failed: too short');
    return { isValid: false, error: 'API key appears to be too short (minimum 10 characters)' };
  }

  console.log('✅ Basic API key validation passed for', provider);
  return { isValid: true };
}

// Verify API key with actual provider
export async function verifyApiKey(provider: string, apiKey: any): Promise<{ isValid: boolean; error?: string; billingEnabled?: boolean; recommendedModel?: string }> {
  // Robust API key conversion to handle any input type
  let keyString: string;

  console.log('🔍 Raw API Key Input Debug:', {
    provider,
    originalType: typeof apiKey,
    originalValue: apiKey,
    isObject: typeof apiKey === 'object',
    hasToString: apiKey && typeof apiKey === 'object' && typeof apiKey.toString === 'function',
    objectKeys: typeof apiKey === 'object' && apiKey ? Object.keys(apiKey) : null
  });

  if (typeof apiKey === 'string') {
    keyString = apiKey;
  } else if (apiKey && typeof apiKey === 'object') {
    // Handle different object types
    if (apiKey.value && typeof apiKey.value === 'string') {
      // If it's an object with a value property
      keyString = apiKey.value;
    } else if (apiKey.key && typeof apiKey.key === 'string') {
      // If it's an object with a key property
      keyString = apiKey.key;
    } else if (typeof apiKey.toString === 'function') {
      // Try toString but check if it's meaningful
      const stringified = apiKey.toString();
      if (stringified !== '[object Object]') {
        keyString = stringified;
      } else {
        // Object toString failed, try JSON.stringify
        try {
          keyString = JSON.stringify(apiKey);
        } catch {
          keyString = '';
        }
      }
    } else {
      keyString = '';
    }
  } else {
    keyString = String(apiKey || '');
  }

  const trimmedKey = keyString.trim();

  console.log('🔍 API Key Verification Debug:', {
    provider,
    originalType: typeof apiKey,
    processedKeyLength: trimmedKey ? trimmedKey.length : 0,
    keyPreview: trimmedKey && trimmedKey.length > 8 ? `${trimmedKey.substring(0, 8)}...` : 'too short',
    isValidString: typeof trimmedKey === 'string' && trimmedKey.length > 0,
  });

  // First check format
  const formatCheck = validateApiKeyFormat(provider, trimmedKey);

  if (!formatCheck.isValid) {
    return { isValid: false, error: formatCheck.error };
  }

  try {
    switch (provider) {
      case 'Google':
        return await verifyGoogleApiKey(trimmedKey);
      case 'OpenAI':
        return await verifyOpenAIApiKey(trimmedKey);
      case 'Anthropic':
        return await verifyAnthropicApiKey(trimmedKey);
      default:
        return { isValid: false, error: 'Unsupported provider for verification' };
    }
  } catch (error) {
    console.error('🚨 API Key Verification Error:', error);
    return { isValid: false, error: `Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

// Verify Google API key
async function verifyGoogleApiKey(apiKey: string): Promise<{ isValid: boolean; error?: string; billingEnabled?: boolean; recommendedModel?: string }> {
  try {
    console.log('🔍 Verifying Google API key:', apiKey && apiKey.length > 8 ? apiKey.substring(0, 8) + '...' : 'invalid key');

    // First try to list models - this is a simple GET request that works reliably
    const modelsResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('🔍 Google API models response status:', modelsResponse.status);

    if (!modelsResponse.ok) {
      const errorData = (await modelsResponse.json().catch(() => ({}))) as any;
      console.log('🔍 Google API error data:', errorData);

      if (modelsResponse.status === 403) {
        return { isValid: false, error: 'API key is invalid or access denied' };
      }

      if (modelsResponse.status === 429) {
        return { isValid: false, error: 'API key quota exceeded' };
      }

      if (modelsResponse.status === 400) {
        const errorMessage = errorData?.error?.message || '';

        if (errorMessage.includes('billing') || errorMessage.includes('quota')) {
          return { isValid: false, error: 'Billing not enabled for this API key' };
        }

        return { isValid: false, error: `Invalid request: ${errorMessage}` };
      }

      return { isValid: false, error: `API verification failed (${modelsResponse.status})` };
    }

    const modelsData = (await modelsResponse.json()) as any;
    console.log('🔍 Google API models loaded:', modelsData?.models?.length || 0);

    // Check if we have access to paid models (indicates billing is enabled)
    const models = modelsData?.models || [];
    const hasPaidModels = models.some(
      (model: any) =>
        model.name?.includes('gemini-pro') ||
        model.name?.includes('gemini-1.5-pro') ||
        model.name?.includes('gemini-2.0-pro'),
    );

    // If we get here, the API key is valid
    return {
      isValid: true,
      billingEnabled: hasPaidModels, // True if we have access to paid models
      recommendedModel: 'gemini-2.5-flash-preview-05-20', // Always recommend Gemini 2.5 Flash as default
    };
  } catch (error) {
    console.error('🚨 Google API verification error:', error);
    return {
      isValid: false,
      error: `Network error during verification: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// Verify OpenAI API key
async function verifyOpenAIApiKey(apiKey: string): Promise<{ isValid: boolean; error?: string; billingEnabled?: boolean; recommendedModel?: string }> {
  try {
    console.log('🔍 Verifying OpenAI API key:', apiKey && apiKey.length > 8 ? apiKey.substring(0, 8) + '...' : 'invalid key');

    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      }
    });

    console.log('🔍 OpenAI API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.log('🔍 OpenAI API error data:', errorData);

      if (response.status === 401) {
        return { isValid: false, error: 'Invalid OpenAI API key' };
      }

      if (response.status === 429) {
        return { isValid: false, error: 'API key quota exceeded' };
      }

      return { isValid: false, error: `API verification failed (${response.status})` };
    }

    const data = (await response.json()) as any;
    console.log('🔍 OpenAI API verification successful, models loaded:', data?.data?.length || 0);

    // OpenAI requires billing for API access
    return {
      isValid: true,
      billingEnabled: true,
      recommendedModel: 'gpt-4o'
    };
  } catch (error) {
    console.error('🚨 OpenAI API verification error:', error);
    return { isValid: false, error: `Network error during verification: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

// Verify Anthropic API key
async function verifyAnthropicApiKey(apiKey: string): Promise<{ isValid: boolean; error?: string; billingEnabled?: boolean; recommendedModel?: string }> {
  try {
    console.log('🔍 Verifying Anthropic API key:', apiKey && apiKey.length > 8 ? apiKey.substring(0, 8) + '...' : 'invalid key');

    // Test with a simple message request (minimal usage)
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hi' }]
      })
    });

    console.log('🔍 Anthropic API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.log('🔍 Anthropic API error data:', errorData);

      if (response.status === 401) {
        return { isValid: false, error: 'Invalid Anthropic API key' };
      }

      if (response.status === 429) {
        return { isValid: false, error: 'API key quota exceeded' };
      }

      return { isValid: false, error: `API verification failed (${response.status})` };
    }

    const data = await response.json();
    console.log('🔍 Anthropic API verification successful:', !!data);

    // Anthropic requires billing for API access
    return {
      isValid: true,
      billingEnabled: true,
      recommendedModel: 'claude-3-5-sonnet-20241022'
    };
  } catch (error) {
    console.error('🚨 Anthropic API verification error:', error);
    return { isValid: false, error: `Network error during verification: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

// Encrypt API key
function encryptApiKey(apiKey: string): string {
  return CryptoJS.AES.encrypt(apiKey, ENCRYPTION_KEY).toString();
}

// Decrypt API key
function decryptApiKey(encryptedKey: string): string {
  const bytes = CryptoJS.AES.decrypt(encryptedKey, ENCRYPTION_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
}

export class ApiKeyService {
  // Get all API keys for a user
  static async getUserApiKeys(supabase: any, userId: string): Promise<ApiKeyData[]> {
    const { data, error } = await supabase
      .from('user_api_keys')
      .select('id, provider_name, is_active, created_at, updated_at')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to fetch API keys: ${error.message}`);
    }

    return data || [];
  }

  // Get decrypted API key for a specific provider
  static async getApiKey(supabase: any, userId: string, providerName: string): Promise<string | null> {
    const { data, error } = await supabase
      .from('user_api_keys')
      .select('encrypted_api_key')
      .eq('user_id', userId)
      .eq('provider_name', providerName)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      return null;
    }

    try {
      return decryptApiKey(data.encrypted_api_key);
    } catch (err) {
      console.error('Failed to decrypt API key:', err);
      return null;
    }
  }

  // Save or update API key
  static async saveApiKey(supabase: any, userId: string, providerName: string, apiKey: any): Promise<void> {
    // Validate provider
    if (!SUPPORTED_PROVIDERS.find((p) => p.name === providerName)) {
      throw new Error(`Unsupported provider: ${providerName}`);
    }

    console.log('💾 Saving API key:', {
      provider: providerName,
      userId,
      keyType: typeof apiKey,
      keyLength: apiKey && typeof apiKey === 'string' ? apiKey.length : 'unknown',
    });

    // Use enhanced validation that handles any input type
    const validation = validateApiKeyFormat(providerName, apiKey);

    if (!validation.isValid) {
      console.error('❌ API key validation failed:', validation.error);
      throw new Error(validation.error || 'Invalid API key format');
    }

    // Extract the validated key string
    let keyString: string;
    if (typeof apiKey === 'string') {
      keyString = apiKey.trim();
    } else if (typeof apiKey === 'object' && apiKey.target && typeof apiKey.target.value === 'string') {
      keyString = apiKey.target.value.trim();
    } else if (typeof apiKey === 'object' && apiKey.value && typeof apiKey.value === 'string') {
      keyString = apiKey.value.trim();
    } else {
      keyString = String(apiKey || '').trim();
    }

    if (!keyString) {
      throw new Error('API key cannot be empty after processing');
    }

    const encryptedKey = encryptApiKey(keyString);

    const { error } = await supabase
      .from('user_api_keys')
      .upsert({
        user_id: userId,
        provider_name: providerName,
        encrypted_api_key: encryptedKey,
        is_active: true,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,provider_name'
      });

    if (error) {
      console.error('Supabase error saving API key:', error);
      throw new Error(`Failed to save API key: ${error.message}`);
    }
  }

  // Delete API key
  static async deleteApiKey(supabase: any, userId: string, providerName: string): Promise<void> {
    const { error } = await supabase
      .from('user_api_keys')
      .update({ is_active: false })
      .eq('user_id', userId)
      .eq('provider_name', providerName);

    if (error) {
      throw new Error(`Failed to delete API key: ${error.message}`);
    }
  }

  // Get all API keys for chat (returns object with provider names as keys)
  static async getAllApiKeysForChat(supabase: any, userId: string): Promise<Record<string, string>> {
    const { data, error } = await supabase
      .from('user_api_keys')
      .select('provider_name, encrypted_api_key')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to fetch API keys: ${error.message}`);
    }

    const apiKeys: Record<string, string> = {};

    for (const row of data || []) {
      try {
        apiKeys[row.provider_name] = decryptApiKey(row.encrypted_api_key);
      } catch (err) {
        console.error(`Failed to decrypt API key for ${row.provider_name}:`, err);
      }
    }

    return apiKeys;
  }

  // Sync Supabase API keys to cookies for compatibility with existing system
  static async syncApiKeysToCookies(supabase: any, userId: string): Promise<void> {
    try {
      const apiKeys = await this.getAllApiKeysForChat(supabase, userId);

      // Only sync if we have keys and we're in the browser
      if (Object.keys(apiKeys).length > 0 && typeof window !== 'undefined') {
        // Set the API keys cookie using native browser API
        const cookieValue = JSON.stringify(apiKeys);
        const expires = new Date();
        expires.setDate(expires.getDate() + 30); // 30 days

        const cookieString = `apiKeys=${encodeURIComponent(cookieValue)}; expires=${expires.toUTCString()}; path=/; ${window.location.protocol === 'https:' ? 'secure; ' : ''}samesite=lax`;

        document.cookie = cookieString;

        console.log('🔄 Synced API keys from Supabase to cookies:', Object.keys(apiKeys));
      }
    } catch (error) {
      console.error('Failed to sync API keys to cookies:', error);
    }
  }

  // Check if user has any API keys
  static async hasApiKeys(supabase: any, userId: string): Promise<boolean> {
    const { count, error } = await supabase
      .from('user_api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      return false;
    }

    return (count || 0) > 0;
  }

  // Get the first available provider for chat (prioritizing Google as default)
  static async getFirstAvailableProvider(supabase: any, userId: string): Promise<string | null> {
    const { data, error } = await supabase
      .from('user_api_keys')
      .select('provider_name')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: true });

    if (error || !data || data.length === 0) {
      return null;
    }

    // Prioritize Google if available
    const googleProvider = data.find(key => key.provider_name === 'Google');
    if (googleProvider) {
      return 'Google';
    }

    // Return the first available provider
    return data[0].provider_name;
  }

  // Get API key status summary for user
  static async getApiKeyStatus(supabase: any, userId: string): Promise<{
    hasKeys: boolean;
    configuredProviders: string[];
    missingProviders: string[];
    recommendedProvider: string | null;
  }> {
    const apiKeys = await this.getUserApiKeys(supabase, userId);
    const configuredProviders = apiKeys.map(key => key.provider_name);
    const allProviders = SUPPORTED_PROVIDERS.map(p => p.name);
    const missingProviders = allProviders.filter(p => !configuredProviders.includes(p));

    return {
      hasKeys: apiKeys.length > 0,
      configuredProviders,
      missingProviders,
      recommendedProvider: configuredProviders.includes('Google') ? 'Google' : configuredProviders[0] || null
    };
  }
}
