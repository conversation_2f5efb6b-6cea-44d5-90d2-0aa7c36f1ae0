import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';
import { toast } from 'react-toastify';

export class SupabaseDebugService {

  /**
   * Generates a valid UUID for testing
   */
  private generateUUID(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }

    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Comprehensive debug check for Supabase connection and permissions
   */
  async runDiagnostics(): Promise<{
    auth: { status: string; user?: any; session?: any; error?: any };
    connection: { status: string; url?: string; error?: any };
    permissions: { status: string; tables?: string[]; error?: any };
    rls: { status: string; policies?: any[]; error?: any };
  }> {
    console.log('🔍 Running Supabase diagnostics...');

    const results = {
      auth: { status: 'unknown' },
      connection: { status: 'unknown' },
      permissions: { status: 'unknown' },
      rls: { status: 'unknown' }
    };

    // 1. Check Authentication
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (userError || sessionError) {
        results.auth = {
          status: 'error',
          error: userError || sessionError
        };
      } else if (user && session) {
        results.auth = {
          status: 'authenticated',
          user: { id: user.id, email: user.email },
          session: { expires_at: session.expires_at }
        };
      } else {
        results.auth = { status: 'not_authenticated' };
      }
    } catch (error) {
      results.auth = { status: 'error', error };
    }

    // 2. Check Connection
    try {
      const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
      if (error) {
        results.connection = { status: 'error', error };
      } else {
        results.connection = {
          status: 'connected',
          url: supabase.supabaseUrl
        };
      }
    } catch (error) {
      results.connection = { status: 'error', error };
    }

    // 3. Check Table Permissions
    try {
      const tables = ['user_profiles', 'chat_conversations', 'chat_messages', 'projects', 'usage_tracking'];
      const tableResults = [];

      for (const table of tables) {
        try {
          const { data, error } = await supabase.from(table).select('*').limit(1);
          tableResults.push({ table, status: error ? 'error' : 'accessible', error });
        } catch (error) {
          tableResults.push({ table, status: 'error', error });
        }
      }

      results.permissions = {
        status: tableResults.every(t => t.status === 'accessible') ? 'all_accessible' : 'some_errors',
        tables: tableResults
      };
    } catch (error) {
      results.permissions = { status: 'error', error };
    }

    // 4. Check RLS Policies (if authenticated)
    if (results.auth.status === 'authenticated') {
      try {
        // Try to insert a test record to check RLS
        const testData = {
          user_id: results.auth.user?.id,
          date: new Date().toISOString().split('T')[0],
          messages_sent: 0,
          tokens_used: 0,
          api_calls_made: 0,
          projects_created: 0,
          subscription_tier: 'free'
        };

        const { data, error } = await supabase
          .from('usage_tracking')
          .insert(testData)
          .select();

        if (error) {
          results.rls = { status: 'policy_error', error };
        } else {
          // Clean up test data
          if (data && data[0]) {
            await supabase.from('usage_tracking').delete().eq('id', data[0].id);
          }
          results.rls = { status: 'policies_working' };
        }
      } catch (error) {
        results.rls = { status: 'error', error };
      }
    }

    console.log('🔍 Diagnostics complete:', results);
    return results;
  }

  /**
   * Test specific table operations
   */
  async testTableOperations(tableName: string): Promise<{
    select: { status: string; error?: any };
    insert: { status: string; error?: any };
    update: { status: string; error?: any };
    delete: { status: string; error?: any };
  }> {
    const user = authState.get().user;
    if (!user) {
      return {
        select: { status: 'no_user' },
        insert: { status: 'no_user' },
        update: { status: 'no_user' },
        delete: { status: 'no_user' }
      };
    }

    const results = {
      select: { status: 'unknown' },
      insert: { status: 'unknown' },
      update: { status: 'unknown' },
      delete: { status: 'unknown' }
    };

    // Test SELECT
    try {
      const { data, error } = await supabase.from(tableName).select('*').limit(1);
      results.select = { status: error ? 'error' : 'success', error };
    } catch (error) {
      results.select = { status: 'error', error };
    }

    // Test INSERT (with different test data per table)
    let testData: any = {};
    let insertedId: string | null = null;

    try {
      switch (tableName) {
        case 'chat_conversations':
          testData = {
            id: this.generateUUID(),
            user_id: user.id,
            title: 'Test Conversation',
            description: 'Debug test conversation'
          };
          break;
        case 'chat_messages':
          // First create a conversation
          const { data: conv } = await supabase.from('chat_conversations').insert({
            id: this.generateUUID(),
            user_id: user.id,
            title: 'Test for Message'
          }).select().single();

          testData = {
            conversation_id: conv?.id,
            user_id: user.id,
            role: 'user',
            content: 'Test message'
          };
          break;
        case 'projects':
          testData = {
            user_id: user.id,
            name: 'Test Project',
            project_type: 'web',
            file_structure: {}
          };
          break;
        case 'usage_tracking':
          testData = {
            user_id: user.id,
            date: new Date().toISOString().split('T')[0],
            messages_sent: 1,
            tokens_used: 0,
            api_calls_made: 1,
            projects_created: 0,
            subscription_tier: 'free'
          };
          break;
        default:
          throw new Error(`Unknown table: ${tableName}`);
      }

      const { data, error } = await supabase.from(tableName).insert(testData).select();
      if (error) {
        results.insert = { status: 'error', error };
      } else {
        results.insert = { status: 'success' };
        insertedId = data?.[0]?.id;
      }
    } catch (error) {
      results.insert = { status: 'error', error };
    }

    // Test UPDATE (if insert succeeded)
    if (insertedId && results.insert.status === 'success') {
      try {
        const updateData = tableName === 'chat_conversations'
          ? { title: 'Updated Test Conversation' }
          : tableName === 'projects'
          ? { name: 'Updated Test Project' }
          : {};

        const { error } = await supabase
          .from(tableName)
          .update(updateData)
          .eq('id', insertedId);

        results.update = { status: error ? 'error' : 'success', error };
      } catch (error) {
        results.update = { status: 'error', error };
      }
    }

    // Test DELETE (cleanup)
    if (insertedId) {
      try {
        const { error } = await supabase.from(tableName).delete().eq('id', insertedId);
        results.delete = { status: error ? 'error' : 'success', error };
      } catch (error) {
        results.delete = { status: 'error', error };
      }
    }

    return results;
  }

  /**
   * Check and fix common issues
   */
  async checkAndFix(): Promise<{ issues: string[]; fixes: string[] }> {
    const issues: string[] = [];
    const fixes: string[] = [];

    const diagnostics = await this.runDiagnostics();

    // Check authentication
    if (diagnostics.auth.status !== 'authenticated') {
      issues.push('User not authenticated');
      fixes.push('Please sign in to continue');
    }

    // Check connection
    if (diagnostics.connection.status === 'error') {
      issues.push('Cannot connect to Supabase database');
      fixes.push('Check your VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables');
    }

    // Check table permissions
    if (diagnostics.permissions.status === 'some_errors') {
      issues.push('Some tables are not accessible');
      fixes.push('Check RLS policies and table permissions');
    }

    // Check RLS policies
    if (diagnostics.rls.status === 'policy_error') {
      issues.push('RLS policies are blocking operations');
      fixes.push('Verify RLS policies allow authenticated users to access their own data');
    }

    return { issues, fixes };
  }

  /**
   * Show user-friendly error message with debugging info
   */
  async showDebugInfo(): Promise<void> {
    const diagnostics = await this.runDiagnostics();
    const { issues, fixes } = await this.checkAndFix();

    console.group('🔍 Supabase Debug Information');
    console.log('Authentication:', diagnostics.auth);
    console.log('Connection:', diagnostics.connection);
    console.log('Permissions:', diagnostics.permissions);
    console.log('RLS Policies:', diagnostics.rls);
    console.log('Issues Found:', issues);
    console.log('Suggested Fixes:', fixes);
    console.groupEnd();

    if (issues.length > 0) {
      toast.error(`Database Error: ${issues[0]}. Check console for details.`, {
        position: 'bottom-right',
        autoClose: 5000
      });
    } else {
      toast.success('Supabase connection and permissions look good!', {
        position: 'bottom-right',
        autoClose: 3000
      });
    }
  }
}

export const supabaseDebugService = new SupabaseDebugService();

// Expose debug function globally for easy testing
if (typeof window !== 'undefined') {
  (window as any).debugSupabase = () => supabaseDebugService.showDebugInfo();
  (window as any).testSupabaseTable = (tableName: string) =>
    supabaseDebugService.testTableOperations(tableName);
}
