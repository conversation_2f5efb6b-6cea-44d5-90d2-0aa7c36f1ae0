import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';
import type { FileMap } from '~/lib/stores/files';

// Project storage limits
const PROJECT_LIMITS = {
  MAX_FILE_SIZE: 100000, // 100KB per file
  MAX_TOTAL_PROJECT_SIZE: 5000000, // 5MB per project
  MAX_FILES_PER_PROJECT: 100,

  // File types to exclude from storage
  EXCLUDE_EXTENSIONS: [
    '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.webp',
    '.woff', '.woff2', '.ttf', '.eot',
    '.mp4', '.mp3', '.wav', '.avi',
    '.zip', '.tar', '.gz', '.rar',
    '.exe', '.dmg', '.pkg',
    '.log', '.tmp', '.cache'
  ],

  // Important file types to always include
  IMPORTANT_EXTENSIONS: [
    '.js', '.ts', '.jsx', '.tsx', '.vue', '.svelte',
    '.html', '.css', '.scss', '.sass', '.less',
    '.json', '.yaml', '.yml', '.toml',
    '.md', '.txt', '.env', '.gitignore',
    '.py', '.java', '.cpp', '.c', '.h',
    '.php', '.rb', '.go', '.rs', '.swift'
  ]
};

interface ProjectMetadata {
  framework?: string;
  dependencies?: Record<string, string>;
  scripts?: Record<string, string>;
  environment?: Record<string, string>;
  buildSettings?: Record<string, any>;
}

export class ProjectStorageService {

  /**
   * Determines if a file should be stored in the database
   */
  private shouldStoreFile(filePath: string, content: string): boolean {
    const extension = this.getFileExtension(filePath);

    // Always exclude certain file types
    if (PROJECT_LIMITS.EXCLUDE_EXTENSIONS.includes(extension)) {
      return false;
    }

    // Always include important file types (within size limits)
    if (PROJECT_LIMITS.IMPORTANT_EXTENSIONS.includes(extension)) {
      return content.length <= PROJECT_LIMITS.MAX_FILE_SIZE;
    }

    // For other files, check if they're small and text-based
    return (
      content.length <= PROJECT_LIMITS.MAX_FILE_SIZE &&
      this.isTextFile(content) &&
      !filePath.includes('node_modules') &&
      !filePath.includes('.git') &&
      !filePath.includes('dist') &&
      !filePath.includes('build')
    );
  }

  /**
   * Checks if content is text-based (not binary)
   */
  private isTextFile(content: string): boolean {
    // Simple heuristic: if content has too many null bytes or non-printable chars, it's likely binary
    const nullBytes = (content.match(/\0/g) || []).length;
    const nonPrintable = (content.match(/[\x00-\x08\x0E-\x1F\x7F-\xFF]/g) || []).length;

    return nullBytes === 0 && nonPrintable < content.length * 0.1;
  }

  /**
   * Gets file extension from path
   */
  private getFileExtension(filePath: string): string {
    const lastDot = filePath.lastIndexOf('.');
    return lastDot > 0 ? filePath.substring(lastDot).toLowerCase() : '';
  }

  /**
   * Optimizes file structure for database storage
   */
  private optimizeFileStructure(files: FileMap): Record<string, any> {
    const optimized: Record<string, any> = {};
    let totalSize = 0;
    let fileCount = 0;

    for (const [filePath, fileData] of Object.entries(files)) {
      if (fileCount >= PROJECT_LIMITS.MAX_FILES_PER_PROJECT) {
        break; // Limit number of files
      }

      if (fileData?.type === 'file' && fileData.content) {
        const content = fileData.content;

        if (this.shouldStoreFile(filePath, content)) {
          if (totalSize + content.length <= PROJECT_LIMITS.MAX_TOTAL_PROJECT_SIZE) {
            optimized[filePath] = {
              type: 'file',
              content: content,
              size: content.length,
              extension: this.getFileExtension(filePath)
            };
            totalSize += content.length;
            fileCount++;
          }
        } else {
          // Store metadata only for excluded files
          optimized[filePath] = {
            type: 'file',
            excluded: true,
            reason: 'size_or_type',
            size: content.length,
            extension: this.getFileExtension(filePath)
          };
        }
      } else if (fileData?.type === 'folder') {
        optimized[filePath] = {
          type: 'folder'
        };
      }
    }

    return optimized;
  }

  /**
   * Extracts project metadata from files
   */
  private extractProjectMetadata(files: FileMap): ProjectMetadata {
    const metadata: ProjectMetadata = {};

    // Extract from package.json
    const packageJsonFile = files['package.json'];
    if (packageJsonFile?.type === 'file' && packageJsonFile.content) {
      try {
        const packageJson = JSON.parse(packageJsonFile.content);
        metadata.dependencies = packageJson.dependencies || {};
        metadata.scripts = packageJson.scripts || {};

        // Detect framework from dependencies
        if (packageJson.dependencies?.react || packageJson.devDependencies?.react) {
          metadata.framework = 'React';
        } else if (packageJson.dependencies?.vue || packageJson.devDependencies?.vue) {
          metadata.framework = 'Vue';
        } else if (packageJson.dependencies?.angular || packageJson.devDependencies?.angular) {
          metadata.framework = 'Angular';
        } else if (packageJson.dependencies?.svelte || packageJson.devDependencies?.svelte) {
          metadata.framework = 'Svelte';
        } else if (packageJson.dependencies?.next || packageJson.devDependencies?.next) {
          metadata.framework = 'Next.js';
        }
      } catch (error) {
        console.warn('Failed to parse package.json:', error);
      }
    }

    // Extract from requirements.txt (Python)
    const requirementsFile = files['requirements.txt'];
    if (requirementsFile?.type === 'file' && requirementsFile.content) {
      metadata.framework = 'Python';
      metadata.dependencies = {};
      requirementsFile.content.split('\n').forEach(line => {
        const [pkg] = line.trim().split('==');
        if (pkg) metadata.dependencies![pkg] = line.trim();
      });
    }

    // Extract environment variables
    const envFile = files['.env'] || files['.env.local'];
    if (envFile?.type === 'file' && envFile.content) {
      metadata.environment = {};
      envFile.content.split('\n').forEach(line => {
        const [key, ...valueParts] = line.trim().split('=');
        if (key && valueParts.length > 0) {
          metadata.environment![key] = valueParts.join('=');
        }
      });
    }

    return metadata;
  }

  /**
   * Saves a project (DISABLED - IndexedDB only mode)
   */
  async saveProject(
    conversationId: string | null,
    projectName: string,
    files: FileMap,
    description?: string
  ): Promise<string | null> {
    // DISABLED: Project storage disabled for IndexedDB-only mode
    console.log('💾 Project storage disabled - using IndexedDB only mode');
    console.log('📝 Project would have been saved:', { conversationId, projectName, description });

    // Return a fake ID to maintain compatibility
    return `local-${Date.now()}`;
  }

  /**
   * Updates an existing project (DISABLED - IndexedDB only mode)
   */
  async updateProject(
    projectId: string,
    files: FileMap,
    updates?: Partial<{
      name: string;
      description: string;
      status: string;
    }>
  ): Promise<boolean> {
    // DISABLED: Project storage disabled for IndexedDB-only mode
    console.log('💾 Project update disabled - using IndexedDB only mode');
    console.log('📝 Project would have been updated:', { projectId, updates });
    return true; // Return success to maintain compatibility
  }

  /**
   * Tracks project creation in usage statistics with atomic updates
   */
  private async trackProjectCreation(): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      console.log('🔧 PROJECT_STORAGE: Tracking project creation with atomic updates...');

      // Use the centralized tracking function for consistency
      const { userActions } = await import('~/lib/stores/user');
      await userActions.trackProjectCreation(user.id, {});

      console.log('✅ PROJECT_STORAGE: Project creation tracked successfully');
    } catch (error) {
      console.error('Failed to track project creation:', error);
    }
  }

  /**
   * Gets user's projects (DISABLED - IndexedDB only mode)
   */
  async getUserProjects(limit: number = 20): Promise<any[]> {
    // DISABLED: Project storage disabled for IndexedDB-only mode
    console.log('💾 Project listing disabled - using IndexedDB only mode');
    return []; // Return empty array to maintain compatibility
  }

  /**
   * Deletes a project (DISABLED - IndexedDB only mode)
   */
  async deleteProject(projectId: string): Promise<boolean> {
    // DISABLED: Project storage disabled for IndexedDB-only mode
    console.log('💾 Project deletion disabled - using IndexedDB only mode');
    console.log('📝 Project would have been deleted:', { projectId });
    return true; // Return success to maintain compatibility
  }
}

export const projectStorageService = new ProjectStorageService();
