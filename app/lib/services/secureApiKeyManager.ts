/**
 * 🛡️ Secure API Key Manager - Phase 1 Implementation
 * 
 * This manager provides secure API key access with fallback to existing cookie system.
 * It's designed to work alongside the current system without breaking anything.
 * 
 * Features:
 * - Secure Supabase storage (encrypted)
 * - Memory caching for performance
 * - Fallback to cookies for backward compatibility
 * - Error handling and recovery
 * - Zero breaking changes
 */

import { ApiKeyService } from './apiKeyService';
import Cookies from 'js-cookie';

// Feature flag to enable/disable secure API key manager
const FEATURE_FLAGS = {
  USE_SECURE_API_KEYS: true,  // Can be disabled if issues arise
  ENABLE_FALLBACK: true,      // Always fallback to cookies if <PERSON><PERSON><PERSON> fails
  CACHE_ENABLED: true,        // Memory caching for performance
  DEBUG_LOGGING: true         // Enhanced logging for debugging
};

interface ApiKeyCache {
  [provider: string]: {
    key: string;
    timestamp: number;
    source: 'supabase' | 'cookie' | 'cache';
  };
}

interface ApiKeyManagerOptions {
  cacheTimeout?: number;      // Cache timeout in milliseconds (default: 5 minutes)
  enableFallback?: boolean;   // Enable cookie fallback (default: true)
  enableLogging?: boolean;    // Enable debug logging (default: true)
}

export class SecureApiKeyManager {
  private cache: ApiKeyCache = {};
  private cacheTimeout: number;
  private enableFallback: boolean;
  private enableLogging: boolean;
  private supabase: any;
  private userId: string | null = null;

  constructor(supabase: any, options: ApiKeyManagerOptions = {}) {
    this.supabase = supabase;
    this.cacheTimeout = options.cacheTimeout || 5 * 60 * 1000; // 5 minutes
    this.enableFallback = options.enableFallback ?? true;
    this.enableLogging = options.enableLogging ?? true;
    
    this.log('🔐 SecureApiKeyManager initialized', { 
      cacheTimeout: this.cacheTimeout,
      enableFallback: this.enableFallback 
    });
  }

  /**
   * Set the current user ID for API key operations
   */
  setUserId(userId: string | null): void {
    if (this.userId !== userId) {
      this.userId = userId;
      this.clearCache(); // Clear cache when user changes
      this.log('👤 User ID updated, cache cleared', { userId });
    }
  }

  /**
   * Get API key for a specific provider with secure fallback
   */
  async getApiKey(provider: string): Promise<string | null> {
    try {
      // Check if secure API keys are enabled
      if (!FEATURE_FLAGS.USE_SECURE_API_KEYS) {
        this.log('🔄 Secure API keys disabled, using cookie fallback', { provider });
        return this.getApiKeyFromCookies(provider);
      }

      // Check cache first (fastest)
      if (FEATURE_FLAGS.CACHE_ENABLED) {
        const cachedKey = this.getCachedKey(provider);
        if (cachedKey) {
          this.log('⚡ API key loaded from cache', { provider, source: 'cache' });
          return cachedKey;
        }
      }

      // Try Supabase (secure)
      if (this.userId) {
        const supabaseKey = await this.getApiKeyFromSupabase(provider);
        if (supabaseKey) {
          this.setCachedKey(provider, supabaseKey, 'supabase');
          this.log('🔐 API key loaded from Supabase', { provider, source: 'supabase' });
          return supabaseKey;
        }
      }

      // Fallback to cookies (backward compatibility)
      if (this.enableFallback && FEATURE_FLAGS.ENABLE_FALLBACK) {
        const cookieKey = this.getApiKeyFromCookies(provider);
        if (cookieKey) {
          this.setCachedKey(provider, cookieKey, 'cookie');
          this.log('🍪 API key loaded from cookies (fallback)', { provider, source: 'cookie' });
          return cookieKey;
        }
      }

      this.log('❌ No API key found for provider', { provider });
      return null;

    } catch (error) {
      this.log('🚨 Error getting API key, trying fallback', { provider, error: error.message });
      
      // Emergency fallback to cookies
      if (this.enableFallback) {
        try {
          const fallbackKey = this.getApiKeyFromCookies(provider);
          if (fallbackKey) {
            this.log('🆘 Emergency fallback successful', { provider });
            return fallbackKey;
          }
        } catch (fallbackError) {
          this.log('💥 Emergency fallback failed', { provider, error: fallbackError.message });
        }
      }

      return null;
    }
  }

  /**
   * Get all API keys for chat functionality
   */
  async getAllApiKeys(): Promise<Record<string, string>> {
    try {
      const providers = ['Google', 'OpenAI', 'Anthropic']; // Common providers
      const apiKeys: Record<string, string> = {};

      // Try to get all keys in parallel
      const keyPromises = providers.map(async (provider) => {
        const key = await this.getApiKey(provider);
        if (key) {
          apiKeys[provider] = key;
        }
      });

      await Promise.all(keyPromises);

      this.log('📦 Loaded all API keys', { 
        providers: Object.keys(apiKeys),
        count: Object.keys(apiKeys).length 
      });

      return apiKeys;

    } catch (error) {
      this.log('🚨 Error getting all API keys, trying cookie fallback', { error: error.message });
      
      // Fallback to cookie-based loading
      if (this.enableFallback) {
        return this.getAllApiKeysFromCookies();
      }

      return {};
    }
  }

  /**
   * Save API key securely (with cookie sync for compatibility)
   */
  async saveApiKey(provider: string, apiKey: string): Promise<void> {
    try {
      if (!this.userId) {
        throw new Error('User ID not set');
      }

      // Save to Supabase (primary)
      await ApiKeyService.saveApiKey(this.supabase, this.userId, provider, apiKey);
      
      // Update cache
      this.setCachedKey(provider, apiKey, 'supabase');
      
      // Sync to cookies for backward compatibility
      await this.syncApiKeyToCookies(provider, apiKey);
      
      this.log('💾 API key saved successfully', { provider });

    } catch (error) {
      this.log('🚨 Error saving API key', { provider, error: error.message });
      throw error;
    }
  }

  /**
   * Check if user has any API keys
   */
  async hasApiKeys(): Promise<boolean> {
    try {
      if (!this.userId) {
        return false;
      }

      // Try Supabase first
      const hasSupabaseKeys = await ApiKeyService.hasApiKeys(this.supabase, this.userId);
      if (hasSupabaseKeys) {
        return true;
      }

      // Fallback to cookies
      if (this.enableFallback) {
        const cookieKeys = this.getAllApiKeysFromCookies();
        return Object.keys(cookieKeys).length > 0;
      }

      return false;

    } catch (error) {
      this.log('🚨 Error checking API keys', { error: error.message });
      
      // Emergency fallback
      if (this.enableFallback) {
        const cookieKeys = this.getAllApiKeysFromCookies();
        return Object.keys(cookieKeys).length > 0;
      }

      return false;
    }
  }

  /**
   * Clear all cached API keys
   */
  clearCache(): void {
    this.cache = {};
    this.log('🧹 API key cache cleared');
  }

  /**
   * Get cache statistics for debugging
   */
  getCacheStats(): { size: number; entries: Array<{ provider: string; source: string; age: number }> } {
    const entries = Object.entries(this.cache).map(([provider, data]) => ({
      provider,
      source: data.source,
      age: Date.now() - data.timestamp
    }));

    return {
      size: Object.keys(this.cache).length,
      entries
    };
  }

  // Private methods

  private async getApiKeyFromSupabase(provider: string): Promise<string | null> {
    if (!this.userId) {
      return null;
    }

    try {
      return await ApiKeyService.getApiKey(this.supabase, this.userId, provider);
    } catch (error) {
      this.log('🚨 Supabase API key fetch failed', { provider, error: error.message });
      return null;
    }
  }

  private getApiKeyFromCookies(provider: string): string | null {
    try {
      const storedApiKeys = Cookies.get('apiKeys');
      if (!storedApiKeys) {
        return null;
      }

      const parsedKeys = JSON.parse(storedApiKeys);
      return parsedKeys[provider] || null;
    } catch (error) {
      this.log('🚨 Cookie API key fetch failed', { provider, error: error.message });
      return null;
    }
  }

  private getAllApiKeysFromCookies(): Record<string, string> {
    try {
      const storedApiKeys = Cookies.get('apiKeys');
      if (!storedApiKeys) {
        return {};
      }

      return JSON.parse(storedApiKeys);
    } catch (error) {
      this.log('🚨 Cookie API keys fetch failed', { error: error.message });
      return {};
    }
  }

  private getCachedKey(provider: string): string | null {
    const cached = this.cache[provider];
    if (!cached) {
      return null;
    }

    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      delete this.cache[provider];
      return null;
    }

    return cached.key;
  }

  private setCachedKey(provider: string, key: string, source: 'supabase' | 'cookie'): void {
    this.cache[provider] = {
      key,
      timestamp: Date.now(),
      source
    };
  }

  private async syncApiKeyToCookies(provider: string, apiKey: string): Promise<void> {
    try {
      const currentKeys = this.getAllApiKeysFromCookies();
      const newKeys = { ...currentKeys, [provider]: apiKey };
      Cookies.set('apiKeys', JSON.stringify(newKeys));
      this.log('🔄 API key synced to cookies', { provider });
    } catch (error) {
      this.log('🚨 Cookie sync failed', { provider, error: error.message });
    }
  }

  private log(message: string, data?: any): void {
    if (this.enableLogging && FEATURE_FLAGS.DEBUG_LOGGING) {
      console.log(`[SecureApiKeyManager] ${message}`, data || '');
    }
  }
}

// Export singleton instance creator
export function createSecureApiKeyManager(supabase: any, options?: ApiKeyManagerOptions): SecureApiKeyManager {
  return new SecureApiKeyManager(supabase, options);
}

// Export feature flags for external control
export { FEATURE_FLAGS as SecureApiKeyFeatureFlags };
