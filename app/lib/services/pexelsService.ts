/**
 * 🖼️ PEXELS API SERVICE
 * 
 * Service for fetching contextual, high-quality stock photography from Pexels
 * Used specifically for Gemini free tier models to provide better image handling
 */

interface PexelsPhoto {
  id: number;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  liked: boolean;
  alt: string;
}

interface PexelsResponse {
  page: number;
  per_page: number;
  photos: PexelsPhoto[];
  total_results: number;
  next_page?: string;
  prev_page?: string;
}

interface CachedImage {
  url: string;
  alt: string;
  photographer: string;
  cached_at: number;
}

// Cache duration: 24 hours
const CACHE_DURATION = 24 * 60 * 60 * 1000;

// Contextual search terms for different app types
const APP_TYPE_SEARCH_TERMS = {
  ecommerce: ['shopping', 'products', 'retail', 'store', 'commerce'],
  restaurant: ['food', 'cooking', 'restaurant', 'dining', 'cuisine'],
  saas: ['technology', 'business', 'office', 'workspace', 'computer'],
  travel: ['travel', 'destinations', 'vacation', 'adventure', 'tourism'],
  fitness: ['fitness', 'workout', 'gym', 'health', 'exercise'],
  education: ['education', 'learning', 'students', 'books', 'study'],
  healthcare: ['healthcare', 'medical', 'doctor', 'hospital', 'wellness'],
  finance: ['finance', 'money', 'banking', 'investment', 'business'],
  social: ['people', 'community', 'social', 'friends', 'networking'],
  portfolio: ['creative', 'design', 'art', 'portfolio', 'professional'],
  blog: ['writing', 'content', 'blog', 'journalism', 'media'],
  gaming: ['gaming', 'entertainment', 'fun', 'technology', 'digital'],
} as const;

type AppType = keyof typeof APP_TYPE_SEARCH_TERMS;

class PexelsService {
  private apiKey: string | null = null;
  private cache: Map<string, CachedImage[]> = new Map();

  constructor() {
    // Try to get API key from environment
    if (typeof window === 'undefined') {
      // Server-side
      this.apiKey = process.env.PEXELS_API_KEY || null;
    } else {
      // Client-side - API key should be passed from server
      this.apiKey = null;
    }
  }

  /**
   * Set API key (for client-side usage)
   */
  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Get contextual search terms for an app type
   */
  getSearchTermsForAppType(appType: string): string[] {
    const normalizedType = appType.toLowerCase() as AppType;
    return APP_TYPE_SEARCH_TERMS[normalizedType] || ['business', 'technology'];
  }

  /**
   * Generate cache key for search terms
   */
  private getCacheKey(searchTerms: string[]): string {
    return searchTerms.sort().join('-');
  }

  /**
   * Check if cached images are still valid
   */
  private isCacheValid(cachedImages: CachedImage[]): boolean {
    if (!cachedImages.length) return false;
    const now = Date.now();
    return (now - cachedImages[0].cached_at) < CACHE_DURATION;
  }

  /**
   * Fetch images from Pexels API
   */
  private async fetchFromPexels(query: string, perPage: number = 10): Promise<PexelsPhoto[]> {
    if (!this.apiKey) {
      throw new Error('Pexels API key not configured');
    }

    const response = await fetch(
      `https://api.pexels.com/v1/search?query=${encodeURIComponent(query)}&per_page=${perPage}&orientation=landscape`,
      {
        headers: {
          'Authorization': this.apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
    }

    const data: PexelsResponse = await response.json();
    return data.photos;
  }

  /**
   * Get images for specific app type with caching
   */
  async getImagesForAppType(appType: string, count: number = 5): Promise<CachedImage[]> {
    const searchTerms = this.getSearchTermsForAppType(appType);
    const cacheKey = this.getCacheKey(searchTerms);

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      console.log(`📸 PEXELS: Using cached images for ${appType}`);
      return cached.slice(0, count);
    }

    try {
      // Try each search term until we get results
      let photos: PexelsPhoto[] = [];
      for (const term of searchTerms) {
        try {
          photos = await this.fetchFromPexels(term, count * 2); // Fetch more for variety
          if (photos.length > 0) break;
        } catch (error) {
          console.warn(`📸 PEXELS: Failed to fetch for term "${term}":`, error);
          continue;
        }
      }

      if (photos.length === 0) {
        throw new Error('No images found for any search terms');
      }

      // Convert to cached format
      const cachedImages: CachedImage[] = photos.slice(0, count).map(photo => ({
        url: photo.src.medium,
        alt: photo.alt || `${appType} image`,
        photographer: photo.photographer,
        cached_at: Date.now(),
      }));

      // Cache the results
      this.cache.set(cacheKey, cachedImages);
      console.log(`📸 PEXELS: Cached ${cachedImages.length} images for ${appType}`);

      return cachedImages;
    } catch (error) {
      console.error(`📸 PEXELS: Error fetching images for ${appType}:`, error);
      
      // Return fallback placeholder images
      return this.getFallbackImages(appType, count);
    }
  }

  /**
   * Get fallback placeholder images when Pexels fails
   */
  private getFallbackImages(appType: string, count: number): CachedImage[] {
    const colors = ['6366f1', '8b5cf6', '06b6d4', '10b981', 'f59e0b', 'ef4444'];
    const images: CachedImage[] = [];

    for (let i = 0; i < count; i++) {
      const color = colors[i % colors.length];
      images.push({
        url: `https://placehold.co/800x600/${color}/ffffff?text=${encodeURIComponent(appType.charAt(0).toUpperCase() + appType.slice(1))}`,
        alt: `${appType} placeholder image`,
        photographer: 'Placeholder',
        cached_at: Date.now(),
      });
    }

    return images;
  }

  /**
   * Search for specific images by query
   */
  async searchImages(query: string, count: number = 5): Promise<CachedImage[]> {
    const cacheKey = this.getCacheKey([query]);

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      return cached.slice(0, count);
    }

    try {
      const photos = await this.fetchFromPexels(query, count);
      
      const cachedImages: CachedImage[] = photos.map(photo => ({
        url: photo.src.medium,
        alt: photo.alt || query,
        photographer: photo.photographer,
        cached_at: Date.now(),
      }));

      this.cache.set(cacheKey, cachedImages);
      return cachedImages;
    } catch (error) {
      console.error(`📸 PEXELS: Error searching for "${query}":`, error);
      return this.getFallbackImages(query, count);
    }
  }

  /**
   * Clear cache (useful for testing or memory management)
   */
  clearCache() {
    this.cache.clear();
    console.log('📸 PEXELS: Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const totalEntries = this.cache.size;
    const totalImages = Array.from(this.cache.values()).reduce((sum, images) => sum + images.length, 0);
    
    return {
      totalEntries,
      totalImages,
      cacheKeys: Array.from(this.cache.keys()),
    };
  }
}

// Export singleton instance
export const pexelsService = new PexelsService();

// Export types for use in components
export type { CachedImage, AppType };
export { APP_TYPE_SEARCH_TERMS };
