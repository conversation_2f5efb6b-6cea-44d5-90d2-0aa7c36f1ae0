import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';

/**
 * Super simple Supabase test - no complex logic, just basic operations
 */
export class SimpleSupabaseTest {

  /**
   * Test 1: Simple conversation insert (let database auto-generate UUID)
   */
  async testSimpleConversation(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const user = authState.get().user;
      if (!user) {
        return { success: false, message: 'No authenticated user' };
      }

      console.log('🧪 Testing simple conversation insert...');

      // Let database auto-generate the UUID
      const { data, error } = await supabase
        .from('chat_conversations')
        .insert({
          user_id: user.id,
          title: 'Simple Test Chat',
          description: 'Testing basic insert'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Simple conversation test failed:', error);
        return {
          success: false,
          message: `Failed: ${error.message}`,
          data: { error, code: error.code }
        };
      }

      console.log('✅ Simple conversation test passed:', data);
      return {
        success: true,
        message: `Success! Created conversation: ${data.id}`,
        data
      };
    } catch (error) {
      console.error('❌ Simple conversation test error:', error);
      return { success: false, message: `Error: ${error}` };
    }
  }

  /**
   * Test 2: Simple message insert
   */
  async testSimpleMessage(conversationId: string): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const user = authState.get().user;
      if (!user) {
        return { success: false, message: 'No authenticated user' };
      }

      console.log('🧪 Testing simple message insert...');

      // Simple message with basic required fields only
      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          conversation_id: conversationId,
          user_id: user.id,
          role: 'user',
          content: 'Hello, this is a simple test message',
          content_type: 'text'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Simple message test failed:', error);
        return {
          success: false,
          message: `Failed: ${error.message}`,
          data: { error, code: error.code }
        };
      }

      console.log('✅ Simple message test passed:', data);
      return {
        success: true,
        message: `Success! Created message: ${data.id}`,
        data
      };
    } catch (error) {
      console.error('❌ Simple message test error:', error);
      return { success: false, message: `Error: ${error}` };
    }
  }

  /**
   * Test 3: Simple usage tracking
   */
  async testSimpleUsageTracking(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const user = authState.get().user;
      if (!user) {
        return { success: false, message: 'No authenticated user' };
      }

      console.log('🧪 Testing simple usage tracking...');

      const today = new Date().toISOString().split('T')[0];

      // First, try to get existing record
      const { data: existing } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      let data, error;

      if (existing) {
        // Update existing record
        console.log('📝 Updating existing usage record...');
        const result = await supabase
          .from('usage_tracking')
          .update({
            messages_sent: (existing.messages_sent || 0) + 1,
            api_calls_made: (existing.api_calls_made || 0) + 1,
          })
          .eq('user_id', user.id)
          .eq('date', today)
          .select()
          .single();

        data = result.data;
        error = result.error;
      } else {
        // Insert new record
        console.log('➕ Creating new usage record...');
        const result = await supabase
          .from('usage_tracking')
          .insert({
            user_id: user.id,
            date: today,
            messages_sent: 1,
            tokens_used: 0,
            api_calls_made: 1,
            projects_created: 0,
            subscription_tier: 'free'
          })
          .select()
          .single();

        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('❌ Simple usage tracking test failed:', error);
        return {
          success: false,
          message: `Failed: ${error.message}`,
          data: { error, code: error.code }
        };
      }

      console.log('✅ Simple usage tracking test passed:', data);
      return {
        success: true,
        message: `Success! Updated usage tracking`,
        data
      };
    } catch (error) {
      console.error('❌ Simple usage tracking test error:', error);
      return { success: false, message: `Error: ${error}` };
    }
  }

  /**
   * Run all simple tests in sequence
   */
  async runAllSimpleTests(): Promise<{
    conversation: { success: boolean; message: string; data?: any };
    message: { success: boolean; message: string; data?: any };
    usage: { success: boolean; message: string; data?: any };
    overall: { success: boolean; message: string };
  }> {
    console.log('🚀 Running simple Supabase tests...');

    // Test 1: Conversation
    const conversation = await this.testSimpleConversation();

    // Test 2: Message (only if conversation succeeded)
    let message = { success: false, message: 'Skipped due to conversation failure' };
    if (conversation.success && conversation.data?.id) {
      message = await this.testSimpleMessage(conversation.data.id);
    }

    // Test 3: Usage tracking
    const usage = await this.testSimpleUsageTracking();

    // Overall result
    const overall = {
      success: conversation.success && message.success && usage.success,
      message: conversation.success && message.success && usage.success
        ? '🎉 All simple tests passed! Supabase is working correctly.'
        : '❌ Some tests failed. Check individual results above.'
    };

    console.log('📊 Simple test results:', { conversation, message, usage, overall });

    return { conversation, message, usage, overall };
  }

  /**
   * Clean up test data
   */
  async cleanupTestData(): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      console.log('🧹 Cleaning up test data...');

      // Delete test conversations (messages will be deleted automatically due to foreign key cascade)
      await supabase
        .from('chat_conversations')
        .delete()
        .eq('user_id', user.id)
        .like('title', '%Test%');

      console.log('✅ Test data cleaned up');
    } catch (error) {
      console.error('❌ Failed to cleanup test data:', error);
    }
  }
}

export const simpleSupabaseTest = new SimpleSupabaseTest();

// Expose simple test function globally
if (typeof window !== 'undefined') {
  (window as any).testSupabaseSimple = () => simpleSupabaseTest.runAllSimpleTests();
  (window as any).cleanupTestData = () => simpleSupabaseTest.cleanupTestData();
  (window as any).quickSupabaseTest = async () => {
    console.log('🚀 Running quick Supabase test...');
    const results = await simpleSupabaseTest.runAllSimpleTests();
    if (results.overall.success) {
      console.log('🎉 SUCCESS: Supabase is working perfectly!');
    } else {
      console.log('❌ FAILED: Check individual test results above');
    }
    return results;
  };
}
