import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';

/**
 * Manual cleanup utility for duplicate conversations
 * Run this in browser console: cleanupDuplicates()
 */
export async function cleanupDuplicates(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🧹 Starting manual duplicate cleanup...');

    // Get all conversations for the user
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select('id, title, description, created_at, message_count')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error || !conversations) {
      console.error('❌ Error fetching conversations:', error);
      return;
    }

    console.log(`📊 Found ${conversations.length} total conversations`);

    // Group by title to find duplicates
    const titleGroups = new Map<string, typeof conversations>();

    conversations.forEach(conv => {
      const title = conv.title || 'Untitled';
      if (!titleGroups.has(title)) {
        titleGroups.set(title, []);
      }
      titleGroups.get(title)!.push(conv);
    });

    // Find groups with duplicates
    const duplicateGroups = Array.from(titleGroups.entries())
      .filter(([_, convs]) => convs.length > 1);

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate conversations found');
      return;
    }

    console.log(`🔍 Found ${duplicateGroups.length} groups with duplicates:`);

    // Show duplicates before cleanup
    duplicateGroups.forEach(([title, convs]) => {
      console.log(`📝 "${title}": ${convs.length} duplicates`);
      convs.forEach((conv, index) => {
        console.log(`  ${index + 1}. ID: ${conv.id}, Created: ${conv.created_at}, Messages: ${conv.message_count || 0}`);
      });
    });

    // Ask for confirmation
    const confirmed = confirm(`Found ${duplicateGroups.length} groups with duplicates. Proceed with cleanup? This will keep the newest conversation in each group and delete the rest.`);

    if (!confirmed) {
      console.log('❌ Cleanup cancelled by user');
      return;
    }

    let totalDeleted = 0;

    // For each group, keep the newest and delete the rest
    for (const [title, convs] of duplicateGroups) {
      // Sort by created_at descending (newest first)
      convs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Keep the first (newest), delete the rest
      const toKeep = convs[0];
      const toDelete = convs.slice(1);

      if (toDelete.length > 0) {
        console.log(`🗑️ Keeping newest "${title}" (${toKeep.id}), deleting ${toDelete.length} duplicates`);

        const idsToDelete = toDelete.map(c => c.id);

        // Delete messages first (foreign key constraint)
        const { error: messagesError } = await supabase
          .from('chat_messages')
          .delete()
          .in('conversation_id', idsToDelete);

        if (messagesError) {
          console.warn(`⚠️ Error deleting messages for "${title}":`, messagesError);
        }

        // Delete conversations
        const { error: conversationsError } = await supabase
          .from('chat_conversations')
          .delete()
          .in('id', idsToDelete);

        if (conversationsError) {
          console.error(`❌ Error deleting conversations for "${title}":`, conversationsError);
        } else {
          totalDeleted += toDelete.length;
          console.log(`✅ Deleted ${toDelete.length} duplicates for "${title}"`);
        }
      }
    }

    console.log(`🎉 Cleanup completed! Deleted ${totalDeleted} duplicate conversations.`);

    // Show final count
    const { count: finalCount } = await supabase
      .from('chat_conversations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    console.log(`📊 Final conversation count: ${finalCount || 0}`);

  } catch (error) {
    console.error('❌ Failed to cleanup duplicates:', error);
  }
}

/**
 * Show current conversation statistics
 */
export async function showConversationStats(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select('id, title, created_at, message_count')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error || !conversations) {
      console.error('❌ Error fetching conversations:', error);
      return;
    }

    console.log(`📊 Conversation Statistics:`);
    console.log(`Total conversations: ${conversations.length}`);

    // Group by title to show duplicates
    const titleGroups = new Map<string, number>();
    conversations.forEach(conv => {
      const title = conv.title || 'Untitled';
      titleGroups.set(title, (titleGroups.get(title) || 0) + 1);
    });

    const duplicates = Array.from(titleGroups.entries()).filter(([_, count]) => count > 1);

    if (duplicates.length > 0) {
      console.log(`🔍 Duplicate titles found:`);
      duplicates.forEach(([title, count]) => {
        console.log(`  "${title}": ${count} conversations`);
      });
    } else {
      console.log(`✅ No duplicate titles found`);
    }

    // Show recent conversations
    console.log(`📝 Recent conversations:`);
    conversations.slice(0, 10).forEach((conv, index) => {
      console.log(`  ${index + 1}. "${conv.title}" (${conv.message_count || 0} messages) - ${conv.created_at}`);
    });

  } catch (error) {
    console.error('❌ Failed to show stats:', error);
  }
}

/**
 * Quick cleanup - automatically removes duplicates without confirmation
 * Use this for immediate cleanup after fixes are applied
 */
export async function quickCleanupDuplicates(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🚀 Starting quick duplicate cleanup...');

    // Get all conversations for the user
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select('id, title, description, created_at, message_count')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error || !conversations) {
      console.error('❌ Error fetching conversations:', error);
      return;
    }

    console.log(`📊 Found ${conversations.length} total conversations`);

    // Group by title to find duplicates
    const titleGroups = new Map<string, typeof conversations>();

    conversations.forEach(conv => {
      const title = conv.title || 'Untitled';
      if (!titleGroups.has(title)) {
        titleGroups.set(title, []);
      }
      titleGroups.get(title)!.push(conv);
    });

    // Find groups with duplicates
    const duplicateGroups = Array.from(titleGroups.entries())
      .filter(([_, convs]) => convs.length > 1);

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate conversations found');
      return;
    }

    console.log(`🔍 Found ${duplicateGroups.length} groups with duplicates - cleaning up automatically...`);

    let totalDeleted = 0;

    // For each group, keep the newest and delete the rest
    for (const [title, convs] of duplicateGroups) {
      // Sort by created_at descending (newest first)
      convs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Keep the first (newest), delete the rest
      const toKeep = convs[0];
      const toDelete = convs.slice(1);

      if (toDelete.length > 0) {
        console.log(`🗑️ Keeping newest "${title}" (${toKeep.id}), deleting ${toDelete.length} duplicates`);

        const idsToDelete = toDelete.map(c => c.id);

        // Delete messages first (foreign key constraint)
        await supabase
          .from('chat_messages')
          .delete()
          .in('conversation_id', idsToDelete);

        // Delete conversations
        await supabase
          .from('chat_conversations')
          .delete()
          .in('id', idsToDelete);

        totalDeleted += toDelete.length;
        console.log(`✅ Deleted ${toDelete.length} duplicates for "${title}"`);
      }
    }

    console.log(`🎉 Quick cleanup completed! Deleted ${totalDeleted} duplicate conversations.`);

    // Show final count
    const { count: finalCount } = await supabase
      .from('chat_conversations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    console.log(`📊 Final conversation count: ${finalCount || 0}`);

  } catch (error) {
    console.error('❌ Failed to cleanup duplicates:', error);
  }
}

/**
 * Complete cleanup - clears both Supabase and IndexedDB data
 * Use this for complete fresh start testing
 */
export async function completeCleanup(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    const confirmed = confirm('⚠️ This will DELETE ALL your chat data, projects, and usage history. This cannot be undone! Are you sure?');
    if (!confirmed) {
      console.log('❌ Cleanup cancelled');
      return;
    }

    console.log('🧹 Starting complete cleanup...');

    // 1. Clean Supabase data
    console.log('🗑️ Cleaning Supabase data...');

    // Delete chat messages
    await supabase.from('chat_messages').delete().eq('user_id', user.id);
    console.log('✅ Deleted chat messages');

    // Delete chat conversations
    await supabase.from('chat_conversations').delete().eq('user_id', user.id);
    console.log('✅ Deleted chat conversations');

    // Delete projects
    await supabase.from('projects').delete().eq('user_id', user.id);
    console.log('✅ Deleted projects');

    // Delete usage tracking
    await supabase.from('usage_tracking').delete().eq('user_id', user.id);
    console.log('✅ Deleted usage tracking');

    // Reset user profile
    await supabase
      .from('user_profiles')
      .update({
        daily_message_count: 0,
        total_messages_sent: 0,
        total_projects_created: 0,
      })
      .eq('id', user.id);
    console.log('✅ Reset user profile');

    // 2. Clean IndexedDB
    console.log('🗑️ Cleaning IndexedDB...');
    await cleanIndexedDB();

    // 3. Clean localStorage
    console.log('🗑️ Cleaning localStorage...');
    cleanLocalStorage();

    // 4. Update local state
    const currentState = authState.get();
    if (currentState.profile) {
      authState.set({
        ...currentState,
        profile: {
          ...currentState.profile,
          daily_message_count: 0,
          total_messages_sent: 0,
          total_projects_created: 0,
        },
        remainingMessages: currentState.profile.daily_message_limit || 10,
      });
    }

    console.log('🎉 Complete cleanup finished! Refresh the page to see clean state.');

    // Suggest page refresh
    const refresh = confirm('✅ Cleanup complete! Refresh the page now to see clean state?');
    if (refresh) {
      window.location.reload();
    }

  } catch (error) {
    console.error('❌ Failed to complete cleanup:', error);
  }
}

/**
 * Clean IndexedDB data
 */
async function cleanIndexedDB(): Promise<void> {
  try {
    // List of IndexedDB databases used by the app
    const dbNames = ['BoltAIDB', 'boltHistory', 'chats', 'workbench', 'files'];

    for (const dbName of dbNames) {
      try {
        // Delete the entire database
        const deleteReq = indexedDB.deleteDatabase(dbName);
        await new Promise((resolve, reject) => {
          deleteReq.onsuccess = () => {
            console.log(`✅ Deleted IndexedDB: ${dbName}`);
            resolve(true);
          };
          deleteReq.onerror = () => {
            console.warn(`⚠️ Could not delete IndexedDB: ${dbName}`);
            resolve(false); // Don't fail the whole cleanup
          };
          deleteReq.onblocked = () => {
            console.warn(`⚠️ IndexedDB deletion blocked: ${dbName} (close all tabs and try again)`);
            resolve(false);
          };
        });
      } catch (error) {
        console.warn(`⚠️ Error deleting IndexedDB ${dbName}:`, error);
      }
    }
  } catch (error) {
    console.error('❌ Error cleaning IndexedDB:', error);
  }
}

/**
 * Clean localStorage data
 */
function cleanLocalStorage(): void {
  try {
    // List of localStorage keys used by the app
    const keysToRemove = [
      'chatHistory',
      'workbenchFiles',
      'userProjects',
      'apiKeys',
      'selectedModel',
      'selectedProvider',
      'promptCache',
    ];

    // Remove specific keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // Remove any keys that start with common prefixes
    const prefixes = ['chat:', 'project:', 'snapshot:', 'bolt:', 'workbench:'];
    Object.keys(localStorage).forEach(key => {
      if (prefixes.some(prefix => key.startsWith(prefix))) {
        localStorage.removeItem(key);
      }
    });

    console.log('✅ Cleaned localStorage');
  } catch (error) {
    console.error('❌ Error cleaning localStorage:', error);
  }
}

/**
 * Reset today's usage tracking to zero
 * Use this to start fresh for testing
 */
export async function resetTodayUsage(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    const confirmed = confirm('⚠️ This will reset your usage count for today to 0. Are you sure?');
    if (!confirmed) {
      console.log('❌ Reset cancelled');
      return;
    }

    console.log('🔄 Resetting today\'s usage...');

    const today = new Date().toISOString().split('T')[0];

    // Reset usage tracking
    const { error: usageError } = await supabase
      .from('usage_tracking')
      .upsert({
        user_id: user.id,
        date: today,
        messages_sent: 0,
        tokens_used: 0,
        api_calls_made: 0,
        projects_created: 0,
        subscription_tier: 'free',
      }, {
        onConflict: 'user_id,date'
      });

    if (usageError) {
      console.error('❌ Error resetting usage tracking:', usageError);
      return;
    }

    // Reset user profile daily count
    const { error: profileError } = await supabase
      .from('user_profiles')
      .update({
        daily_message_count: 0,
      })
      .eq('id', user.id);

    if (profileError) {
      console.error('❌ Error resetting profile:', profileError);
      return;
    }

    // Update local state
    const currentState = authState.get();
    if (currentState.profile) {
      authState.set({
        ...currentState,
        profile: {
          ...currentState.profile,
          daily_message_count: 0,
        },
        remainingMessages: currentState.profile.daily_message_limit || 10,
      });
    }

    console.log('✅ Usage reset successfully! You can now test with a clean slate.');

  } catch (error) {
    console.error('❌ Failed to reset usage:', error);
  }
}

/**
 * Sync IndexedDB with Supabase - removes local chats that don't exist in cloud
 * This fixes the issue where deleted Supabase conversations still appear in sidebar
 */
export async function syncIndexedDBWithSupabase(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔄 Syncing IndexedDB with Supabase...');

    // Get all conversations from Supabase
    const { data: supabaseConversations, error } = await supabase
      .from('chat_conversations')
      .select('id, metadata')
      .eq('user_id', user.id);

    if (error) {
      console.error('❌ Error fetching Supabase conversations:', error);
      return;
    }

    // Create a set of valid conversation IDs from Supabase
    const validIds = new Set<string>();
    supabaseConversations?.forEach(conv => {
      validIds.add(conv.id);
      // Also add original_id and url_id from metadata if they exist
      if (conv.metadata?.original_id) validIds.add(conv.metadata.original_id);
      if (conv.metadata?.url_id) validIds.add(conv.metadata.url_id);
    });

    console.log(`📊 Found ${validIds.size} valid conversation IDs in Supabase`);

    // Open IndexedDB and get all local chats
    const { openDatabase } = await import('~/lib/persistence/db');
    const db = await openDatabase();

    if (!db) {
      console.error('❌ Could not open IndexedDB');
      return;
    }

    // Get all chats from IndexedDB
    const transaction = db.transaction('chats', 'readonly');
    const store = transaction.objectStore('chats');
    const getAllRequest = store.getAll();

    const localChats = await new Promise<any[]>((resolve, reject) => {
      getAllRequest.onsuccess = () => resolve(getAllRequest.result);
      getAllRequest.onerror = () => reject(getAllRequest.error);
    });

    console.log(`📊 Found ${localChats.length} chats in IndexedDB`);

    // Find chats that exist locally but not in Supabase
    const chatsToDelete = localChats.filter(chat => {
      return !validIds.has(chat.id) && !validIds.has(chat.urlId);
    });

    if (chatsToDelete.length === 0) {
      console.log('✅ IndexedDB is already in sync with Supabase');
      return;
    }

    console.log(`🗑️ Found ${chatsToDelete.length} orphaned chats to remove from IndexedDB`);

    // Delete orphaned chats from IndexedDB
    const deleteTransaction = db.transaction('chats', 'readwrite');
    const deleteStore = deleteTransaction.objectStore('chats');

    for (const chat of chatsToDelete) {
      try {
        await new Promise<void>((resolve, reject) => {
          const deleteRequest = deleteStore.delete(chat.id);
          deleteRequest.onsuccess = () => {
            console.log(`✅ Removed orphaned chat: ${chat.id} (${chat.description || 'Untitled'})`);
            resolve();
          };
          deleteRequest.onerror = () => reject(deleteRequest.error);
        });

        // Also remove snapshot from localStorage
        try {
          localStorage.removeItem(`snapshot:${chat.id}`);
          localStorage.removeItem(`snapshot:${chat.urlId}`);
        } catch (error) {
          // Ignore localStorage errors
        }
      } catch (error) {
        console.warn(`⚠️ Error deleting chat ${chat.id}:`, error);
      }
    }

    console.log(`🎉 Sync complete! Removed ${chatsToDelete.length} orphaned chats from IndexedDB`);
    console.log('💡 Refresh the page to see updated sidebar');

  } catch (error) {
    console.error('❌ Failed to sync IndexedDB with Supabase:', error);
  }
}

/**
 * COMPREHENSIVE PLATFORM DIAGNOSIS
 * Identifies all storage, sync, and counting issues
 */
export async function comprehensiveDiagnosis(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔍 COMPREHENSIVE PLATFORM DIAGNOSIS');
    console.log('=====================================');

    // 1. Check Authentication State
    console.log('👤 Authentication State:');
    const authData = authState.get();
    console.log(`  User ID: ${user.id}`);
    console.log(`  Email: ${user.email}`);
    console.log(`  Daily Messages: ${authData.profile?.daily_message_count || 0}`);
    console.log(`  Remaining: ${authData.remainingMessages || 0}`);
    console.log(`  Subscription: ${authData.profile?.subscription_tier || 'unknown'}`);

    // 2. Check IndexedDB
    console.log('\n📱 IndexedDB (Local Storage):');
    const { openDatabase } = await import('~/lib/persistence/db');
    const db = await openDatabase();

    let localChats: any[] = [];
    if (db) {
      const transaction = db.transaction('chats', 'readonly');
      const store = transaction.objectStore('chats');
      const getAllRequest = store.getAll();

      localChats = await new Promise<any[]>((resolve, reject) => {
        getAllRequest.onsuccess = () => resolve(getAllRequest.result);
        getAllRequest.onerror = () => reject(getAllRequest.error);
      });

      console.log(`  Total chats in IndexedDB: ${localChats.length}`);
      localChats.forEach((chat, index) => {
        console.log(`  ${index + 1}. ID: ${chat.id}`);
        console.log(`     Description: "${chat.description || 'No description'}"`);
        console.log(`     URL ID: ${chat.urlId || 'No URL ID'}`);
        console.log(`     Messages: ${chat.messages?.length || 0}`);
        console.log(`     Timestamp: ${chat.timestamp}`);
      });
    } else {
      console.log('  ❌ IndexedDB not available');
    }

    // 3. Check Supabase Conversations
    console.log('\n☁️ Supabase Conversations:');
    const { data: conversations, error: convError } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (convError) {
      console.error('  ❌ Error fetching conversations:', convError);
    } else {
      console.log(`  Total conversations: ${conversations?.length || 0}`);
      conversations?.forEach((conv, index) => {
        console.log(`  ${index + 1}. ID: ${conv.id}`);
        console.log(`     Title: "${conv.title}"`);
        console.log(`     Description: "${conv.description || 'No description'}"`);
        console.log(`     Message Count: ${conv.message_count || 0}`);
        console.log(`     Original ID: ${conv.metadata?.original_id || 'None'}`);
        console.log(`     URL ID: ${conv.metadata?.url_id || 'None'}`);
        console.log(`     Created: ${conv.created_at}`);
      });
    }

    // 4. Check Supabase Projects
    console.log('\n🏗️ Supabase Projects:');
    const { data: projects, error: projError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (projError) {
      console.error('  ❌ Error fetching projects:', projError);
    } else {
      console.log(`  Total projects: ${projects?.length || 0}`);
      projects?.forEach((proj, index) => {
        console.log(`  ${index + 1}. ID: ${proj.id}`);
        console.log(`     Name: "${proj.name}"`);
        console.log(`     Description: "${proj.description || 'No description'}"`);
        console.log(`     Conversation ID: ${proj.conversation_id || 'None'}`);
        console.log(`     Status: ${proj.status}`);
        console.log(`     Created: ${proj.created_at}`);
      });
    }

    // 5. Check Usage Tracking
    console.log('\n📊 Usage Tracking:');
    const today = new Date().toISOString().split('T')[0];
    const { data: todayUsage, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (usageError) {
      console.log('  ❌ No usage tracking record for today');
    } else {
      console.log(`  Messages sent: ${todayUsage.messages_sent || 0}`);
      console.log(`  Tokens used: ${todayUsage.tokens_used || 0}`);
      console.log(`  API calls: ${todayUsage.api_calls_made || 0}`);
      console.log(`  Projects created: ${todayUsage.projects_created || 0}`);
      console.log(`  Subscription tier: ${todayUsage.subscription_tier}`);
    }

    // 6. Check User Profile
    console.log('\n👤 User Profile (Database):');
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('  ❌ Error fetching profile:', profileError);
    } else {
      console.log(`  Daily message count: ${profile.daily_message_count || 0}`);
      console.log(`  Total messages sent: ${profile.total_messages_sent || 0}`);
      console.log(`  Total projects created: ${profile.total_projects_created || 0}`);
      console.log(`  Daily message limit: ${profile.daily_message_limit || 10}`);
      console.log(`  Subscription tier: ${profile.subscription_tier || 'free'}`);
    }

    // 7. ISSUE ANALYSIS
    console.log('\n🔍 ISSUE ANALYSIS:');
    console.log('==================');

    const issues: string[] = [];

    // Check IndexedDB vs Supabase sync
    if (localChats.length === 0 && (conversations?.length || 0) > 0) {
      issues.push('❌ CRITICAL: Supabase has conversations but IndexedDB is empty - sidebar will show "No previous conversations"');
    }

    // Check project counting
    const actualProjects = projects?.length || 0;
    const trackedProjects = todayUsage?.projects_created || 0;
    const profileProjects = profile?.total_projects_created || 0;

    if (actualProjects !== trackedProjects) {
      issues.push(`❌ PROJECT COUNT MISMATCH: Actual projects (${actualProjects}) ≠ Usage tracking (${trackedProjects})`);
    }

    // Check message counting
    const trackedMessages = todayUsage?.messages_sent || 0;
    const profileMessages = profile?.daily_message_count || 0;
    const authMessages = authData.profile?.daily_message_count || 0;

    if (trackedMessages !== profileMessages || profileMessages !== authMessages) {
      issues.push(`❌ MESSAGE COUNT MISMATCH: Usage (${trackedMessages}) ≠ Profile (${profileMessages}) ≠ Auth (${authMessages})`);
    }

    // Check conversation-project linking
    const conversationsWithProjects = conversations?.filter(c =>
      projects?.some(p => p.conversation_id === c.id)
    ).length || 0;

    if (actualProjects > conversationsWithProjects) {
      issues.push(`❌ ORPHANED PROJECTS: ${actualProjects - conversationsWithProjects} projects not linked to conversations`);
    }

    if (issues.length === 0) {
      console.log('✅ No critical issues detected');
    } else {
      console.log(`Found ${issues.length} critical issues:`);
      issues.forEach(issue => console.log(`  ${issue}`));
    }

    // 8. RECOMMENDATIONS
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('===================');

    if (localChats.length === 0 && (conversations?.length || 0) > 0) {
      console.log('1. Run: syncSupabaseToIndexedDB() - to populate sidebar');
    }

    if (issues.some(i => i.includes('COUNT MISMATCH'))) {
      console.log('2. Run: fixAllCounting() - to correct all count mismatches');
    }

    if (issues.some(i => i.includes('ORPHANED'))) {
      console.log('3. Run: fixProjectLinking() - to link orphaned projects');
    }

    console.log('\n🎯 Quick fixes available:');
    console.log('  - syncSupabaseToIndexedDB()');
    console.log('  - fixAllCounting()');
    console.log('  - fixProjectLinking()');

  } catch (error) {
    console.error('❌ Failed to run comprehensive diagnosis:', error);
  }
}

/**
 * Debug function to compare IndexedDB vs Supabase data
 * Helps identify sync issues between local and cloud storage
 */
export async function debugStorageSync(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔍 Debugging storage sync...');

    // Check IndexedDB
    const { openDatabase } = await import('~/lib/persistence/db');
    const db = await openDatabase();

    if (db) {
      const transaction = db.transaction('chats', 'readonly');
      const store = transaction.objectStore('chats');
      const getAllRequest = store.getAll();

      const localChats = await new Promise<any[]>((resolve, reject) => {
        getAllRequest.onsuccess = () => resolve(getAllRequest.result);
        getAllRequest.onerror = () => reject(getAllRequest.error);
      });

      console.log('📱 IndexedDB (Sidebar data):');
      console.log(`  Total chats: ${localChats.length}`);
      localChats.forEach(chat => {
        console.log(`  - ${chat.id}: "${chat.description}" (urlId: ${chat.urlId})`);
      });
    } else {
      console.log('📱 IndexedDB: Not available');
    }

    // Check Supabase conversations
    const { data: conversations, error: convError } = await supabase
      .from('chat_conversations')
      .select('id, title, description, metadata')
      .eq('user_id', user.id);

    if (convError) {
      console.error('❌ Error fetching conversations:', convError);
    } else {
      console.log('☁️ Supabase Conversations:');
      console.log(`  Total conversations: ${conversations?.length || 0}`);
      conversations?.forEach(conv => {
        console.log(`  - ${conv.id}: "${conv.title}" (original_id: ${conv.metadata?.original_id})`);
      });
    }

    // Check Supabase projects
    const { data: projects, error: projError } = await supabase
      .from('projects')
      .select('id, name, description, conversation_id')
      .eq('user_id', user.id);

    if (projError) {
      console.error('❌ Error fetching projects:', projError);
    } else {
      console.log('🏗️ Supabase Projects:');
      console.log(`  Total projects: ${projects?.length || 0}`);
      projects?.forEach(proj => {
        console.log(`  - ${proj.id}: "${proj.name}" (conversation_id: ${proj.conversation_id})`);
      });
    }

    // Summary
    const localCount = db ? (await new Promise<number>((resolve) => {
      const transaction = db.transaction('chats', 'readonly');
      const store = transaction.objectStore('chats');
      const countRequest = store.count();
      countRequest.onsuccess = () => resolve(countRequest.result);
      countRequest.onerror = () => resolve(0);
    })) : 0;

    console.log('📊 Sync Summary:');
    console.log(`  IndexedDB chats: ${localCount}`);
    console.log(`  Supabase conversations: ${conversations?.length || 0}`);
    console.log(`  Supabase projects: ${projects?.length || 0}`);

    if (localCount === 0 && (conversations?.length || 0) > 0) {
      console.log('⚠️ ISSUE: Supabase has data but IndexedDB is empty - sidebar will show "No previous conversations"');
      console.log('💡 Solution: Ensure conversations are saved to IndexedDB when created');
    }

  } catch (error) {
    console.error('❌ Failed to debug storage sync:', error);
  }
}

/**
 * Show current usage tracking stats
 */
export async function showUsageStats(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('📊 Usage Tracking Statistics:');

    // Get today's usage
    const today = new Date().toISOString().split('T')[0];
    const { data: todayUsage, error: todayError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (todayError) {
      console.log('📅 Today:', 'No usage record found');
    } else {
      console.log('📅 Today:', {
        messages_sent: todayUsage.messages_sent,
        tokens_used: todayUsage.tokens_used,
        api_calls_made: todayUsage.api_calls_made,
        subscription_tier: todayUsage.subscription_tier
      });
    }

    // Get recent usage (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    const weekAgoStr = weekAgo.toISOString().split('T')[0];

    const { data: recentUsage, error: recentError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .gte('date', weekAgoStr)
      .order('date', { ascending: false });

    if (recentError) {
      console.error('❌ Error fetching recent usage:', recentError);
    } else {
      console.log('📈 Recent usage (last 7 days):');
      recentUsage.forEach(usage => {
        console.log(`  ${usage.date}: ${usage.messages_sent} messages, ${usage.tokens_used} tokens`);
      });
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('daily_message_count, total_messages_sent, daily_message_limit, subscription_tier')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('❌ Error fetching profile:', profileError);
    } else {
      console.log('👤 User Profile:', {
        daily_message_count: profile.daily_message_count,
        total_messages_sent: profile.total_messages_sent,
        daily_message_limit: profile.daily_message_limit,
        subscription_tier: profile.subscription_tier,
        remaining_messages: profile.daily_message_limit - (profile.daily_message_count || 0)
      });
    }

  } catch (error) {
    console.error('❌ Failed to show usage stats:', error);
  }
}

/**
 * Sync Supabase conversations to IndexedDB to populate sidebar
 */
export async function syncSupabaseToIndexedDB(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔄 Syncing Supabase conversations to IndexedDB...');

    // Get conversations from Supabase
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching conversations:', error);
      return;
    }

    if (!conversations || conversations.length === 0) {
      console.log('ℹ️ No conversations to sync');
      return;
    }

    // Open IndexedDB
    const { openDatabase, setMessages } = await import('~/lib/persistence/db');
    const db = await openDatabase();

    if (!db) {
      console.error('❌ Could not open IndexedDB');
      return;
    }

    console.log(`📥 Syncing ${conversations.length} conversations to IndexedDB...`);

    for (const conv of conversations) {
      try {
        // Create a basic message structure for IndexedDB
        const basicMessages = [
          {
            id: '1',
            role: 'user',
            content: `Project: ${conv.title}`
          },
          {
            id: '2',
            role: 'assistant',
            content: conv.description || 'Project generated successfully'
          }
        ];

        // Save to IndexedDB with proper structure
        await setMessages(
          db,
          conv.metadata?.original_id || conv.id,
          basicMessages,
          conv.metadata?.url_id || conv.metadata?.original_id || conv.id,
          conv.title,
          conv.created_at
        );

        console.log(`✅ Synced: "${conv.title}"`);
      } catch (error) {
        console.warn(`⚠️ Failed to sync conversation ${conv.id}:`, error);
      }
    }

    console.log('🎉 Sync complete! Refresh the page to see conversations in sidebar.');

  } catch (error) {
    console.error('❌ Failed to sync Supabase to IndexedDB:', error);
  }
}

/**
 * Fix all counting mismatches across the platform
 */
export async function fixAllCounting(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔧 Fixing all counting mismatches...');

    const today = new Date().toISOString().split('T')[0];

    // Get actual counts from database
    const { data: conversations } = await supabase
      .from('chat_conversations')
      .select('id, message_count')
      .eq('user_id', user.id);

    const { data: projects } = await supabase
      .from('projects')
      .select('id')
      .eq('user_id', user.id);

    const actualConversations = conversations?.length || 0;
    const actualProjects = projects?.length || 0;
    const totalMessages = conversations?.reduce((sum, conv) => sum + (conv.message_count || 0), 0) || 0;

    console.log(`📊 Actual counts: ${actualConversations} conversations, ${actualProjects} projects, ${totalMessages} total messages`);

    // Fix usage tracking
    const { error: usageError } = await supabase
      .from('usage_tracking')
      .upsert({
        user_id: user.id,
        date: today,
        messages_sent: totalMessages,
        tokens_used: 0, // Keep existing tokens
        api_calls_made: totalMessages,
        projects_created: actualProjects,
        subscription_tier: 'free',
      }, {
        onConflict: 'user_id,date'
      });

    if (usageError) {
      console.error('❌ Error fixing usage tracking:', usageError);
    } else {
      console.log('✅ Fixed usage tracking');
    }

    // Fix user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .update({
        daily_message_count: totalMessages,
        total_messages_sent: totalMessages,
        total_projects_created: actualProjects,
      })
      .eq('id', user.id);

    if (profileError) {
      console.error('❌ Error fixing user profile:', profileError);
    } else {
      console.log('✅ Fixed user profile');
    }

    // Fix local auth state
    const currentState = authState.get();
    if (currentState.profile) {
      authState.set({
        ...currentState,
        profile: {
          ...currentState.profile,
          daily_message_count: totalMessages,
          total_messages_sent: totalMessages,
          total_projects_created: actualProjects,
        },
        remainingMessages: Math.max(0, (currentState.profile.daily_message_limit || 10) - totalMessages),
      });
      console.log('✅ Fixed local auth state');
    }

    console.log('🎉 All counting fixed! Counts should now be consistent.');

  } catch (error) {
    console.error('❌ Failed to fix counting:', error);
  }
}

/**
 * Fix project linking issues
 */
export async function fixProjectLinking(): Promise<void> {
  try {
    const user = authState.get().user;
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }

    console.log('🔗 Fixing project linking...');

    // Get orphaned projects (projects without conversation_id)
    const { data: orphanedProjects } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', user.id)
      .is('conversation_id', null);

    if (!orphanedProjects || orphanedProjects.length === 0) {
      console.log('✅ No orphaned projects found');
      return;
    }

    console.log(`🔧 Found ${orphanedProjects.length} orphaned projects`);

    for (const project of orphanedProjects) {
      try {
        // Try to find a matching conversation by name/title
        const { data: matchingConv } = await supabase
          .from('chat_conversations')
          .select('id')
          .eq('user_id', user.id)
          .ilike('title', `%${project.name}%`)
          .limit(1)
          .single();

        if (matchingConv) {
          // Link the project to the conversation
          await supabase
            .from('projects')
            .update({ conversation_id: matchingConv.id })
            .eq('id', project.id);

          console.log(`✅ Linked project "${project.name}" to conversation ${matchingConv.id}`);
        } else {
          console.log(`⚠️ No matching conversation found for project "${project.name}"`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to link project ${project.id}:`, error);
      }
    }

    console.log('🎉 Project linking complete!');

  } catch (error) {
    console.error('❌ Failed to fix project linking:', error);
  }
}

/**
 * NUCLEAR RESET - Clears everything and logs out user
 * WARNING: This will delete ALL user data from all storage systems
 */
export async function nuclearReset(): Promise<void> {
  try {
    const user = authState.get().user;
    const userId = user?.id;

    console.log('💥 NUCLEAR RESET INITIATED');
    console.log('⚠️ WARNING: This will delete ALL user data!');
    console.log('=====================================');

    // Step 1: Clear Supabase database tables (if user is authenticated)
    if (userId) {
      console.log('🗄️ Clearing Supabase database tables...');

      try {
        // Delete chat messages
        const { error: messagesError } = await supabase
          .from('chat_messages')
          .delete()
          .eq('user_id', userId);

        if (messagesError) {
          console.warn('⚠️ Error deleting chat messages:', messagesError);
        } else {
          console.log('✅ Deleted chat messages');
        }

        // Delete chat conversations
        const { error: conversationsError } = await supabase
          .from('chat_conversations')
          .delete()
          .eq('user_id', userId);

        if (conversationsError) {
          console.warn('⚠️ Error deleting conversations:', conversationsError);
        } else {
          console.log('✅ Deleted chat conversations');
        }

        // Delete projects
        const { error: projectsError } = await supabase
          .from('projects')
          .delete()
          .eq('user_id', userId);

        if (projectsError) {
          console.warn('⚠️ Error deleting projects:', projectsError);
        } else {
          console.log('✅ Deleted projects');
        }

        // Delete usage tracking
        const { error: usageError } = await supabase
          .from('usage_tracking')
          .delete()
          .eq('user_id', userId);

        if (usageError) {
          console.warn('⚠️ Error deleting usage tracking:', usageError);
        } else {
          console.log('✅ Deleted usage tracking');
        }

        // Reset user profile (don't delete, just reset counters)
        const { error: profileError } = await supabase
          .from('user_profiles')
          .update({
            daily_message_count: 0,
            total_messages_sent: 0,
            total_projects_created: 0,
          })
          .eq('id', userId);

        if (profileError) {
          console.warn('⚠️ Error resetting user profile:', profileError);
        } else {
          console.log('✅ Reset user profile counters');
        }

        // Delete API keys
        const { error: apiKeysError } = await supabase
          .from('user_api_keys')
          .delete()
          .eq('user_id', userId);

        if (apiKeysError) {
          console.warn('⚠️ Error deleting API keys:', apiKeysError);
        } else {
          console.log('✅ Deleted API keys');
        }

      } catch (error) {
        console.error('❌ Error clearing Supabase data:', error);
      }
    }

    // Step 2: Clear IndexedDB
    console.log('📱 Clearing IndexedDB...');
    try {
      const { openDatabase } = await import('~/lib/persistence/db');
      const db = await openDatabase();

      if (db) {
        const transaction = db.transaction(['chats'], 'readwrite');
        const store = transaction.objectStore('chats');
        await store.clear();
        console.log('✅ Cleared IndexedDB');
      } else {
        console.log('⚠️ IndexedDB not available');
      }
    } catch (error) {
      console.error('❌ Error clearing IndexedDB:', error);
    }

    // Step 3: Clear localStorage
    console.log('💾 Clearing localStorage...');
    try {
      const keysToKeep = ['theme']; // Keep theme preference
      const allKeys = Object.keys(localStorage);

      allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
          localStorage.removeItem(key);
        }
      });

      console.log('✅ Cleared localStorage (kept theme)');
    } catch (error) {
      console.error('❌ Error clearing localStorage:', error);
    }

    // Step 4: Clear sessionStorage
    console.log('🔄 Clearing sessionStorage...');
    try {
      sessionStorage.clear();
      console.log('✅ Cleared sessionStorage');
    } catch (error) {
      console.error('❌ Error clearing sessionStorage:', error);
    }

    // Step 5: Clear cookies
    console.log('🍪 Clearing cookies...');
    try {
      // Get all cookies
      const cookies = document.cookie.split(';');

      // Clear each cookie
      cookies.forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

        if (name) {
          // Clear for current domain
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          // Clear for parent domain
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
          // Clear for all subdomains
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
        }
      });

      console.log('✅ Cleared cookies');
    } catch (error) {
      console.error('❌ Error clearing cookies:', error);
    }

    // Step 6: Clear auth state
    console.log('🔐 Clearing auth state...');
    try {
      authState.set({
        user: null,
        session: null,
        profile: null,
        isLoading: false,
        isAuthenticated: false,
        remainingMessages: 0,
      });
      console.log('✅ Cleared auth state');
    } catch (error) {
      console.error('❌ Error clearing auth state:', error);
    }

    // Step 7: Sign out from Supabase
    console.log('🚪 Signing out from Supabase...');
    try {
      await supabase.auth.signOut();
      console.log('✅ Signed out from Supabase');
    } catch (error) {
      console.error('❌ Error signing out from Supabase:', error);
    }

    console.log('');
    console.log('💥 NUCLEAR RESET COMPLETE!');
    console.log('=====================================');
    console.log('✅ All data cleared successfully');
    console.log('✅ User logged out');
    console.log('🔄 Redirecting to homepage...');

    // Step 8: Redirect to homepage after a short delay
    setTimeout(() => {
      window.location.href = '/';
    }, 2000);

  } catch (error) {
    console.error('💥 NUCLEAR RESET FAILED:', error);
    throw error;
  }
}

// Make functions available globally for console use
if (typeof window !== 'undefined') {
  (window as any).cleanupDuplicates = cleanupDuplicates;
  (window as any).showConversationStats = showConversationStats;
  (window as any).quickCleanupDuplicates = quickCleanupDuplicates;
  (window as any).showUsageStats = showUsageStats;
  (window as any).resetTodayUsage = resetTodayUsage;
  (window as any).completeCleanup = completeCleanup;
  (window as any).syncIndexedDBWithSupabase = syncIndexedDBWithSupabase;
  (window as any).debugStorageSync = debugStorageSync;
  (window as any).comprehensiveDiagnosis = comprehensiveDiagnosis;
  (window as any).syncSupabaseToIndexedDB = syncSupabaseToIndexedDB;
  (window as any).fixAllCounting = fixAllCounting;
  (window as any).fixProjectLinking = fixProjectLinking;
  (window as any).nuclearReset = nuclearReset;

  // Make security and integrity functions available
  import('~/lib/sync/dataIntegrity').then(module => {
    (window as any).verifyDataIntegrity = module.verifyDataIntegrity;
    (window as any).autoHealDataInconsistencies = module.autoHealDataInconsistencies;
    (window as any).syncConversationsWithIntegrity = module.syncConversationsWithIntegrity;
  }).catch(() => {
    console.warn('Data integrity functions not available');
  });
}
