import { supabase } from '~/lib/supabase/client';
import { authState } from '~/lib/stores/user';
import type { Message } from 'ai';
import type { IChatMetadata } from '~/lib/persistence/db';
import { generateId } from 'ai';

// Smart storage limits to prevent database bloat
const STORAGE_LIMITS = {
  MAX_MESSAGE_SIZE: 50000, // 50KB per message
  MAX_CONVERSATION_MESSAGES: 100, // Keep only last 100 messages per conversation
  MAX_TOTAL_CONVERSATIONS: 50, // Keep only 50 conversations per user
  IMPORTANT_MESSAGE_THRESHOLD: 1000, // Messages over 1KB are considered important

  // File content filtering
  EXCLUDE_FILE_EXTENSIONS: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf'],
  MAX_FILE_SIZE: 100000, // 100KB max file size to store
};

interface OptimizedMessage {
  role: string;
  content: string;
  content_type: 'text' | 'code' | 'image' | 'file' | 'important' | 'summary';
  tokens_used?: number;
  model_used?: string;
  original_size?: number;
  is_important: boolean;
}

export class ChatStorageService {
  // Cache to map local IDs to Supabase UUIDs to prevent duplicates
  private static idMappingCache = new Map<string, string>();

  // Debouncing to prevent rapid duplicate saves
  private static saveTimeouts = new Map<string, NodeJS.Timeout>();
  private static lastSaveAttempt = new Map<string, number>();

  /**
   * Generates a valid UUID for database operations
   */
  private generateUUID(): string {
    // Use crypto.randomUUID if available (modern browsers)
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }

    // Fallback: generate UUID v4 manually
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Checks if a string is a valid UUID
   */
  private isValidUUID(id: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  /**
   * Gets or creates a consistent UUID for a conversation ID
   * This prevents duplicate conversations by maintaining ID mapping
   */
  private async getConsistentUUID(originalId: string, userId: string): Promise<string> {
    // If it's already a valid UUID, use it
    if (this.isValidUUID(originalId)) {
      return originalId;
    }

    // Check cache first
    const cacheKey = `${userId}:${originalId}`;
    if (ChatStorageService.idMappingCache.has(cacheKey)) {
      return ChatStorageService.idMappingCache.get(cacheKey)!;
    }

    // Check if conversation already exists in database with this original ID
    try {
      // Get all conversations for this user and check metadata in JavaScript
      // This avoids complex JSON query syntax issues
      const { data: conversations } = await supabase
        .from('chat_conversations')
        .select('id, metadata')
        .eq('user_id', userId);

      if (conversations) {
        const existing = conversations.find(conv => {
          const metadata = conv.metadata || {};
          return metadata.original_id === originalId || metadata.url_id === originalId;
        });

        if (existing) {
          // Found existing conversation, cache and return its UUID
          ChatStorageService.idMappingCache.set(cacheKey, existing.id);
          console.log(`🔄 Found existing conversation: "${originalId}" → "${existing.id}"`);
          return existing.id;
        }
      }
    } catch (error) {
      console.warn('⚠️ Error checking existing conversations:', error);
      // Continue to create new UUID
    }

    // Generate new UUID and cache it
    const newUUID = this.generateUUID();
    ChatStorageService.idMappingCache.set(cacheKey, newUUID);
    console.log(`🆕 Created new UUID mapping: "${originalId}" → "${newUUID}"`);
    return newUUID;
  }

  /**
   * Determines if a message is important enough to store in database
   */
  private isImportantMessage(message: Message): boolean {
    const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);

    // Always store user messages
    if (message.role === 'user') return true;

    // Store assistant messages that contain code or are substantial
    if (message.role === 'assistant') {
      return (
        content.length > STORAGE_LIMITS.IMPORTANT_MESSAGE_THRESHOLD ||
        content.includes('```') || // Contains code blocks
        content.includes('boltArtifact') || // Contains artifacts
        content.includes('project') || // Project-related
        content.includes('error') || // Error messages
        content.includes('install') || // Installation commands
        content.includes('npm') || content.includes('yarn') || content.includes('pip') // Package management
      );
    }

    return false;
  }

  /**
   * Optimizes message content for database storage
   */
  private optimizeMessage(message: Message): OptimizedMessage {
    const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
    const isImportant = this.isImportantMessage(message);

    let optimizedContent = content;
    let contentType: 'text' | 'code' | 'image' | 'file' | 'important' | 'summary' = 'text';

    // Determine content type based on content
    if (content.includes('```')) {
      contentType = 'code';
    } else if (content.includes('![') || content.includes('<img')) {
      contentType = 'image';
    } else if (content.includes('boltArtifact') || content.includes('file:')) {
      contentType = 'file';
    } else if (isImportant) {
      contentType = 'important';
    } else {
      contentType = 'text';
    }

    // If message is too large, create summary for non-important messages
    if (content.length > STORAGE_LIMITS.MAX_MESSAGE_SIZE && !isImportant) {
      optimizedContent = this.createMessageSummary(content);
      contentType = 'summary';
    }

    return {
      role: message.role,
      content: optimizedContent,
      content_type: contentType,
      tokens_used: (message as any).usage?.totalTokens || 0,
      model_used: this.extractModelFromContent(content),
      original_size: content.length,
      is_important: isImportant,
    };
  }

  /**
   * Creates a summary of large message content
   */
  private createMessageSummary(content: string): string {
    // Extract key information for summary
    const lines = content.split('\n');
    const summary = [];

    // Keep first few lines
    summary.push(...lines.slice(0, 3));

    // Extract code blocks
    const codeBlocks = content.match(/```[\s\S]*?```/g);
    if (codeBlocks && codeBlocks.length > 0) {
      summary.push(`\n[Contains ${codeBlocks.length} code block(s)]`);
    }

    // Extract file operations
    const fileOps = content.match(/\b(created|updated|deleted|modified)\s+[\w\/\.-]+/gi);
    if (fileOps && fileOps.length > 0) {
      summary.push(`\n[File operations: ${fileOps.slice(0, 3).join(', ')}]`);
    }

    // Add truncation notice
    summary.push(`\n\n[Message truncated - Original size: ${content.length} chars]`);

    return summary.join('\n').substring(0, 2000); // Limit summary to 2KB
  }

  /**
   * Extracts model information from message content
   */
  private extractModelFromContent(content: string): string {
    const modelMatch = content.match(/\[Model:\s*([^\]]+)\]/);
    return modelMatch ? modelMatch[1].trim() : '';
  }

  /**
   * Debounced save to prevent rapid duplicate saves
   */
  private async debouncedSave<T>(
    key: string,
    saveFunction: () => Promise<T>,
    debounceMs: number = 1000
  ): Promise<T | null> {
    const now = Date.now();
    const lastAttempt = ChatStorageService.lastSaveAttempt.get(key) || 0;

    // If we just saved recently, skip this save
    if (now - lastAttempt < debounceMs) {
      console.log(`⏭️ Skipping duplicate save for ${key} (too recent)`);
      return null;
    }

    // Clear any existing timeout
    const existingTimeout = ChatStorageService.saveTimeouts.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Update last attempt time
    ChatStorageService.lastSaveAttempt.set(key, now);

    // Execute the save function
    try {
      return await saveFunction();
    } catch (error) {
      console.error(`❌ Debounced save failed for ${key}:`, error);
      return null;
    }
  }

  /**
   * Saves or updates a conversation (DISABLED - IndexedDB only mode)
   */
  async saveConversation(
    conversationId: string,
    title: string,
    description?: string,
    projectType?: string,
    metadata?: IChatMetadata
  ): Promise<string | null> {
    // DISABLED: Chat storage disabled for IndexedDB-only mode
    console.log('💾 Chat storage disabled - using IndexedDB only mode');
    console.log('📝 Conversation would have been saved:', { conversationId, title, description });

    // Return the original ID to maintain compatibility
    return conversationId;
  }

  /**
   * Saves important messages (DISABLED - IndexedDB only mode)
   */
  async saveMessages(conversationId: string, messages: Message[]): Promise<boolean> {
    // DISABLED: Message storage disabled for IndexedDB-only mode
    console.log('💬 Message storage disabled - using IndexedDB only mode');
    console.log('📝 Messages would have been saved:', { conversationId, messageCount: messages.length });

    // Return success to maintain compatibility
    return true;
  }

  /**
   * Updates only token usage without incrementing message count
   * Used when message count was already tracked elsewhere
   */
  async updateTokenUsage(tokensUsed: number = 0, modelUsed: string = ''): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      console.log('🔢 Updating token usage only:', { tokensUsed, modelUsed, user: user.id });

      const today = new Date().toISOString().split('T')[0];

      // Only update tokens, don't touch message count
      const { data: existing } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      if (existing) {
        // Update existing record - only tokens
        const { error } = await supabase
          .from('usage_tracking')
          .update({
            tokens_used: (existing.tokens_used || 0) + tokensUsed,
          })
          .eq('user_id', user.id)
          .eq('date', today);

        if (error) {
          console.error('❌ Error updating token usage:', error);
        } else {
          console.log('✅ Token usage updated successfully');
        }
      } else {
        console.warn('⚠️ No existing usage record found for token update');
      }
    } catch (error) {
      console.error('❌ Failed to update token usage:', error);
    }
  }

  /**
   * Tracks usage statistics (full tracking with message count) - DEPRECATED
   * Use userActions.trackMessageUsage() instead for atomic updates
   */
  async trackUsage(tokensUsed: number = 0, modelUsed: string = '', skipLocalUpdate: boolean = false): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      console.log('📊 CHAT_STORAGE: Using centralized tracking for atomic updates...');

      // Use the centralized tracking function for consistency
      const { userActions } = await import('~/lib/stores/user');
      await userActions.trackMessageUsage(user.id, tokensUsed, modelUsed);

      console.log('✅ CHAT_STORAGE: Usage tracked successfully');
    } catch (error) {
      console.error('❌ Failed to track usage:', error);
    }
  }

  /**
   * Cleans up duplicate and old conversations to stay within limits
   */
  async cleanupOldConversations(): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      console.log('🧹 Starting conversation cleanup...');

      // First, cleanup duplicates
      await this.cleanupDuplicateConversations(user.id);

      // Get user's conversation count after duplicate cleanup
      const { count } = await supabase
        .from('chat_conversations')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      console.log(`📊 Found ${count || 0} conversations after duplicate cleanup`);

      if (!count || count <= STORAGE_LIMITS.MAX_TOTAL_CONVERSATIONS) {
        console.log('ℹ️ No cleanup needed - within limits');
        return; // Within limits
      }

      // Get oldest conversations to delete
      const { data: oldConversations } = await supabase
        .from('chat_conversations')
        .select('id')
        .eq('user_id', user.id)
        .order('last_message_at', { ascending: true })
        .limit(count - STORAGE_LIMITS.MAX_TOTAL_CONVERSATIONS);

      if (oldConversations && oldConversations.length > 0) {
        const idsToDelete = oldConversations.map(conv => conv.id);

        console.log(`🗑️ Deleting ${idsToDelete.length} old conversations`);

        // Delete messages first (due to foreign key constraint)
        await supabase
          .from('chat_messages')
          .delete()
          .in('conversation_id', idsToDelete);

        // Delete conversations
        await supabase
          .from('chat_conversations')
          .delete()
          .in('id', idsToDelete);

        console.log(`✅ Cleaned up ${idsToDelete.length} old conversations`);
      }
    } catch (error) {
      console.error('❌ Failed to cleanup old conversations:', error);
    }
  }

  /**
   * Cleanup duplicate conversations with same title/metadata
   */
  private async cleanupDuplicateConversations(userId: string): Promise<void> {
    try {
      console.log('🔍 Checking for duplicate conversations...');

      // Find conversations with same title and similar metadata
      const { data: conversations, error } = await supabase
        .from('chat_conversations')
        .select('id, title, metadata, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error || !conversations) {
        console.warn('⚠️ Could not fetch conversations for duplicate cleanup:', error);
        return;
      }

      // Group by title to find duplicates
      const titleGroups = new Map<string, typeof conversations>();

      conversations.forEach(conv => {
        const title = conv.title || 'Untitled';
        if (!titleGroups.has(title)) {
          titleGroups.set(title, []);
        }
        titleGroups.get(title)!.push(conv);
      });

      // Find groups with duplicates
      const duplicateGroups = Array.from(titleGroups.entries())
        .filter(([_, convs]) => convs.length > 1);

      if (duplicateGroups.length === 0) {
        console.log('✅ No duplicate conversations found');
        return;
      }

      console.log(`🔍 Found ${duplicateGroups.length} groups with duplicates`);

      // For each group, keep the newest and delete the rest
      for (const [title, convs] of duplicateGroups) {
        // Sort by created_at descending (newest first)
        convs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        // Keep the first (newest), delete the rest
        const toDelete = convs.slice(1);

        if (toDelete.length > 0) {
          console.log(`🗑️ Deleting ${toDelete.length} duplicate conversations for "${title}"`);

          const idsToDelete = toDelete.map(c => c.id);

          // Delete messages first
          await supabase
            .from('chat_messages')
            .delete()
            .in('conversation_id', idsToDelete);

          // Delete conversations
          await supabase
            .from('chat_conversations')
            .delete()
            .in('id', idsToDelete);
        }
      }

      console.log('✅ Duplicate cleanup completed');
    } catch (error) {
      console.error('❌ Failed to cleanup duplicates:', error);
    }
  }
}

export const chatStorageService = new ChatStorageService();
