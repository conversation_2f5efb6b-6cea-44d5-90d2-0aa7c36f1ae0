import { chatStorageService } from './chatStorageService';
import { projectStorageService } from './projectStorageService';
import { authState } from '~/lib/stores/user';
import type { Message } from 'ai';

/**
 * Test service to verify Supabase storage is working correctly
 */
export class StorageTestService {
  
  /**
   * Tests chat storage functionality
   */
  async testChatStorage(): Promise<{ success: boolean; message: string }> {
    try {
      const user = authState.get().user;
      if (!user) {
        return { success: false, message: 'No authenticated user' };
      }

      // Test conversation creation
      const testConversationId = `test-${Date.now()}`;
      const conversationId = await chatStorageService.saveConversation(
        testConversationId,
        'Test Conversation',
        'Testing chat storage functionality',
        'web'
      );

      if (!conversationId) {
        return { success: false, message: 'Failed to create test conversation' };
      }

      // Test message storage
      const testMessages: Message[] = [
        {
          id: '1',
          role: 'user',
          content: 'Hello, this is a test message'
        },
        {
          id: '2',
          role: 'assistant',
          content: 'This is a test response with some code:\n```javascript\nconsole.log("Hello World");\n```'
        }
      ];

      const messagesSaved = await chatStorageService.saveMessages(conversationId, testMessages);
      if (!messagesSaved) {
        return { success: false, message: 'Failed to save test messages' };
      }

      // Test usage tracking
      await chatStorageService.trackUsage(100, 'gpt-4');

      return { success: true, message: 'Chat storage test completed successfully' };
    } catch (error) {
      console.error('Chat storage test failed:', error);
      return { success: false, message: `Chat storage test failed: ${error}` };
    }
  }

  /**
   * Tests project storage functionality
   */
  async testProjectStorage(): Promise<{ success: boolean; message: string }> {
    try {
      const user = authState.get().user;
      if (!user) {
        return { success: false, message: 'No authenticated user' };
      }

      // Create test file structure
      const testFiles = {
        'package.json': {
          type: 'file' as const,
          content: JSON.stringify({
            name: 'test-project',
            version: '1.0.0',
            dependencies: {
              react: '^18.0.0',
              typescript: '^4.0.0'
            },
            scripts: {
              start: 'npm run dev',
              build: 'npm run build'
            }
          }, null, 2)
        },
        'src/App.tsx': {
          type: 'file' as const,
          content: `import React from 'react';

function App() {
  return (
    <div className="App">
      <h1>Test Project</h1>
      <p>This is a test project for storage verification.</p>
    </div>
  );
}

export default App;`
        },
        'src/index.css': {
          type: 'file' as const,
          content: `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.App {
  text-align: center;
  padding: 20px;
}`
        },
        '.env': {
          type: 'file' as const,
          content: 'REACT_APP_API_URL=https://api.example.com\nREACT_APP_VERSION=1.0.0'
        },
        'README.md': {
          type: 'file' as const,
          content: '# Test Project\n\nThis is a test project for verifying storage functionality.'
        }
      };

      // Test project creation
      const projectId = await projectStorageService.saveProject(
        `test-conv-${Date.now()}`,
        'Test Project',
        testFiles,
        'A test project to verify storage functionality'
      );

      if (!projectId) {
        return { success: false, message: 'Failed to create test project' };
      }

      // Test project update
      const updatedFiles = {
        ...testFiles,
        'src/components/TestComponent.tsx': {
          type: 'file' as const,
          content: `import React from 'react';

export const TestComponent: React.FC = () => {
  return <div>Updated test component</div>;
};`
        }
      };

      const updateSuccess = await projectStorageService.updateProject(
        projectId,
        updatedFiles,
        { description: 'Updated test project description' }
      );

      if (!updateSuccess) {
        return { success: false, message: 'Failed to update test project' };
      }

      return { success: true, message: 'Project storage test completed successfully' };
    } catch (error) {
      console.error('Project storage test failed:', error);
      return { success: false, message: `Project storage test failed: ${error}` };
    }
  }

  /**
   * Runs all storage tests
   */
  async runAllTests(): Promise<{ 
    chatTest: { success: boolean; message: string };
    projectTest: { success: boolean; message: string };
    overall: { success: boolean; message: string };
  }> {
    console.log('🧪 Running storage system tests...');

    const chatTest = await this.testChatStorage();
    console.log('📝 Chat storage test:', chatTest);

    const projectTest = await this.testProjectStorage();
    console.log('📁 Project storage test:', projectTest);

    const overall = {
      success: chatTest.success && projectTest.success,
      message: chatTest.success && projectTest.success 
        ? 'All storage tests passed successfully!' 
        : 'Some storage tests failed. Check individual test results.'
    };

    console.log('🎯 Overall test result:', overall);

    return { chatTest, projectTest, overall };
  }

  /**
   * Cleans up test data (optional)
   */
  async cleanupTestData(): Promise<void> {
    try {
      const user = authState.get().user;
      if (!user) return;

      // Note: In a real implementation, you might want to add cleanup methods
      // to the storage services to remove test data
      console.log('🧹 Test data cleanup would go here');
    } catch (error) {
      console.error('Failed to cleanup test data:', error);
    }
  }
}

export const storageTestService = new StorageTestService();

// Expose test function globally for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testStorage = () => storageTestService.runAllTests();
}
