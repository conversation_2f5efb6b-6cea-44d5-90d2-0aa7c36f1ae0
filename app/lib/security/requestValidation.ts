import { z } from 'zod';

/**
 * Enterprise Request Validation & Rate Limiting
 * Prevents abuse, injection attacks, and server crashes
 */

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; resetTime: number; blocked: boolean }>();

// Request validation schemas
export const chatRequestSchema = z.object({
  message: z.string()
    .min(1, 'Message cannot be empty')
    .max(50000, 'Message too long')
    .refine(val => !containsMaliciousContent(val), 'Message contains prohibited content'),

  model: z.string()
    .min(1, 'Model is required')
    .max(100, 'Model name too long')
    .regex(/^[a-zA-Z0-9\-_.\/]+$/, 'Invalid model format'),

  provider: z.string()
    .min(1, 'Provider is required')
    .max(50, 'Provider name too long')
    .regex(/^[a-zA-Z0-9\-_]+$/, 'Invalid provider format'),

  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(100000).optional(),

  files: z.array(z.object({
    name: z.string().max(255),
    size: z.number().max(50 * 1024 * 1024), // 50MB max
    type: z.string().regex(/^[a-zA-Z0-9\/\-_.]+$/),
  })).max(10).optional(),
});

export const projectRequestSchema = z.object({
  name: z.string()
    .min(1, 'Project name is required')
    .max(100, 'Project name too long')
    .regex(/^[a-zA-Z0-9\s\-_.]+$/, 'Invalid project name format'),

  description: z.string()
    .max(1000, 'Description too long')
    .optional(),

  template: z.string()
    .max(50, 'Template name too long')
    .regex(/^[a-zA-Z0-9\-_]+$/, 'Invalid template format')
    .optional(),
});

// Malicious content detection
const maliciousPatterns = [
  // Script injection
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /onload\s*=/gi,
  /onerror\s*=/gi,

  // SQL injection patterns
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
  /(--|\/\*|\*\/|;)/g,

  // Command injection
  /(\||&|;|\$\(|\`)/g,
  /(rm\s+-rf|del\s+\/|format\s+c:)/gi,

  // Path traversal
  /\.\.\//g,
  /\.\.\\/g,

  // XSS patterns
  /(<|%3C)(img|iframe|object|embed|link|meta)(\s|%20)/gi,
  /(expression\s*\(|@import|behavior\s*:)/gi,
];

const containsMaliciousContent = (content: string): boolean => {
  return maliciousPatterns.some(pattern => pattern.test(content));
};

// Rate limiting configuration
const rateLimits = {
  chat: { requests: 50, window: 60000 }, // 50 requests per minute
  project: { requests: 20, window: 60000 }, // 20 projects per minute
  apiKey: { requests: 10, window: 60000 }, // 10 API key operations per minute
  auth: { requests: 5, window: 300000 }, // 5 auth attempts per 5 minutes
};

export const checkRateLimit = (
  userId: string,
  operation: keyof typeof rateLimits
): { allowed: boolean; resetTime?: number; remaining?: number } => {
  const key = `${userId}:${operation}`;
  const now = Date.now();
  const config = rateLimits[operation];

  let limit = rateLimitStore.get(key);

  // Reset if window expired
  if (!limit || now > limit.resetTime) {
    limit = { count: 0, resetTime: now + config.window, blocked: false };
    rateLimitStore.set(key, limit);
  }

  // Check if blocked
  if (limit.blocked) {
    return { allowed: false, resetTime: limit.resetTime };
  }

  // Check if limit exceeded
  if (limit.count >= config.requests) {
    limit.blocked = true;
    logSecurityEvent(userId, 'RATE_LIMIT_EXCEEDED', { operation, count: limit.count });
    return { allowed: false, resetTime: limit.resetTime };
  }

  // Increment counter
  limit.count++;

  return {
    allowed: true,
    remaining: config.requests - limit.count,
    resetTime: limit.resetTime
  };
};

// Input sanitization
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
};

// File validation
export const validateFile = (file: File): { valid: boolean; error?: string } => {
  // Check file size (50MB max)
  if (file.size > 50 * 1024 * 1024) {
    return { valid: false, error: 'File too large (max 50MB)' };
  }

  // Check file type
  const allowedTypes = [
    'text/plain',
    'text/javascript',
    'text/typescript',
    'text/css',
    'text/html',
    'text/markdown',
    'application/json',
    'image/png',
    'image/jpeg',
    'image/gif',
    'image/webp',
  ];

  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: 'File type not allowed' };
  }

  // Check file name
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
  const fileName = file.name.toLowerCase();

  if (dangerousExtensions.some(ext => fileName.endsWith(ext))) {
    return { valid: false, error: 'Dangerous file extension' };
  }

  return { valid: true };
};

// Request size validation
export const validateRequestSize = (request: any): boolean => {
  const requestString = JSON.stringify(request);
  const sizeInBytes = new Blob([requestString]).size;

  // 10MB max request size
  return sizeInBytes <= 10 * 1024 * 1024;
};

// Security headers validation
export const validateSecurityHeaders = (headers: Headers): boolean => {
  // Check for required security headers
  const requiredHeaders = ['user-agent', 'accept'];

  for (const header of requiredHeaders) {
    if (!headers.get(header)) {
      return false;
    }
  }

  // Check for suspicious patterns in headers
  const userAgent = headers.get('user-agent') || '';
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
  ];

  // Allow legitimate bots but log them
  if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
    console.log('🤖 Bot detected:', userAgent);
  }

  return true;
};

// Clean up rate limit store
export const cleanupRateLimitStore = (): void => {
  const now = Date.now();

  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime + 300000) { // 5 minutes after reset
      rateLimitStore.delete(key);
    }
  }
};

// Security event logging
const logSecurityEvent = (userId: string, event: string, details: any = {}): void => {
  console.log('🔒 Security Event:', {
    timestamp: new Date().toISOString(),
    userId,
    event,
    details,
  });
};

// Periodic cleanup
setInterval(cleanupRateLimitStore, 300000); // Clean every 5 minutes
