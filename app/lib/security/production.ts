/**
 * Production security configuration and validation
 */

export interface ProductionConfig {
  isProduction: boolean;
  supabaseUrl: string;
  supabaseAnonKey: string;
  encryptionSecret: string;
  logLevel: string;
}

/**
 * Validates production environment configuration
 */
export function validateProductionConfig(): ProductionConfig {
  const isProduction = process.env.NODE_ENV === 'production';
  
  const config: ProductionConfig = {
    isProduction,
    supabaseUrl: process.env.VITE_SUPABASE_URL || '',
    supabaseAnonKey: process.env.VITE_SUPABASE_ANON_KEY || '',
    encryptionSecret: process.env.API_KEY_ENCRYPTION_SECRET || '',
    logLevel: process.env.VITE_LOG_LEVEL || 'info',
  };

  if (isProduction) {
    const errors: string[] = [];

    // Validate Supabase configuration
    if (!config.supabaseUrl || config.supabaseUrl.includes('your_')) {
      errors.push('VITE_SUPABASE_URL must be set to your production Supabase URL');
    }

    if (!config.supabaseAnonKey || config.supabaseAnonKey.includes('your_')) {
      errors.push('VITE_SUPABASE_ANON_KEY must be set to your production Supabase anon key');
    }

    // Validate encryption secret
    if (!config.encryptionSecret || 
        config.encryptionSecret.includes('your_') || 
        config.encryptionSecret.includes('CHANGE_THIS') ||
        config.encryptionSecret.length < 32) {
      errors.push('API_KEY_ENCRYPTION_SECRET must be a strong random secret (32+ characters)');
    }

    // Validate log level for production
    if (config.logLevel === 'debug') {
      console.warn('⚠️  Warning: Debug logging enabled in production. Consider setting VITE_LOG_LEVEL=error');
    }

    if (errors.length > 0) {
      throw new Error(`Production configuration errors:\n${errors.map(e => `- ${e}`).join('\n')}`);
    }

    console.log('✅ Production configuration validated successfully');
  }

  return config;
}

/**
 * Security headers for production
 */
export const PRODUCTION_SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://*.supabase.co https://*.openai.com https://*.anthropic.com https://*.googleapis.com wss://*.supabase.co",
    "frame-src 'self' https://*.webcontainer-api.io",
    "worker-src 'self' blob:",
  ].join('; '),
};

/**
 * Rate limiting configuration for production
 */
export const RATE_LIMITS = {
  // API calls per minute
  api: {
    windowMs: 60 * 1000, // 1 minute
    max: 100, // requests per window
  },
  // Chat messages per minute
  chat: {
    windowMs: 60 * 1000,
    max: 20,
  },
  // File operations per minute
  files: {
    windowMs: 60 * 1000,
    max: 50,
  },
};

/**
 * Generates a secure encryption secret
 */
export function generateEncryptionSecret(): string {
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  
  // Fallback for Node.js
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
}
