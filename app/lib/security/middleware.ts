import { json } from '@remix-run/cloudflare';
import { checkRateLimit, validateRequestSize } from './requestValidation';
import { supabase } from '~/lib/supabase/client';

/**
 * Security Middleware for API Routes
 * Provides authentication, rate limiting, and request validation
 */

export interface SecurityContext {
  userId: string;
  userEmail: string;
  isAuthenticated: boolean;
  rateLimitRemaining: number;
  requestId: string;
}

// Generate unique request ID for tracking
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Security headers for all responses
const getSecurityHeaders = () => ({
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' data:;",
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
});

// Extract user from request
const extractUserFromRequest = async (request: Request): Promise<{ user: any; error?: string }> => {
  try {
    // Get authorization header
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { user: null, error: 'No valid authorization header' };
    }

    const token = authHeader.substring(7);
    
    // Verify JWT token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return { user: null, error: 'Invalid or expired token' };
    }

    return { user };
  } catch (error) {
    return { user: null, error: 'Authentication failed' };
  }
};

// Main security middleware
export const withSecurity = (
  handler: (request: Request, context: SecurityContext) => Promise<Response>,
  options: {
    requireAuth?: boolean;
    rateLimit?: keyof typeof import('./requestValidation').rateLimits;
    validateSize?: boolean;
  } = {}
) => {
  return async (request: Request): Promise<Response> => {
    const requestId = generateRequestId();
    const startTime = Date.now();
    
    try {
      // Log request start
      console.log(`🔒 [${requestId}] ${request.method} ${request.url} - Security check started`);

      // Validate request size
      if (options.validateSize !== false) {
        const contentLength = request.headers.get('content-length');
        if (contentLength && parseInt(contentLength) > 10 * 1024 * 1024) { // 10MB limit
          return json(
            { error: 'Request too large', code: 'REQUEST_TOO_LARGE' },
            { 
              status: 413,
              headers: getSecurityHeaders(),
            }
          );
        }
      }

      // Check for suspicious patterns in URL
      const url = new URL(request.url);
      const suspiciousPatterns = [
        /\.\./,  // Path traversal
        /<script/i,  // XSS attempt
        /union.*select/i,  // SQL injection
        /javascript:/i,  // JavaScript protocol
      ];

      if (suspiciousPatterns.some(pattern => pattern.test(url.pathname + url.search))) {
        console.warn(`🚨 [${requestId}] Suspicious request pattern detected: ${request.url}`);
        return json(
          { error: 'Invalid request', code: 'SUSPICIOUS_PATTERN' },
          { 
            status: 400,
            headers: getSecurityHeaders(),
          }
        );
      }

      // Extract and validate user if auth required
      let user = null;
      if (options.requireAuth) {
        const { user: extractedUser, error } = await extractUserFromRequest(request);
        if (error || !extractedUser) {
          console.warn(`🚨 [${requestId}] Authentication failed: ${error}`);
          return json(
            { error: 'Authentication required', code: 'UNAUTHORIZED' },
            { 
              status: 401,
              headers: getSecurityHeaders(),
            }
          );
        }
        user = extractedUser;
      }

      // Rate limiting
      let rateLimitRemaining = -1;
      if (options.rateLimit && user) {
        const { checkRateLimit } = await import('./requestValidation');
        const rateLimit = checkRateLimit(user.id, options.rateLimit);
        
        if (!rateLimit.allowed) {
          console.warn(`🚨 [${requestId}] Rate limit exceeded for user ${user.id}`);
          return json(
            { 
              error: 'Rate limit exceeded', 
              code: 'RATE_LIMIT_EXCEEDED',
              resetTime: rateLimit.resetTime,
            },
            { 
              status: 429,
              headers: {
                ...getSecurityHeaders(),
                'Retry-After': Math.ceil((rateLimit.resetTime! - Date.now()) / 1000).toString(),
              },
            }
          );
        }
        
        rateLimitRemaining = rateLimit.remaining || -1;
      }

      // Create security context
      const context: SecurityContext = {
        userId: user?.id || '',
        userEmail: user?.email || '',
        isAuthenticated: !!user,
        rateLimitRemaining,
        requestId,
      };

      // Call the actual handler
      const response = await handler(request, context);

      // Add security headers to response
      const headers = new Headers(response.headers);
      Object.entries(getSecurityHeaders()).forEach(([key, value]) => {
        headers.set(key, value);
      });

      // Add rate limit headers if applicable
      if (rateLimitRemaining >= 0) {
        headers.set('X-RateLimit-Remaining', rateLimitRemaining.toString());
      }

      // Log successful request
      const duration = Date.now() - startTime;
      console.log(`✅ [${requestId}] Request completed in ${duration}ms`);

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers,
      });

    } catch (error) {
      console.error(`❌ [${requestId}] Security middleware error:`, error);
      
      return json(
        { error: 'Internal server error', code: 'INTERNAL_ERROR' },
        { 
          status: 500,
          headers: getSecurityHeaders(),
        }
      );
    }
  };
};

// Specific middleware for chat endpoints
export const withChatSecurity = (handler: (request: Request, context: SecurityContext) => Promise<Response>) => {
  return withSecurity(handler, {
    requireAuth: true,
    rateLimit: 'chat',
    validateSize: true,
  });
};

// Specific middleware for project endpoints
export const withProjectSecurity = (handler: (request: Request, context: SecurityContext) => Promise<Response>) => {
  return withSecurity(handler, {
    requireAuth: true,
    rateLimit: 'project',
    validateSize: true,
  });
};

// Specific middleware for API key endpoints
export const withApiKeySecurity = (handler: (request: Request, context: SecurityContext) => Promise<Response>) => {
  return withSecurity(handler, {
    requireAuth: true,
    rateLimit: 'apiKey',
    validateSize: false,
  });
};

// CORS middleware for secure cross-origin requests
export const withCORS = (handler: (request: Request) => Promise<Response>) => {
  return async (request: Request): Promise<Response> => {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': process.env.NODE_ENV === 'production' 
            ? 'https://yourdomain.com' 
            : 'http://localhost:5174',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    const response = await handler(request);
    
    // Add CORS headers to actual response
    const headers = new Headers(response.headers);
    headers.set('Access-Control-Allow-Origin', process.env.NODE_ENV === 'production' 
      ? 'https://yourdomain.com' 
      : 'http://localhost:5174');
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });
  };
};
