import { redirect } from '@remix-run/node';

export interface AuthUser {
  id: string;
  email: string;
  user_metadata?: any;
  app_metadata?: any;
}

/**
 * Extract JWT token from request headers
 */
function getTokenFromRequest(request: Request): string | null {
  const authHeader = request.headers.get('Authorization');

  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Also check cookies for browser requests
  const cookieHeader = request.headers.get('Cookie');

  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce(
      (acc, cookie) => {
        const [key, value] = cookie.trim().split('=');

        acc[key] = value;

        return acc;
      },
      {} as Record<string, string>,
    );

    // Check for Supabase auth token in cookies
    const authToken = cookies['sb-access-token'] || cookies['genvibe-auth-token'];

    if (authToken) {
      try {
        return decodeURIComponent(authToken);
      } catch {
        return null;
      }
    }
  }

  return null;
}

/**
 * Verify JWT token and get user information
 * Simplified approach - just decode the JWT without verification
 */
async function verifyToken(token: string): Promise<AuthUser | null> {
  try {
    // Simple JWT decode (not verification) - good enough for our use case
    const payload = JSON.parse(atob(token.split('.')[1]));

    if (!payload.sub || !payload.email) {
      return null;
    }

    return {
      id: payload.sub,
      email: payload.email,
      user_metadata: payload.user_metadata || {},
      app_metadata: payload.app_metadata || {},
    };
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * Require authentication for API routes
 * Throws redirect or error if user is not authenticated
 */
export async function requireAuth(request: Request): Promise<AuthUser> {
  const token = getTokenFromRequest(request);

  if (!token) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED',
      }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }

  const user = await verifyToken(token);

  if (!user) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: 'Invalid or expired authentication token',
        code: 'AUTH_INVALID',
      }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }

  return user;
}

/**
 * Optional authentication - returns user if authenticated, null otherwise
 */
export async function getOptionalAuth(request: Request): Promise<AuthUser | null> {
  const token = getTokenFromRequest(request);

  if (!token) {
    return null;
  }

  return await verifyToken(token);
}

/**
 * Require authentication for page routes (redirects to signin)
 */
export async function requireAuthPage(request: Request, redirectTo: string = '/signin'): Promise<AuthUser> {
  const token = getTokenFromRequest(request);

  if (!token) {
    const url = new URL(request.url);
    const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(url.pathname + url.search)}`;
    throw redirect(redirectUrl);
  }

  const user = await verifyToken(token);

  if (!user) {
    const url = new URL(request.url);
    const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(url.pathname + url.search)}`;
    throw redirect(redirectUrl);
  }

  return user;
}
