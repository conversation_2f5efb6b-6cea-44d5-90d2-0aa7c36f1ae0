/**
 * Test Script for Email Signin Profile Fix
 * 
 * This script helps verify that email signin properly shows user profile
 * Run this in browser console after attempting email signin
 */

console.log('🧪 Email Signin Profile Fix Test');
console.log('================================');

// Test configuration
const TEST_CONFIG = {
  testSteps: [
    'Sign out if currently signed in',
    'Go to /signin',
    'Enter email and password (not Google)',
    'Click "Sign In"',
    'Verify success toast appears',
    'Verify user profile shows in sidebar',
    'Verify no "Sign In" button in profile section'
  ]
};

// Helper functions
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const checkAuthState = () => {
  // Check if auth state is properly set
  const authStateElement = document.querySelector('[data-auth-state]');
  const profileElement = document.querySelector('[data-user-profile]');
  const signInButton = document.querySelector('a[href="/signin"]');
  
  return {
    hasAuthState: !!authStateElement,
    hasProfile: !!profileElement,
    hasSignInButton: !!signInButton,
    currentUrl: window.location.href
  };
};

const checkSupabaseSession = async () => {
  try {
    // Check if Supabase client is available
    if (typeof window !== 'undefined' && window.supabase) {
      const { data: { session }, error } = await window.supabase.auth.getSession();
      
      return {
        hasSession: !!session,
        sessionValid: !error && !!session?.user,
        userEmail: session?.user?.email,
        userMetadata: session?.user?.user_metadata,
        error: error?.message
      };
    } else {
      return {
        hasSession: false,
        error: 'Supabase client not found'
      };
    }
  } catch (error) {
    return {
      hasSession: false,
      error: error.message
    };
  }
};

const checkUserProfileDisplay = () => {
  // Look for user profile elements
  const profileButton = document.querySelector('button[class*="profile"]');
  const userAvatar = document.querySelector('img[alt*="@"], div[class*="avatar"]');
  const userName = document.querySelector('[class*="user-name"], [class*="display-name"]');
  const signInLink = document.querySelector('a[href="/signin"]');
  
  // Check sidebar for profile section
  const sidebar = document.querySelector('[class*="sidebar"]');
  const profileSection = sidebar?.querySelector('[class*="profile"], [class*="user"]');
  
  return {
    hasProfileButton: !!profileButton,
    hasUserAvatar: !!userAvatar,
    hasUserName: !!userName,
    hasSignInLink: !!signInLink,
    hasSidebar: !!sidebar,
    hasProfileSection: !!profileSection,
    profileSectionContent: profileSection?.textContent || 'Not found'
  };
};

// Main test function
async function testEmailSigninFix() {
  console.log('🔍 Testing Email Signin Profile Display...\n');
  
  // Test 1: Check current auth state
  console.log('1️⃣ Checking current auth state...');
  const authState = checkAuthState();
  console.log('Auth State:', authState);
  
  // Test 2: Check Supabase session
  console.log('\n2️⃣ Checking Supabase session...');
  const sessionState = await checkSupabaseSession();
  console.log('Session State:', sessionState);
  
  // Test 3: Check user profile display
  console.log('\n3️⃣ Checking user profile display...');
  const profileDisplay = checkUserProfileDisplay();
  console.log('Profile Display:', profileDisplay);
  
  // Test 4: Overall assessment
  console.log('\n📊 Overall Assessment:');
  console.log('=====================');
  
  const isSignedIn = sessionState.hasSession && sessionState.sessionValid;
  const profileShowing = profileDisplay.hasProfileSection && !profileDisplay.hasSignInLink;
  
  console.log(`✅ User is signed in: ${isSignedIn ? 'YES' : 'NO'}`);
  console.log(`✅ Profile is showing: ${profileShowing ? 'YES' : 'NO'}`);
  console.log(`✅ Sign in button hidden: ${!profileDisplay.hasSignInLink ? 'YES' : 'NO'}`);
  
  if (isSignedIn && profileShowing) {
    console.log('🎉 SUCCESS: Email signin profile fix is working!');
    console.log(`👤 Signed in as: ${sessionState.userEmail}`);
    console.log(`📝 Full name: ${sessionState.userMetadata?.full_name || 'Not set'}`);
  } else if (isSignedIn && !profileShowing) {
    console.log('⚠️ PARTIAL: User is signed in but profile not showing');
    console.log('💡 Try refreshing the page or check console for errors');
  } else if (!isSignedIn) {
    console.log('❌ NOT SIGNED IN: Please sign in with email/password first');
  } else {
    console.log('❓ UNKNOWN STATE: Check browser console for errors');
  }
  
  return {
    isSignedIn,
    profileShowing,
    sessionState,
    profileDisplay,
    success: isSignedIn && profileShowing
  };
}

// Continuous monitoring function
function monitorAuthState(duration = 30000) {
  console.log(`🔄 Monitoring auth state for ${duration/1000} seconds...`);
  
  let checks = 0;
  const interval = setInterval(async () => {
    checks++;
    console.log(`\n📊 Check #${checks}:`);
    
    const sessionState = await checkSupabaseSession();
    const profileDisplay = checkUserProfileDisplay();
    
    const isSignedIn = sessionState.hasSession && sessionState.sessionValid;
    const profileShowing = profileDisplay.hasProfileSection && !profileDisplay.hasSignInLink;
    
    console.log(`Session: ${isSignedIn ? '✅' : '❌'} | Profile: ${profileShowing ? '✅' : '❌'}`);
    
    if (isSignedIn && profileShowing) {
      console.log('🎉 SUCCESS: Auth state is working correctly!');
      clearInterval(interval);
    }
  }, 2000);
  
  setTimeout(() => {
    clearInterval(interval);
    console.log('⏰ Monitoring stopped');
  }, duration);
}

// Manual test instructions
function showTestInstructions() {
  console.log('\n📋 Manual Test Instructions:');
  console.log('============================');
  console.log('1. Make sure you are signed out');
  console.log('2. Go to /signin');
  console.log('3. Enter your email and password');
  console.log('4. Click "Sign In"');
  console.log('5. Watch for success toast');
  console.log('6. Check if user profile appears in sidebar');
  console.log('7. Verify "Sign In" button is gone');
  console.log('');
  console.log('Expected Result:');
  console.log('- Success toast: "Welcome back to GenVibe! 🎉"');
  console.log('- User profile shows with name/email');
  console.log('- No "Sign In" button in sidebar');
  console.log('- User can access all authenticated features');
}

// Export functions for manual use
window.testEmailSignin = {
  test: testEmailSigninFix,
  monitor: monitorAuthState,
  instructions: showTestInstructions,
  checkSession: checkSupabaseSession,
  checkProfile: checkUserProfileDisplay
};

// Auto-run instructions
console.log('🔧 Email signin testing tools loaded!');
console.log('Run: testEmailSignin.test() to test current state');
console.log('Run: testEmailSignin.monitor() to monitor auth changes');
console.log('Run: testEmailSignin.instructions() for manual testing guide');

// Auto-run if on localhost
if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
  console.log('\n🏠 Development environment detected');
  console.log('💡 Sign in with email/password, then run: testEmailSignin.test()');
} else {
  console.log('\n🌐 Production environment detected');
  console.log('💡 Run testEmailSignin.instructions() for testing guide');
}
