-- =====================================================
-- 🛡️ ATOMIC MESSAGE LIMIT FUNCTIONS FOR SUPABASE
-- =====================================================
-- These functions provide atomic operations for message limit enforcement
-- to prevent race conditions and ensure data consistency

-- =====================================================
-- Function 1: Get User Message Status
-- =====================================================
CREATE OR REPLACE FUNCTION get_user_message_status(
  p_user_id UUID,
  p_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
  can_send BOOLEAN,
  remaining_messages INTEGER,
  daily_limit INTEGER,
  daily_count INTEGER,
  is_pro BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_profile RECORD;
  v_usage RECORD;
  v_daily_count INTEGER := 0;
  v_daily_limit INTEGER := 10;
  v_is_pro BOOLEAN := false;
  v_remaining INTEGER := 0;
  v_can_send BOOLEAN := false;
BEGIN
  -- Get user profile
  SELECT 
    subscription_tier,
    daily_message_limit,
    daily_message_count,
    last_message_reset_date
  INTO v_profile
  FROM user_profiles 
  WHERE id = p_user_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User profile not found for user_id: %', p_user_id;
  END IF;

  -- Check if user is pro
  v_is_pro := (v_profile.subscription_tier = 'pro');
  v_daily_limit := COALESCE(v_profile.daily_message_limit, 10);

  -- If pro user, unlimited messages
  IF v_is_pro THEN
    RETURN QUERY SELECT 
      true::BOOLEAN as can_send,
      -1::INTEGER as remaining_messages,
      -1::INTEGER as daily_limit,
      0::INTEGER as daily_count,
      true::BOOLEAN as is_pro;
    RETURN;
  END IF;

  -- For free users, check if we need to reset daily count
  IF v_profile.last_message_reset_date IS NULL OR v_profile.last_message_reset_date < p_date THEN
    -- Reset daily count
    UPDATE user_profiles 
    SET 
      daily_message_count = 0,
      last_message_reset_date = p_date,
      updated_at = now()
    WHERE id = p_user_id;
    
    v_daily_count := 0;
  ELSE
    v_daily_count := COALESCE(v_profile.daily_message_count, 0);
  END IF;

  -- Calculate remaining messages
  v_remaining := GREATEST(0, v_daily_limit - v_daily_count);
  v_can_send := (v_remaining > 0);

  RETURN QUERY SELECT 
    v_can_send as can_send,
    v_remaining as remaining_messages,
    v_daily_limit as daily_limit,
    v_daily_count as daily_count,
    v_is_pro as is_pro;
END;
$$;

-- =====================================================
-- Function 2: Consume User Message (Atomic)
-- =====================================================
CREATE OR REPLACE FUNCTION consume_user_message(
  p_user_id UUID,
  p_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
  can_send BOOLEAN,
  remaining_messages INTEGER,
  daily_limit INTEGER,
  daily_count INTEGER,
  is_pro BOOLEAN,
  consumed BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_profile RECORD;
  v_daily_count INTEGER := 0;
  v_daily_limit INTEGER := 10;
  v_is_pro BOOLEAN := false;
  v_remaining INTEGER := 0;
  v_can_send BOOLEAN := false;
  v_consumed BOOLEAN := false;
BEGIN
  -- Lock the user profile row to prevent race conditions
  SELECT 
    subscription_tier,
    daily_message_limit,
    daily_message_count,
    last_message_reset_date
  INTO v_profile
  FROM user_profiles 
  WHERE id = p_user_id
  FOR UPDATE;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User profile not found for user_id: %', p_user_id;
  END IF;

  -- Check if user is pro
  v_is_pro := (v_profile.subscription_tier = 'pro');
  v_daily_limit := COALESCE(v_profile.daily_message_limit, 10);

  -- If pro user, allow unlimited messages
  IF v_is_pro THEN
    -- Update total message count
    UPDATE user_profiles 
    SET 
      total_messages_sent = COALESCE(total_messages_sent, 0) + 1,
      updated_at = now()
    WHERE id = p_user_id;

    -- Update usage tracking
    INSERT INTO usage_tracking (user_id, date, messages_sent, subscription_tier)
    VALUES (p_user_id, p_date, 1, 'pro')
    ON CONFLICT (user_id, date) 
    DO UPDATE SET 
      messages_sent = usage_tracking.messages_sent + 1,
      updated_at = now();

    RETURN QUERY SELECT 
      true::BOOLEAN as can_send,
      -1::INTEGER as remaining_messages,
      -1::INTEGER as daily_limit,
      0::INTEGER as daily_count,
      true::BOOLEAN as is_pro,
      true::BOOLEAN as consumed;
    RETURN;
  END IF;

  -- For free users, check if we need to reset daily count
  IF v_profile.last_message_reset_date IS NULL OR v_profile.last_message_reset_date < p_date THEN
    -- Reset daily count
    v_daily_count := 0;
  ELSE
    v_daily_count := COALESCE(v_profile.daily_message_count, 0);
  END IF;

  -- Check if user can send message
  IF v_daily_count >= v_daily_limit THEN
    -- User has reached limit
    v_remaining := 0;
    v_can_send := false;
    v_consumed := false;
  ELSE
    -- User can send message - consume it
    v_daily_count := v_daily_count + 1;
    v_remaining := v_daily_limit - v_daily_count;
    v_can_send := (v_remaining > 0);
    v_consumed := true;

    -- Update user profile atomically
    UPDATE user_profiles 
    SET 
      daily_message_count = v_daily_count,
      total_messages_sent = COALESCE(total_messages_sent, 0) + 1,
      last_message_reset_date = p_date,
      updated_at = now()
    WHERE id = p_user_id;

    -- Update usage tracking
    INSERT INTO usage_tracking (user_id, date, messages_sent, subscription_tier)
    VALUES (p_user_id, p_date, 1, 'free')
    ON CONFLICT (user_id, date) 
    DO UPDATE SET 
      messages_sent = usage_tracking.messages_sent + 1,
      updated_at = now();
  END IF;

  RETURN QUERY SELECT 
    v_can_send as can_send,
    v_remaining as remaining_messages,
    v_daily_limit as daily_limit,
    v_daily_count as daily_count,
    v_is_pro as is_pro,
    v_consumed as consumed;
END;
$$;

-- =====================================================
-- Function 3: Reset Daily Message Count
-- =====================================================
CREATE OR REPLACE FUNCTION reset_daily_message_count(
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE user_profiles 
  SET 
    daily_message_count = 0,
    last_message_reset_date = CURRENT_DATE,
    updated_at = now()
  WHERE id = p_user_id;

  RETURN FOUND;
END;
$$;

-- =====================================================
-- Grant Permissions
-- =====================================================
GRANT EXECUTE ON FUNCTION get_user_message_status TO authenticated;
GRANT EXECUTE ON FUNCTION consume_user_message TO authenticated;
GRANT EXECUTE ON FUNCTION reset_daily_message_count TO authenticated;

-- =====================================================
-- Create Unique Constraint for Usage Tracking
-- =====================================================
-- Ensure one record per user per date
ALTER TABLE usage_tracking 
ADD CONSTRAINT IF NOT EXISTS usage_tracking_user_date_unique 
UNIQUE (user_id, date);

-- =====================================================
-- Create Indexes for Performance
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_tier 
ON user_profiles(subscription_tier);

CREATE INDEX IF NOT EXISTS idx_user_profiles_reset_date 
ON user_profiles(last_message_reset_date);

CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_date 
ON usage_tracking(user_id, date);

-- =====================================================
-- Test the Functions (Remove in production)
-- =====================================================
-- Example usage:
-- SELECT * FROM get_user_message_status('user-uuid-here'::UUID);
-- SELECT * FROM consume_user_message('user-uuid-here'::UUID);
-- SELECT * FROM reset_daily_message_count('user-uuid-here'::UUID);

-- =====================================================
-- Instructions:
-- =====================================================
-- 1. Run this SQL in your Supabase SQL editor
-- 2. These functions provide atomic operations for message limits
-- 3. Use get_user_message_status() to check current status
-- 4. Use consume_user_message() to atomically consume a message
-- 5. Use reset_daily_message_count() to reset daily count
-- 6. All operations are atomic and prevent race conditions
-- =====================================================
