# Fixing Supabase 400 Bad Request Error

## 🚨 Quick Debugging Steps

### 1. **Use the Debug Panel**
A debug panel is now available in the bottom-right corner of your app:
- Look for the red "🔧 Debug" button
- Click it to open the Supabase Debug Panel
- Run diagnostics to identify the issue

### 2. **Console Commands**
Open browser console and run:
```javascript
// Full diagnostics
debugSupabase()

// Test storage systems
testStorage()

// Check authentication
authState.get()

// Test specific table
testSupabaseTable('chat_conversations')
```

## 🔍 Common Causes & Fixes

### **Issue 1: Authentication Problems**
**Symptoms:** User not authenticated, no session
**Fix:**
1. Make sure you're signed in to GenVibe
2. Check if session is valid: `authState.get()`
3. If not authenticated, sign in again

### **Issue 2: RLS Policy Violations**
**Symptoms:** 400 error with "RLS policy violation" or "insufficient privileges"
**Fix:**
```sql
-- Run in Supabase SQL Editor to check policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('chat_conversations', 'chat_messages', 'projects', 'usage_tracking');
```

### **Issue 3: Missing Required Fields**
**Symptoms:** 400 error with "null value in column" or "violates not-null constraint"
**Common missing fields:**
- `user_id` (should be auto-filled from auth)
- `subscription_tier` in usage_tracking
- `project_type` in projects
- `role` in chat_messages

### **Issue 4: Invalid Data Types**
**Symptoms:** 400 error with "invalid input syntax" or "type mismatch"
**Check:**
- UUIDs are valid format
- Dates are ISO strings
- JSON fields are valid JSON
- Text fields don't exceed limits

### **Issue 5: Foreign Key Violations**
**Symptoms:** 400 error with "violates foreign key constraint"
**Fix:**
- Ensure `user_id` exists in `auth.users`
- Ensure `conversation_id` exists before inserting messages
- Check all referenced IDs are valid

## 🛠️ Step-by-Step Debugging

### Step 1: Check Authentication
```javascript
const auth = authState.get();
console.log('Auth status:', {
  isAuthenticated: auth.isAuthenticated,
  user: auth.user,
  session: auth.session
});
```

### Step 2: Test Database Connection
```javascript
// Test basic connection
const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
console.log('Connection test:', { data, error });
```

### Step 3: Test Table Access
```javascript
// Test each table
const tables = ['user_profiles', 'chat_conversations', 'chat_messages', 'projects', 'usage_tracking'];
for (const table of tables) {
  const { data, error } = await supabase.from(table).select('*').limit(1);
  console.log(`${table}:`, error ? 'ERROR' : 'OK', error?.message);
}
```

### Step 4: Test Insert Operation
```javascript
// Test minimal insert
const { data, error } = await supabase
  .from('usage_tracking')
  .insert({
    user_id: authState.get().user?.id,
    date: new Date().toISOString().split('T')[0],
    messages_sent: 1,
    tokens_used: 0,
    api_calls_made: 1,
    projects_created: 0,
    subscription_tier: 'free'
  });
console.log('Insert test:', { data, error });
```

## 🔧 Manual Fixes

### Fix 1: Reset User Profile
If user profile is corrupted:
```javascript
const user = authState.get().user;
if (user) {
  const { data, error } = await supabase
    .from('user_profiles')
    .upsert({
      id: user.id,
      email: user.email,
      subscription_tier: 'free',
      daily_message_count: 0,
      daily_message_limit: 10,
      total_messages_sent: 0,
      total_projects_created: 0
    });
  console.log('Profile reset:', { data, error });
}
```

### Fix 2: Clean Invalid Data
```sql
-- Run in Supabase SQL Editor
-- Remove any invalid records
DELETE FROM chat_messages WHERE user_id IS NULL;
DELETE FROM chat_conversations WHERE user_id IS NULL;
DELETE FROM projects WHERE user_id IS NULL;
DELETE FROM usage_tracking WHERE user_id IS NULL;
```

### Fix 3: Recreate RLS Policies
If policies are broken, run in Supabase SQL Editor:
```sql
-- Drop and recreate policies for chat_conversations
DROP POLICY IF EXISTS "Users can manage their own conversations" ON chat_conversations;
CREATE POLICY "Users can manage their own conversations"
  ON chat_conversations
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);
```

## 📊 Monitoring & Prevention

### Enable Detailed Logging
The new storage services include detailed logging. Check console for:
- `💾 Saving conversation:` - Conversation save attempts
- `💬 Saving messages:` - Message save attempts  
- `❌ Supabase error:` - Detailed error information
- `✅ Messages saved successfully` - Success confirmations

### Environment Variables Check
Ensure these are set in `.env.local`:
```
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### Database Health Check
Run this weekly:
```javascript
// Check for orphaned records
debugSupabase()
```

## 🆘 Emergency Reset

If all else fails, reset your data:
```javascript
// WARNING: This deletes all your data
const user = authState.get().user;
if (user && confirm('Delete all data?')) {
  await supabase.from('chat_messages').delete().eq('user_id', user.id);
  await supabase.from('chat_conversations').delete().eq('user_id', user.id);
  await supabase.from('projects').delete().eq('user_id', user.id);
  await supabase.from('usage_tracking').delete().eq('user_id', user.id);
  console.log('All data deleted');
}
```

## 📞 Getting Help

1. **Check Console Logs**: Look for detailed error messages
2. **Use Debug Panel**: Run diagnostics and tests
3. **Check Supabase Dashboard**: Look at logs and metrics
4. **Test Incrementally**: Start with simple operations

The debug panel and enhanced logging should help identify the exact cause of your 400 error. Most issues are related to authentication or data validation.
