#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to fix Google OAuth "Database error saving new user" issue
 * by creating the automatic user profile creation trigger in Supabase
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration from .env.local
const SUPABASE_URL = 'https://bgyzehghvajcgqdkpkve.supabase.co';
const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJneXplaGdodmFqY2dxZGtwa3ZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNzIyNTksImV4cCI6MjA2Mjg0ODI1OX0.2RN76yDDQK7CFLRrUEANZ_bbYKtmX6AQIEVcOI95AGw';

// SQL to create the automatic user profile creation trigger
const CREATE_TRIGGER_SQL = `
-- =====================================================
-- Automatic User Profile Creation Trigger for ZenVibe
-- =====================================================

-- 1. Create Function to Handle New User Profile Creation
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a new user profile with default values
  INSERT INTO public.user_profiles (
    id,
    email,
    full_name,
    avatar_url,
    username,
    subscription_tier,
    subscription_status,
    daily_message_count,
    daily_message_limit,
    total_messages_sent,
    total_projects_created,
    preferences,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    NEW.raw_user_meta_data->>'avatar_url',
    NEW.raw_user_meta_data->>'username',
    'free',
    'active',
    0,
    10,
    0,
    0,
    '{}',
    NOW(),
    NOW()
  );

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create Trigger on auth.users Table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_user_profile();

-- 3. Ensure RLS Policies Allow Profile Creation
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow the trigger function to insert profiles
CREATE POLICY "System can create user profiles during signup"
  ON user_profiles
  FOR INSERT
  WITH CHECK (true);

-- Ensure users can read their own profiles
DROP POLICY IF EXISTS "Users can read their own profile" ON user_profiles;
CREATE POLICY "Users can read their own profile"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Ensure users can update their own profiles
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- 4. Grant Necessary Permissions
GRANT USAGE ON SCHEMA public TO postgres;
GRANT ALL ON public.user_profiles TO postgres;
`;

async function fixOAuthTrigger() {
  console.log('🔧 Starting Google OAuth fix...');

  try {
    // Create Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    console.log('📡 Connected to Supabase');

    // Execute the SQL to create the trigger
    console.log('🔨 Creating automatic user profile creation trigger...');

    const { data, error } = await supabase.rpc('exec_sql', {
      sql: CREATE_TRIGGER_SQL,
    });

    if (error) {
      console.error('❌ Error creating trigger:', error);

      // Try alternative approach using individual queries
      console.log('🔄 Trying alternative approach...');
      await createTriggerStepByStep(supabase);
    } else {
      console.log('✅ Trigger created successfully!');
      console.log('📊 Result:', data);
    }

    // Test the trigger by checking if it exists
    await testTrigger(supabase);

    console.log('\n🎉 Google OAuth fix completed!');
    console.log('✅ Users can now sign up with Google without database errors');
  } catch (error) {
    console.error('❌ Failed to fix OAuth trigger:', error);
    console.log('\n📋 Manual Steps Required:');
    console.log('1. Go to your Supabase dashboard: https://app.supabase.com/');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the SQL from supabase-auto-profile-creation.sql');
    console.log('4. Run the SQL to create the trigger');
  }
}

async function createTriggerStepByStep(supabase) {
  const steps = [
    {
      name: 'Create function',
      sql: `
        CREATE OR REPLACE FUNCTION create_user_profile()
        RETURNS TRIGGER AS $$
        BEGIN
          INSERT INTO public.user_profiles (
            id, email, full_name, avatar_url, username,
            subscription_tier, subscription_status,
            daily_message_count, daily_message_limit,
            total_messages_sent, total_projects_created,
            preferences, created_at, updated_at
          ) VALUES (
            NEW.id, NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
            NEW.raw_user_meta_data->>'avatar_url',
            NEW.raw_user_meta_data->>'username',
            'free', 'active', 0, 10, 0, 0, '{}', NOW(), NOW()
          );
          RETURN NEW;
        EXCEPTION
          WHEN OTHERS THEN
            RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
      `,
    },
    {
      name: 'Drop existing trigger',
      sql: 'DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;',
    },
    {
      name: 'Create trigger',
      sql: `
        CREATE TRIGGER on_auth_user_created
          AFTER INSERT ON auth.users
          FOR EACH ROW
          EXECUTE FUNCTION create_user_profile();
      `,
    },
  ];

  for (const step of steps) {
    try {
      console.log(`🔨 ${step.name}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: step.sql });
      if (error) {
        console.error(`❌ Failed to ${step.name}:`, error);
      } else {
        console.log(`✅ ${step.name} completed`);
      }
    } catch (error) {
      console.error(`❌ Exception during ${step.name}:`, error);
    }
  }
}

async function testTrigger(supabase) {
  try {
    console.log('🧪 Testing trigger existence...');

    const { data, error } = await supabase
      .from('information_schema.triggers')
      .select('*')
      .eq('trigger_name', 'on_auth_user_created');

    if (error) {
      console.log('⚠️  Could not verify trigger (this is normal with RLS)');
    } else if (data && data.length > 0) {
      console.log('✅ Trigger exists and is active');
    } else {
      console.log('⚠️  Trigger may not be created properly');
    }
  } catch (error) {
    console.log('⚠️  Could not test trigger (this is normal)');
  }
}

// Run the fix
fixOAuthTrigger().catch(console.error);
