# 🔍 GenVibe Data Storage Analysis & Issues

## 📊 Current Storage Architecture

### **Data Storage Locations**

#### 1. **🍪 Cookies**

- **API Keys**: `apiKeys` cookie (JSON format)
- **Model Selection**: `selectedModel`, `selectedProvider`
- **Prompt Cache**: `PROMPT_COOKIE_KEY`
- **GitHub Auth**: `githubToken`, `githubUsername`, `git:github.com`

#### 2. **🗄️ localStorage**

- **Authentication**: `genvibe-auth-token` (Supabase session)
- **User Profile**: `bolt_profile` (username, bio, avatar)
- **Settings**: Various settings keys (autoSelectTemplate, contextOptimization, etc.)
- **Supabase Connection**: `supabase_connection`, `supabaseCredentials`
- **File Locks**: Project-scoped file locking data

#### 3. **💾 IndexedDB (`boltHistory` database)**

- **Chat History**: Complete chat messages and metadata
- **File Snapshots**: WebContainer file system snapshots
- **Local Backup**: Full conversation data for offline access

#### 4. **☁️ Supabase Database**

- **user_api_keys**: Encrypted API keys
- **user_profiles**: User account information
- **chat_conversations**: Chat metadata and summaries
- **chat_messages**: Important messages only (filtered)
- **projects**: Generated project files and metadata
- **usage_tracking**: Daily usage statistics

## 🚨 **CRITICAL ISSUES IDENTIFIED**

### **Issue 1: API Key Storage Conflict** ⚠️

**Problem**: Dual storage system causing inconsistencies

- **Supabase**: Encrypted API keys in `user_api_keys` table
- **Cookies**: Plain text API keys in `apiKeys` cookie
- **Sync Process**: Background sync from Supabase → Cookies

**Conflicts**:

```typescript
// Chat.client.tsx - Loads from cookies
const storedApiKeys = Cookies.get('apiKeys');
setApiKeys(JSON.parse(storedApiKeys));

// But API keys are stored encrypted in Supabase
// Sync happens in background, causing timing issues
```

**Impact**:

- API keys may not be available when chat starts
- Inconsistent state between database and cookies
- Security risk with plain text cookies

### **Issue 2: Chat History Synchronization** ⚠️

**Problem**: Multiple sources of truth for chat data

- **IndexedDB**: Complete chat history (local backup)
- **Supabase**: Filtered important messages only
- **Sidebar**: Loads from Supabase but IndexedDB may have orphaned data

**Conflicts**:

```typescript
// Sidebar loads from Supabase only
const { data: conversations } = await supabase.from('chat_conversations').select('*');

// But chat loading uses hybrid approach
loadChatMessages(mixedId); // Tries both Supabase + IndexedDB
```

**Impact**:

- Deleted chats still appear in sidebar (IndexedDB orphans)
- Message count mismatches
- Duplicate conversations
- Sync conflicts between local and remote

### **Issue 3: Data Integrity Problems** ⚠️

**Problem**: No consistent data validation across storage systems

**Specific Issues**:

1. **ID Mapping**: Local numeric IDs vs Supabase UUIDs
2. **Message Filtering**: Different logic for what gets stored where
3. **Cleanup**: No automatic cleanup of orphaned data
4. **Conflict Resolution**: No strategy for handling sync conflicts

### **Issue 4: Storage Limits & Performance** ⚠️

**Problem**: Inefficient storage usage

- **IndexedDB**: Stores complete file snapshots (HUGE)
- **Supabase**: 500MB limit being approached
- **localStorage**: No size management

## 🔧 **RECOMMENDED FIXES**

### **Priority 1: Unify API Key Storage**

```typescript
// Remove cookie-based API key storage entirely
// Use only Supabase with proper caching

class ApiKeyManager {
  private cache: Map<string, string> = new Map();

  async getApiKey(provider: string): Promise<string | null> {
    // Check cache first
    if (this.cache.has(provider)) {
      return this.cache.get(provider)!;
    }

    // Load from Supabase and cache
    const key = await ApiKeyService.getApiKey(supabase, userId, provider);
    if (key) {
      this.cache.set(provider, key);
    }
    return key;
  }
}
```

### **Priority 2: Single Source of Truth for Chats**

```typescript
// Make Supabase the primary source
// Use IndexedDB only for offline backup

class ChatManager {
  async loadChat(id: string) {
    // Try Supabase first
    const chat = await this.loadFromSupabase(id);
    if (chat) return chat;

    // Fallback to IndexedDB for offline
    return this.loadFromIndexedDB(id);
  }

  async saveChat(chat: Chat) {
    // Save to Supabase first
    await this.saveToSupabase(chat);

    // Backup to IndexedDB
    await this.backupToIndexedDB(chat);
  }
}
```

### **Priority 3: Implement Data Cleanup**

```typescript
// Regular cleanup of orphaned data
class DataCleanup {
  async cleanupOrphanedData() {
    // Remove IndexedDB entries not in Supabase
    await this.syncIndexedDBWithSupabase();

    // Clear old localStorage entries
    await this.cleanupLocalStorage();

    // Remove expired cookies
    await this.cleanupCookies();
  }
}
```

## 📋 **IMMEDIATE ACTION ITEMS**

### **Step 1: Debug Current State**

Run these commands in browser console:

```javascript
// Check all storage systems
debugStorageSync();
verifyDataIntegrity();
showConversationStats();

// Clean up conflicts
syncIndexedDBWithSupabase();
quickCleanupDuplicates();
```

### **Step 2: Fix API Key Issues**

1. Remove `apiKeys` cookie dependency
2. Implement proper API key caching
3. Fix timing issues in chat initialization

### **Step 3: Resolve Chat Sync Issues**

1. Make Supabase primary source for sidebar
2. Fix IndexedDB orphan cleanup
3. Implement proper conflict resolution

### **Step 4: Optimize Storage**

1. Implement storage quotas
2. Add automatic cleanup
3. Optimize message filtering

## 🎯 **STORAGE STRATEGY RECOMMENDATION**

### **Proposed Architecture**:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Supabase      │    │   IndexedDB     │    │  localStorage   │
│  (Primary)      │    │   (Backup)      │    │   (Settings)    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • API Keys      │◄──►│ • Chat Backup   │    │ • User Prefs    │
│ • Chat Data     │    │ • File Snapshots│    │ • UI Settings   │
│ • User Profile  │    │ • Offline Data  │    │ • Auth Tokens   │
│ • Projects      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Data Flow**:

1. **Write**: Supabase → IndexedDB (backup)
2. **Read**: Supabase (online) → IndexedDB (offline)
3. **Sync**: Periodic cleanup and conflict resolution

This architecture would eliminate the current conflicts and provide a clean, scalable storage solution.

## 🔍 **DETAILED ISSUE BREAKDOWN**

### **API Key Storage Issues**

#### **Current Problematic Flow**:

1. User saves API key → Encrypted in Supabase
2. Background sync → Decrypts and stores in cookies
3. Chat loads → Reads from cookies
4. **Problem**: Timing issues, security risks, dual storage

#### **Specific Code Issues**:

```typescript
// app/components/chat/Chat.client.tsx:530-536
useEffect(() => {
  const storedApiKeys = Cookies.get('apiKeys');
  if (storedApiKeys) {
    setApiKeys(JSON.parse(storedApiKeys)); // Plain text in cookies!
  }
}, []);

// app/lib/stores/user.ts:292-300
async syncApiKeysToCookies(userId: string) {
  // Background sync - may not complete before chat needs keys
  await ApiKeyService.syncApiKeysToCookies(supabase, userId);
}
```

#### **Security Concerns**:

- API keys stored as plain text in cookies
- Cookies accessible via JavaScript
- No expiration on API key cookies
- Potential XSS vulnerability

### **Chat History Sync Issues**

#### **Current Problematic Flow**:

1. Chat saved → Both IndexedDB and Supabase
2. User deletes chat → Only Supabase deleted
3. Sidebar loads → Shows Supabase data
4. **Problem**: IndexedDB still has deleted chat data

#### **Specific Code Issues**:

```typescript
// app/components/sidebar/Menu.client.tsx:78-83
const { data: conversations } = await supabase.from('chat_conversations').select('*').eq('user_id', user.id);
// Only loads from Supabase, ignores IndexedDB state

// app/lib/persistence/useChatHistory.ts:74
loadChatMessages(mixedId); // Hybrid approach causes conflicts
```

#### **Data Inconsistency Examples**:

- Sidebar shows 5 chats (Supabase)
- IndexedDB has 8 chats (includes deleted ones)
- Message counts don't match
- Duplicate conversations with different IDs

### **Storage Performance Issues**

#### **IndexedDB Bloat**:

```typescript
// app/lib/persistence/db.ts stores EVERYTHING
setMessages(db, id, messages, urlId, description, timestamp, metadata);
// No size limits, no cleanup, stores full file snapshots
```

#### **Supabase Approaching Limits**:

- 500MB free tier limit
- Storing filtered messages but still growing
- No automatic cleanup of old data
- Project files taking significant space

## 🛠️ **SPECIFIC FIXES NEEDED**

### **Fix 1: Remove Cookie-Based API Keys**

```typescript
// Replace this pattern:
const apiKeys = Cookies.get('apiKeys');

// With direct Supabase access:
const apiKeys = await ApiKeyService.getAllKeys(supabase, userId);
```

### **Fix 2: Unify Chat Data Source**

```typescript
// Replace hybrid loading:
loadChatMessages(mixedId)

// With Supabase-first approach:
async loadChat(id: string) {
  const chat = await loadFromSupabase(id);
  return chat || await loadFromIndexedDBBackup(id);
}
```

### **Fix 3: Implement Proper Cleanup**

```typescript
// Add to user logout:
async completeSignOut() {
  await this.clearSupabaseData();
  await this.clearIndexedDBData();
  await this.clearLocalStorageData();
  await this.clearCookieData();
}
```

## 🚨 **IMMEDIATE DEBUGGING COMMANDS**

Run these in browser console to identify current issues:

```javascript
// 1. Check API key conflicts
console.log('Cookies:', Cookies.get('apiKeys'));
console.log('Auth state:', authState.get());

// 2. Check chat sync issues
debugStorageSync();
showConversationStats();

// 3. Check storage usage
verifyDataIntegrity();

// 4. Clean up conflicts
syncIndexedDBWithSupabase();
quickCleanupDuplicates();

// 5. Complete reset if needed
completeCleanup(); // WARNING: Deletes all data
```

## 📊 **STORAGE USAGE ANALYSIS**

### **Current Estimated Usage**:

- **IndexedDB**: ~50-100MB (full chat history + snapshots)
- **Supabase**: ~200-300MB (filtered data but growing)
- **localStorage**: ~1-5MB (settings, auth tokens)
- **Cookies**: ~10-50KB (API keys, preferences)

### **Optimization Opportunities**:

1. **Remove IndexedDB snapshots** (largest space usage)
2. **Implement message archiving** in Supabase
3. **Add storage quotas** per user
4. **Compress stored data** where possible

This analysis shows that the platform has significant data storage conflicts that need immediate attention to ensure reliability and security.
