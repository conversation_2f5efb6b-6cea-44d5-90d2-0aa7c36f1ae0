#!/usr/bin/env node

/**
 * Production readiness checker for GenVibe
 * Run this script before deploying to production
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

const REQUIRED_ENV_VARS = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY', 'API_KEY_ENCRYPTION_SECRET'];

const SECURITY_CHECKS = [
  'No default/example values in environment variables',
  'Strong encryption secret (32+ characters)',
  'Production log level set',
  'Security headers configured',
  'Rate limiting enabled',
];

function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...');

  const envFile = '.env.local';
  if (!existsSync(envFile)) {
    console.error('❌ .env.local file not found');
    return false;
  }

  const envContent = readFileSync(envFile, 'utf8');
  const envVars = {};

  envContent.split('\n').forEach((line) => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });

  let allValid = true;

  REQUIRED_ENV_VARS.forEach((varName) => {
    const value = envVars[varName] || process.env[varName];

    if (!value) {
      console.error(`❌ Missing required environment variable: ${varName}`);
      allValid = false;
    } else if (value.includes('your_') || value.includes('CHANGE_THIS')) {
      console.error(`❌ ${varName} contains default/example value: ${value}`);
      allValid = false;
    } else if (varName === 'API_KEY_ENCRYPTION_SECRET' && value.length < 32) {
      console.error(`❌ ${varName} is too short (${value.length} chars, need 32+)`);
      allValid = false;
    } else {
      console.log(`✅ ${varName} is properly configured`);
    }
  });

  return allValid;
}

function checkBuildConfiguration() {
  console.log('\n🔍 Checking build configuration...');

  const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));

  // Check if production build script exists
  if (!packageJson.scripts?.build) {
    console.error('❌ No build script found in package.json');
    return false;
  }

  // Check if Docker configuration exists
  if (!existsSync('Dockerfile')) {
    console.error('❌ Dockerfile not found');
    return false;
  }

  console.log('✅ Build configuration looks good');
  return true;
}

function checkSecurityConfiguration() {
  console.log('\n🔍 Checking security configuration...');

  // Check if security files exist
  const securityFiles = ['app/lib/security/production.ts', 'app/lib/security/middleware.ts'];

  let allExist = true;
  securityFiles.forEach((file) => {
    if (existsSync(file)) {
      console.log(`✅ ${file} exists`);
    } else {
      console.error(`❌ ${file} not found`);
      allExist = false;
    }
  });

  return allExist;
}

function generateProductionChecklist() {
  console.log('\n📋 Production Deployment Checklist:');
  console.log('');
  console.log('Environment & Security:');
  console.log('□ Set unique VITE_SUPABASE_URL for production');
  console.log('□ Set unique VITE_SUPABASE_ANON_KEY for production');
  console.log('□ Generate strong API_KEY_ENCRYPTION_SECRET (32+ chars)');
  console.log('□ Set NODE_ENV=production');
  console.log('□ Set VITE_LOG_LEVEL=error');
  console.log('');
  console.log('Monitoring & Analytics:');
  console.log('□ Configure error tracking (Sentry/LogRocket)');
  console.log('□ Set up performance monitoring');
  console.log('□ Configure analytics (optional)');
  console.log('');
  console.log('Infrastructure:');
  console.log('□ Set up CDN for static assets');
  console.log('□ Configure SSL/TLS certificates');
  console.log('□ Set up backup strategy');
  console.log('□ Configure monitoring/alerting');
  console.log('');
  console.log('Testing:');
  console.log('□ Run full test suite');
  console.log('□ Test with production data');
  console.log('□ Load testing');
  console.log('□ Security audit');
}

function main() {
  console.log('🚀 GenVibe Production Readiness Check\n');

  const envCheck = checkEnvironmentVariables();
  const buildCheck = checkBuildConfiguration();
  const securityCheck = checkSecurityConfiguration();

  const allChecksPass = envCheck && buildCheck && securityCheck;

  console.log('\n' + '='.repeat(50));

  if (allChecksPass) {
    console.log('✅ All checks passed! GenVibe is ready for production.');
    console.log('\n💡 Recommendation: Run additional load testing and security audit before going live.');
  } else {
    console.log('❌ Some checks failed. Please fix the issues above before deploying.');
    process.exit(1);
  }

  generateProductionChecklist();
}

main();
