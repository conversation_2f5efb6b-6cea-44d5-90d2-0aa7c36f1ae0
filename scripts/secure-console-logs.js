#!/usr/bin/env node

/**
 * <PERSON>ript to secure console logs by removing/replacing risky ones
 * This script identifies and fixes console logs that could expose sensitive data
 */

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';

// Patterns that indicate sensitive data
const SENSITIVE_PATTERNS = [
  /console\.(log|debug|info)\([^)]*token[^)]*\)/gi,
  /console\.(log|debug|info)\([^)]*key[^)]*\)/gi,
  /console\.(log|debug|info)\([^)]*secret[^)]*\)/gi,
  /console\.(log|debug|info)\([^)]*password[^)]*\)/gi,
  /console\.(log|debug|info)\([^)]*auth[^)]*\)/gi,
  /console\.(log|debug|info)\([^)]*bearer[^)]*\)/gi,
  /console\.(log|debug|info)\([^)]*api[_-]?key[^)]*\)/gi,
];

// Patterns for development-only logs
const DEV_ONLY_PATTERNS = [
  /console\.(log|debug)\(/gi,
];

// Files to exclude from processing
const EXCLUDE_FILES = [
  'node_modules',
  '.git',
  'dist',
  'build',
  '.next',
  'coverage',
  'scripts/secure-console-logs.js',
  'app/utils/secureLogger.ts',
];

// File extensions to process
const INCLUDE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

function getAllFiles(dir, files = []) {
  const items = readdirSync(dir);
  
  for (const item of items) {
    const fullPath = join(dir, item);
    const stat = statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!EXCLUDE_FILES.some(exclude => fullPath.includes(exclude))) {
        getAllFiles(fullPath, files);
      }
    } else if (INCLUDE_EXTENSIONS.includes(extname(fullPath))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function secureConsoleLog(content, filePath) {
  let modified = false;
  let newContent = content;
  
  // Replace sensitive console logs
  for (const pattern of SENSITIVE_PATTERNS) {
    const matches = content.match(pattern);
    if (matches) {
      console.log(`🔒 Found sensitive console log in ${filePath}:`, matches[0]);
      
      // Replace with secure version
      newContent = newContent.replace(pattern, (match) => {
        modified = true;
        if (match.includes('console.log') || match.includes('console.debug')) {
          return `// SECURITY: Removed sensitive console log - ${match.substring(0, 50)}...`;
        } else {
          // Keep info logs but sanitize them
          return match.replace(/(['"`])[^'"`]*\1/g, '"***"');
        }
      });
    }
  }
  
  // Wrap development logs with environment check
  const devLogMatches = newContent.match(/console\.(log|debug)\([^)]*\)/g);
  if (devLogMatches) {
    for (const match of devLogMatches) {
      // Skip if already wrapped or is an error/warn
      if (newContent.includes(`if (process.env.NODE_ENV === 'development') {\n    ${match}`) ||
          match.includes('error') || match.includes('warn')) {
        continue;
      }
      
      // Check if this log might contain sensitive data
      const isSensitive = SENSITIVE_PATTERNS.some(pattern => pattern.test(match));
      
      if (isSensitive) {
        console.log(`🔒 Removing sensitive dev log in ${filePath}:`, match);
        newContent = newContent.replace(match, `// SECURITY: Removed sensitive log`);
        modified = true;
      } else {
        // Wrap non-sensitive logs with dev check
        const indentation = newContent.split(match)[0].split('\n').pop();
        const wrappedLog = `if (process.env.NODE_ENV === 'development') {\n${indentation}  ${match};\n${indentation}}`;
        newContent = newContent.replace(match, wrappedLog);
        modified = true;
      }
    }
  }
  
  return { content: newContent, modified };
}

function addSecureLoggerImport(content, filePath) {
  // Check if file already imports secureLogger
  if (content.includes('secureLogger') || content.includes('app/utils/secureLogger')) {
    return content;
  }
  
  // Check if file has console logs that should use secureLogger
  const hasConsoleLogs = /console\.(log|debug|info|warn|error)/.test(content);
  if (!hasConsoleLogs) {
    return content;
  }
  
  // Add import at the top
  const lines = content.split('\n');
  let importIndex = 0;
  
  // Find the right place to add import (after existing imports)
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('import ') || lines[i].startsWith('const ') && lines[i].includes('require(')) {
      importIndex = i + 1;
    } else if (lines[i].trim() === '' && importIndex > 0) {
      break;
    }
  }
  
  const importStatement = "import { log } from '~/utils/secureLogger';";
  lines.splice(importIndex, 0, importStatement);
  
  return lines.join('\n');
}

function processFile(filePath) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const { content: securedContent, modified } = secureConsoleLog(content, filePath);
    
    if (modified) {
      // Add secure logger import if needed
      const finalContent = addSecureLoggerImport(securedContent, filePath);
      writeFileSync(filePath, finalContent, 'utf8');
      console.log(`✅ Secured console logs in ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔍 Scanning for risky console logs...\n');
  
  const files = getAllFiles('app');
  let processedCount = 0;
  let modifiedCount = 0;
  
  for (const file of files) {
    processedCount++;
    const wasModified = processFile(file);
    if (wasModified) {
      modifiedCount++;
    }
  }
  
  console.log('\n📊 Summary:');
  console.log(`Files processed: ${processedCount}`);
  console.log(`Files modified: ${modifiedCount}`);
  console.log(`Files secured: ${modifiedCount}`);
  
  if (modifiedCount > 0) {
    console.log('\n✅ Console logs have been secured!');
    console.log('🔒 Sensitive logs removed or sanitized');
    console.log('🛡️ Development logs wrapped with environment checks');
    console.log('📝 Consider using the secureLogger utility for new logs');
  } else {
    console.log('\n✅ No risky console logs found!');
  }
}

main();
