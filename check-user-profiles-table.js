#!/usr/bin/env node

/**
 * Simple script to check if user_profiles table exists and is properly configured
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://bgyzehghvajcgqdkpkve.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJneXplaGdodmFqY2dxZGtwa3ZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNzIyNTksImV4cCI6MjA2Mjg0ODI1OX0.2RN76yDDQK7CFLRrUEANZ_bbYKtmX6AQIEVcOI95AGw';

async function checkUserProfilesTable() {
  console.log('🔍 Checking user_profiles table...');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

  try {
    // Try to select from user_profiles table
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);

    if (error) {
      console.log('🚨 ISSUE FOUND with user_profiles table:');
      console.log(`   Error: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      console.log(`   Details: ${error.details}`);
      
      if (error.code === 'PGRST116') {
        console.log('');
        console.log('❌ user_profiles table does NOT exist!');
        console.log('   This is the root cause of the OAuth error.');
        console.log('');
        console.log('🔧 SOLUTION: Create the user_profiles table');
        console.log('   Run this SQL in Supabase SQL Editor:');
        console.log('');
        console.log(getUserProfilesTableSQL());
      } else if (error.code === '42501') {
        console.log('');
        console.log('🔒 RLS Policy issue - table exists but access denied');
        console.log('   Need to check/fix RLS policies');
      }
    } else {
      console.log('✅ user_profiles table exists and is accessible');
      console.log(`   Found ${data ? data.length : 0} records`);
      
      // Try to insert a test record to check permissions
      await testInsertPermissions(supabase);
    }
  } catch (error) {
    console.error('❌ Failed to check user_profiles table:', error);
  }
}

async function testInsertPermissions(supabase) {
  try {
    console.log('🧪 Testing insert permissions...');
    
    // Try to insert a test record (this will fail but shows us the error)
    const { data, error } = await supabase
      .from('user_profiles')
      .insert([{
        id: '00000000-0000-0000-0000-000000000000', // Fake UUID
        email: '<EMAIL>',
        subscription_tier: 'free',
        subscription_status: 'active',
        daily_message_count: 0,
        daily_message_limit: 10,
        total_messages_sent: 0,
        total_projects_created: 0,
        preferences: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select();

    if (error) {
      console.log('🚨 INSERT PERMISSION ISSUE:');
      console.log(`   Error: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      
      if (error.code === '42501') {
        console.log('   ❌ RLS policies are blocking inserts');
        console.log('   This is likely why OAuth signup fails!');
      }
    } else {
      console.log('✅ Insert permissions work (cleaning up test record...)');
      // Clean up the test record
      await supabase
        .from('user_profiles')
        .delete()
        .eq('id', '00000000-0000-0000-0000-000000000000');
    }
  } catch (error) {
    console.log('⚠️  Could not test insert permissions:', error.message);
  }
}

function getUserProfilesTableSQL() {
  return `
-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  username TEXT,
  subscription_tier TEXT NOT NULL DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro')),
  subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired')),
  daily_message_count INTEGER NOT NULL DEFAULT 0,
  daily_message_limit INTEGER NOT NULL DEFAULT 10,
  total_messages_sent INTEGER NOT NULL DEFAULT 0,
  total_projects_created INTEGER NOT NULL DEFAULT 0,
  preferences JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read their own profile" 
  ON public.user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON public.user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Create trigger for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id,
    email,
    full_name,
    avatar_url,
    username,
    subscription_tier,
    subscription_status,
    daily_message_count,
    daily_message_limit,
    total_messages_sent,
    total_projects_created,
    preferences,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    NEW.raw_user_meta_data->>'avatar_url',
    NEW.raw_user_meta_data->>'username',
    'free',
    'active',
    0,
    10,
    0,
    0,
    '{}',
    NOW(),
    NOW()
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail user creation
    RAISE WARNING 'Failed to create user profile: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();
`;
}

// Run the check
checkUserProfilesTable().catch(console.error);
