-- GenVibe Complete Database Schema
-- This schema has been applied to the bkzenvibe project
-- No need to run this again - it's for reference only

-- ✅ ALREADY APPLIED: Clean database structure
-- Removed duplicate tables: profiles, users (kept user_profiles)
-- Recreated user_api_keys with correct structure

-- User API Keys Table (✅ CREATED)
CREATE TABLE IF NOT EXISTS user_api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider_name TEXT NOT NULL,
    encrypted_api_key TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),

    -- Ensure one active key per user per provider
    UNIQUE(user_id, provider_name)
);

-- Enable Row Level Security
ALTER TABLE user_api_keys ENABLE ROW LEVEL SECURITY;

-- Create policies for secure access
CREATE POLICY "Users can view their own API keys"
    ON user_api_keys
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own API keys"
    ON user_api_keys
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own API keys"
    ON user_api_keys
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own API keys"
    ON user_api_keys
    FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_user_api_keys_updated_at
    BEFORE UPDATE ON user_api_keys
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_user_api_keys_user_id ON user_api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_user_api_keys_provider ON user_api_keys(provider_name);
CREATE INDEX IF NOT EXISTS idx_user_api_keys_active ON user_api_keys(is_active) WHERE is_active = true;

-- ✅ EXISTING TABLES (Already in database):
--
-- user_profiles: Main user data table with subscription info, preferences, etc.
--   - Links to auth.users(id)
--   - Has RLS policies enabled
--   - Contains: email, full_name, avatar_url, subscription_tier, daily_message_limit, etc.
--
-- chat_conversations: Chat conversation management
-- chat_messages: Individual chat messages
-- projects: User project management
-- usage_tracking: Analytics and usage tracking
--
-- Note: Removed duplicate tables (profiles, users) and recreated user_api_keys with correct structure
