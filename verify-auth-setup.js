/**
 * GenVibe Authentication Setup Verification
 * 
 * This script verifies that all authentication components are properly configured
 * Run this with: node verify-auth-setup.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 GenVibe Authentication Setup Verification');
console.log('===========================================\n');

const checks = {
  // Check if all required files exist
  checkFiles() {
    console.log('📁 Checking required files...');
    
    const requiredFiles = [
      'app/routes/signup.tsx',
      'app/routes/signin.tsx',
      'app/routes/forgot-password.tsx',
      'app/routes/reset-password.tsx',
      'app/routes/auth.callback.tsx',
      'app/lib/supabase/client.ts',
      'app/lib/stores/user.ts'
    ];
    
    let allFilesExist = true;
    
    requiredFiles.forEach(file => {
      const exists = fs.existsSync(file);
      console.log(`${exists ? '✅' : '❌'} ${file}`);
      if (!exists) allFilesExist = false;
    });
    
    return allFilesExist;
  },

  // Check signup form has full name field
  checkSignupForm() {
    console.log('\n📝 Checking signup form...');
    
    try {
      const signupContent = fs.readFileSync('app/routes/signup.tsx', 'utf8');
      
      const hasFullNameField = signupContent.includes('name="fullName"');
      const hasFullNameValidation = signupContent.includes('fullName.trim().length');
      const hasFullNameInMetadata = signupContent.includes('full_name: fullName');
      
      console.log(`${hasFullNameField ? '✅' : '❌'} Full name field in form`);
      console.log(`${hasFullNameValidation ? '✅' : '❌'} Full name validation`);
      console.log(`${hasFullNameInMetadata ? '✅' : '❌'} Full name in user metadata`);
      
      return hasFullNameField && hasFullNameValidation && hasFullNameInMetadata;
    } catch (error) {
      console.log('❌ Error reading signup form:', error.message);
      return false;
    }
  },

  // Check signin form has forgot password link
  checkSigninForm() {
    console.log('\n🔐 Checking signin form...');
    
    try {
      const signinContent = fs.readFileSync('app/routes/signin.tsx', 'utf8');
      
      const hasForgotPasswordLink = signinContent.includes('href="/forgot-password"');
      const hasPasswordToggle = signinContent.includes('showPassword');
      
      console.log(`${hasForgotPasswordLink ? '✅' : '❌'} Forgot password link`);
      console.log(`${hasPasswordToggle ? '✅' : '❌'} Password visibility toggle`);
      
      return hasForgotPasswordLink && hasPasswordToggle;
    } catch (error) {
      console.log('❌ Error reading signin form:', error.message);
      return false;
    }
  },

  // Check forgot password route
  checkForgotPasswordRoute() {
    console.log('\n🔄 Checking forgot password route...');
    
    try {
      const forgotPasswordContent = fs.readFileSync('app/routes/forgot-password.tsx', 'utf8');
      
      const hasEmailValidation = forgotPasswordContent.includes('emailRegex');
      const usesServerClient = forgotPasswordContent.includes('createSupabaseClient(context');
      const hasResetPasswordCall = forgotPasswordContent.includes('resetPasswordForEmail');
      
      console.log(`${hasEmailValidation ? '✅' : '❌'} Email validation`);
      console.log(`${usesServerClient ? '✅' : '❌'} Server-side Supabase client`);
      console.log(`${hasResetPasswordCall ? '✅' : '❌'} Reset password API call`);
      
      return hasEmailValidation && usesServerClient && hasResetPasswordCall;
    } catch (error) {
      console.log('❌ Error reading forgot password route:', error.message);
      return false;
    }
  },

  // Check reset password route
  checkResetPasswordRoute() {
    console.log('\n🔑 Checking reset password route...');
    
    try {
      const resetPasswordContent = fs.readFileSync('app/routes/reset-password.tsx', 'utf8');
      
      const hasCodeExchange = resetPasswordContent.includes('exchangeCodeForSession');
      const hasPasswordUpdate = resetPasswordContent.includes('updateUser');
      const hasPasswordValidation = resetPasswordContent.includes('password.length < 6');
      const hasPasswordConfirmation = resetPasswordContent.includes('confirmPassword');
      
      console.log(`${hasCodeExchange ? '✅' : '❌'} Code exchange for session`);
      console.log(`${hasPasswordUpdate ? '✅' : '❌'} Password update`);
      console.log(`${hasPasswordValidation ? '✅' : '❌'} Password validation`);
      console.log(`${hasPasswordConfirmation ? '✅' : '❌'} Password confirmation`);
      
      return hasCodeExchange && hasPasswordUpdate && hasPasswordValidation && hasPasswordConfirmation;
    } catch (error) {
      console.log('❌ Error reading reset password route:', error.message);
      return false;
    }
  },

  // Check Supabase client configuration
  checkSupabaseClient() {
    console.log('\n🔌 Checking Supabase client...');
    
    try {
      const clientContent = fs.readFileSync('app/lib/supabase/client.ts', 'utf8');
      
      const hasSignUpWithFullName = clientContent.includes('signUp: async (email: string, password: string, fullName?: string)');
      const hasResetPasswordHelper = clientContent.includes('resetPassword: async');
      const hasUpdatePasswordHelper = clientContent.includes('updatePassword: async');
      const hasEnvironmentCheck = clientContent.includes('VITE_SUPABASE_URL');
      
      console.log(`${hasSignUpWithFullName ? '✅' : '❌'} SignUp with full name support`);
      console.log(`${hasResetPasswordHelper ? '✅' : '❌'} Reset password helper`);
      console.log(`${hasUpdatePasswordHelper ? '✅' : '❌'} Update password helper`);
      console.log(`${hasEnvironmentCheck ? '✅' : '❌'} Environment variable check`);
      
      return hasSignUpWithFullName && hasResetPasswordHelper && hasUpdatePasswordHelper && hasEnvironmentCheck;
    } catch (error) {
      console.log('❌ Error reading Supabase client:', error.message);
      return false;
    }
  },

  // Check environment configuration
  checkEnvironment() {
    console.log('\n🌍 Checking environment configuration...');
    
    const envFiles = ['.env.local', '.env', '.env.example'];
    let hasEnvFile = false;
    let hasSupabaseVars = false;
    
    envFiles.forEach(file => {
      if (fs.existsSync(file)) {
        hasEnvFile = true;
        try {
          const envContent = fs.readFileSync(file, 'utf8');
          if (envContent.includes('VITE_SUPABASE_URL') && envContent.includes('VITE_SUPABASE_ANON_KEY')) {
            hasSupabaseVars = true;
          }
        } catch (error) {
          // Ignore read errors
        }
      }
    });
    
    console.log(`${hasEnvFile ? '✅' : '❌'} Environment file exists`);
    console.log(`${hasSupabaseVars ? '✅' : '❌'} Supabase environment variables`);
    
    return hasEnvFile && hasSupabaseVars;
  },

  // Check user profile structure
  checkUserProfileStructure() {
    console.log('\n👤 Checking user profile structure...');
    
    try {
      const userStoreContent = fs.readFileSync('app/lib/stores/user.ts', 'utf8');
      
      const hasFullNameField = userStoreContent.includes('full_name?:');
      const hasProfileInterface = userStoreContent.includes('interface UserProfile');
      const hasMetadataMapping = userStoreContent.includes('user_metadata?.full_name');
      
      console.log(`${hasFullNameField ? '✅' : '❌'} Full name field in UserProfile interface`);
      console.log(`${hasProfileInterface ? '✅' : '❌'} UserProfile interface exists`);
      console.log(`${hasMetadataMapping ? '✅' : '❌'} User metadata mapping`);
      
      return hasFullNameField && hasProfileInterface && hasMetadataMapping;
    } catch (error) {
      console.log('❌ Error reading user store:', error.message);
      return false;
    }
  }
};

// Run all checks
async function runVerification() {
  const results = {
    files: checks.checkFiles(),
    signupForm: checks.checkSignupForm(),
    signinForm: checks.checkSigninForm(),
    forgotPassword: checks.checkForgotPasswordRoute(),
    resetPassword: checks.checkResetPasswordRoute(),
    supabaseClient: checks.checkSupabaseClient(),
    environment: checks.checkEnvironment(),
    userProfile: checks.checkUserProfileStructure()
  };
  
  console.log('\n📊 Verification Results:');
  console.log('========================');
  
  Object.entries(results).forEach(([check, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${check}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedChecks = Object.values(results).filter(Boolean).length;
  const totalChecks = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedChecks}/${totalChecks} checks passed`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 All authentication components are properly configured!');
    console.log('\n📋 Next Steps:');
    console.log('1. Start your development server');
    console.log('2. Load test-auth-flows.js in browser console');
    console.log('3. Run testAuth.runAllTests() to verify functionality');
    console.log('4. Test manual flows with real email addresses');
  } else {
    console.log('⚠️ Some authentication components need attention.');
    console.log('\n🔧 Recommended Actions:');
    
    if (!results.files) console.log('- Ensure all required files are present');
    if (!results.signupForm) console.log('- Check signup form implementation');
    if (!results.signinForm) console.log('- Check signin form forgot password link');
    if (!results.forgotPassword) console.log('- Review forgot password route');
    if (!results.resetPassword) console.log('- Review reset password route');
    if (!results.supabaseClient) console.log('- Update Supabase client helpers');
    if (!results.environment) console.log('- Configure environment variables');
    if (!results.userProfile) console.log('- Update user profile structure');
  }
  
  return results;
}

// Run verification
runVerification().catch(console.error);
