# 🎯 GenVibe Storage Strategy (500MB Supabase Limit)

## 📊 **Current Storage Analysis**

### **Estimated Data Sizes**:

- **Chat Message**: ~1-5KB (text) to 50KB+ (with code)
- **File Snapshot**: 10-100MB+ per project (HUGE!)
- **Project Files**: 1-10MB per project
- **API Keys**: ~100-200 bytes each (encrypted)
- **User Profile**: ~1KB
- **Usage Tracking**: ~500 bytes per day

### **Current Supabase Usage Breakdown**:

```
📊 Estimated Current Usage (~200-300MB):
├─ chat_messages: ~150-200MB (filtered messages)
├─ projects: ~30-50MB (project files)
├─ chat_conversations: ~5-10MB (metadata)
├─ user_profiles: ~1MB (user data)
├─ user_api_keys: ~1MB (encrypted keys)
└─ usage_tracking: ~1MB (analytics)
```

### **Current IndexedDB Usage (~50-100MB)**:

```
💾 IndexedDB Storage:
├─ Full chat history: ~30-50MB
├─ File snapshots: ~20-50MB (LARGEST!)
├─ Offline backup: ~10-20MB
└─ Metadata: ~1-5MB
```

## 🎯 **OPTIMAL STORAGE STRATEGY**

### **🔥 CRITICAL: What MUST Stay in Supabase**

```typescript
// Essential data that needs cloud sync and security
SUPABASE_CRITICAL = {
  // Authentication & Security (HIGH PRIORITY)
  user_api_keys: 'ENCRYPTED API keys - MUST be secure',
  user_profiles: 'Account data, subscription, limits',

  // Core Business Logic (HIGH PRIORITY)
  chat_conversations: 'Chat metadata, titles, timestamps',
  usage_tracking: 'Message limits, billing data',

  // Lightweight Content (MEDIUM PRIORITY)
  chat_messages: 'FILTERED important messages only',
  projects: 'METADATA only, not full files',
};
```

### **💾 What Should Stay Local (IndexedDB/localStorage)**

```typescript
// Large data that doesn't need cloud sync
LOCAL_STORAGE = {
  // Heavy Data (KEEP LOCAL)
  file_snapshots: 'Complete WebContainer snapshots (10-100MB each)',
  full_chat_history: 'Complete message history with artifacts',
  project_files: 'Generated code files and assets',

  // UI/UX Data (KEEP LOCAL)
  user_preferences: 'Theme, settings, UI state',
  temporary_data: 'Draft messages, unsaved changes',
  offline_backup: 'Emergency recovery data',
};
```

## 📋 **DETAILED STORAGE ALLOCATION**

### **🔒 Supabase (Primary - 500MB Limit)**

#### **Tier 1: Critical Data (~50MB)**

```sql
-- user_api_keys: ~1MB total
-- Encrypted API keys for AI providers
-- MUST be in Supabase for security

-- user_profiles: ~1MB total
-- Account data, subscription tiers, daily limits
-- MUST be in Supabase for billing/limits

-- usage_tracking: ~5MB total
-- Daily usage stats, message counts
-- MUST be in Supabase for enforcement
```

#### **Tier 2: Core Business Data (~100MB)**

```sql
-- chat_conversations: ~10MB total
-- Chat metadata, titles, timestamps, project types
-- MUST be in Supabase for cross-device sync

-- chat_messages: ~80MB total (FILTERED)
-- Only important messages:
--   • All user messages (always important)
--   • Assistant messages with code/artifacts
--   • Messages > 1KB (substantial content)
--   • Error messages and important responses
-- EXCLUDE: Simple acknowledgments, "I'll help you" responses
```

#### **Tier 3: Project Metadata (~50MB)**

```sql
-- projects: ~50MB total (METADATA ONLY)
-- Project info, dependencies, framework detection
-- File structure summary (not full content)
-- EXCLUDE: Actual generated files
```

### **💾 IndexedDB (Local - Unlimited)**

#### **Heavy Data Storage**

```typescript
// file_snapshots: 10-100MB per project
// Complete WebContainer file system snapshots
// Used for instant project restoration

// full_chat_history: 20-50MB total
// Complete chat messages including artifacts
// Used for offline access and full context

// project_files: 5-20MB per project
// Generated code, assets, build files
// Used for local development and backup
```

#### **UI/Performance Data**

```typescript
// user_preferences: ~1MB
// Theme, settings, UI state, file locks

// temporary_data: ~5MB
// Draft messages, unsaved changes, cache

// offline_backup: ~10MB
// Emergency recovery data for critical chats
```

### **🗄️ localStorage (Settings - ~5MB)**

```typescript
// Authentication tokens
// UI preferences and settings
// Temporary cache data
// File locking state
```

## 🔧 **IMPLEMENTATION STRATEGY**

### **Phase 1: Message Filtering (Save ~100MB)**

````typescript
// Implement smart message filtering
const shouldStoreInSupabase = (message: Message): boolean => {
  // Always store user messages
  if (message.role === 'user') return true;

  // Store assistant messages with substantial content
  const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);

  // Store if contains code, artifacts, or substantial content
  return (
    content.includes('```') || // Code blocks
    content.includes('boltArtifact') || // Artifacts
    content.length > 1000 || // Substantial content
    content.includes('error') || // Error messages
    content.includes('install') || // Installation commands
    content.includes('npm') || // Package management
    content.includes('dependency') // Dependency info
  );
};
````

### **Phase 2: Project Optimization (Save ~100MB)**

```typescript
// Store only project metadata in Supabase
const optimizeProjectForSupabase = (project: Project) => ({
  id: project.id,
  name: project.name,
  framework: project.framework,
  dependencies: project.dependencies,
  file_structure: {
    // Only file names and sizes, not content
    files: Object.keys(project.files).map((path) => ({
      path,
      size: project.files[path].content?.length || 0,
      type: project.files[path].type,
    })),
  },
  // Store actual files in IndexedDB
  created_at: project.created_at,
  updated_at: project.updated_at,
});
```

### **Phase 3: Cleanup Strategy (Save ~50MB)**

```typescript
// Automatic cleanup to stay within limits
const CLEANUP_THRESHOLDS = {
  MAX_CONVERSATIONS_PER_USER: 50, // Keep last 50 chats
  MAX_MESSAGES_PER_CONVERSATION: 100, // Keep last 100 messages
  MAX_PROJECTS_PER_USER: 20, // Keep last 20 projects
  ARCHIVE_AFTER_DAYS: 30, // Archive old data
};
```

## 📊 **PROJECTED STORAGE USAGE**

### **After Optimization**:

```
🎯 Optimized Supabase Usage (~200MB):
├─ chat_messages: ~80MB (filtered, 60% reduction)
├─ projects: ~20MB (metadata only, 60% reduction)
├─ chat_conversations: ~10MB (unchanged)
├─ user_profiles: ~1MB (unchanged)
├─ user_api_keys: ~1MB (unchanged)
└─ usage_tracking: ~5MB (unchanged)

💾 IndexedDB Usage (~200MB):
├─ file_snapshots: ~100MB (moved from Supabase)
├─ full_chat_history: ~50MB (complete backup)
├─ project_files: ~40MB (moved from Supabase)
└─ cache_data: ~10MB (temporary)
```

### **Benefits**:

- **60% Supabase reduction**: From ~300MB to ~120MB
- **200MB headroom**: For future growth
- **Better performance**: Large data stays local
- **Offline capability**: Full functionality without internet
- **Cost efficiency**: Stay within free tier longer

## 🚀 **IMPLEMENTATION PRIORITY**

### **Immediate (Week 1)**:

1. ✅ Implement message filtering
2. ✅ Move file snapshots to IndexedDB only
3. ✅ Optimize project storage

### **Short-term (Week 2)**:

1. ✅ Implement automatic cleanup
2. ✅ Add storage monitoring
3. ✅ Test data migration

### **Long-term (Month 1)**:

1. ✅ Add data archiving
2. ✅ Implement compression
3. ✅ Add storage analytics

This strategy ensures we stay well within the 500MB Supabase limit while maintaining full functionality and performance.

## 🎯 **FINAL STORAGE DECISION MATRIX**

### **✅ SUPABASE (Cloud Storage - 500MB Limit)**

| Data Type              | Size  | Priority    | Reason                             |
| ---------------------- | ----- | ----------- | ---------------------------------- |
| **user_api_keys**      | ~1MB  | 🔥 CRITICAL | Security, encryption, cross-device |
| **user_profiles**      | ~1MB  | 🔥 CRITICAL | Billing, limits, account data      |
| **usage_tracking**     | ~5MB  | 🔥 CRITICAL | Message limits, enforcement        |
| **chat_conversations** | ~10MB | 🟡 HIGH     | Cross-device sync, metadata        |
| **chat_messages**      | ~80MB | 🟡 HIGH     | Important messages only (filtered) |
| **projects**           | ~20MB | 🟢 MEDIUM   | Metadata only, no files            |

**Total Supabase: ~120MB (76% reduction from current)**

### **💾 INDEXEDDB (Local Storage - Unlimited)**

| Data Type             | Size   | Priority | Reason                         |
| --------------------- | ------ | -------- | ------------------------------ |
| **file_snapshots**    | ~100MB | 🟢 LOCAL | Huge, instant restore, offline |
| **full_chat_history** | ~50MB  | 🟢 LOCAL | Complete backup, artifacts     |
| **project_files**     | ~40MB  | 🟢 LOCAL | Generated code, assets         |
| **offline_backup**    | ~10MB  | 🟢 LOCAL | Emergency recovery             |

**Total IndexedDB: ~200MB**

### **🗄️ LOCALSTORAGE (Settings - ~5MB)**

| Data Type            | Size  | Priority  | Reason               |
| -------------------- | ----- | --------- | -------------------- |
| **auth_tokens**      | ~10KB | 🟡 HIGH   | Session management   |
| **user_preferences** | ~1MB  | 🟢 MEDIUM | UI settings, theme   |
| **file_locks**       | ~1MB  | 🟢 MEDIUM | Project-scoped locks |
| **temp_cache**       | ~3MB  | 🟢 LOW    | Temporary data       |

## 🚀 **IMPLEMENTATION ROADMAP**

### **Step 1: Remove Cookie Dependencies (Priority 1)**

```typescript
// REMOVE: API keys from cookies (security risk)
// REMOVE: Model selection from cookies
// KEEP: Only essential session data

// Before (INSECURE):
const apiKeys = Cookies.get('apiKeys'); // Plain text!

// After (SECURE):
const apiKeys = await ApiKeyService.getDecryptedKeys(userId);
```

### **Step 2: Implement Smart Message Filtering (Priority 1)**

````typescript
// Save ~100MB by filtering messages
const IMPORTANT_MESSAGE_CRITERIA = {
  user_messages: true, // Always important
  code_blocks: true, // Contains ```
  artifacts: true, // Contains boltArtifact
  substantial_content: true, // > 1KB
  error_messages: true, // Contains error/warning
  commands: true, // npm, install, etc.
  exclude_simple: true, // "I'll help", "Sure!", etc.
};
````

### **Step 3: Move Heavy Data to IndexedDB (Priority 1)**

```typescript
// Move file snapshots from Supabase to IndexedDB
// Save ~100MB in Supabase
const HEAVY_DATA_LOCAL = {
  file_snapshots: 'IndexedDB', // 10-100MB each
  project_files: 'IndexedDB', // Generated code
  full_artifacts: 'IndexedDB', // Complete responses
  offline_backup: 'IndexedDB', // Emergency data
};
```

### **Step 4: Optimize Project Storage (Priority 2)**

```typescript
// Store only metadata in Supabase
const PROJECT_SUPABASE = {
  metadata: true, // Name, framework, dependencies
  file_structure: true, // File names and sizes only
  timestamps: true, // Created, updated dates

  // EXCLUDE from Supabase:
  file_content: false, // Move to IndexedDB
  snapshots: false, // Move to IndexedDB
  artifacts: false, // Move to IndexedDB
};
```

## 📊 **STORAGE MONITORING & LIMITS**

### **Automatic Cleanup Triggers**:

```typescript
const STORAGE_LIMITS = {
  // Supabase limits (enforce strictly)
  MAX_SUPABASE_USAGE: 400, // MB (80% of 500MB limit)
  MAX_CONVERSATIONS: 50, // Per user
  MAX_MESSAGES_PER_CHAT: 100,

  // IndexedDB limits (soft limits)
  MAX_INDEXEDDB_USAGE: 1000, // MB (1GB soft limit)
  MAX_SNAPSHOTS: 20, // Per user

  // Cleanup thresholds
  ARCHIVE_AFTER_DAYS: 30,
  DELETE_AFTER_DAYS: 90,
};
```

### **Storage Health Monitoring**:

```typescript
// Add to debug panel
const STORAGE_HEALTH = {
  supabase_usage: '120MB / 500MB (24%)',
  indexeddb_usage: '200MB / 1GB (20%)',
  localStorage_usage: '5MB / 10MB (50%)',

  status: 'HEALTHY', // GREEN, YELLOW, RED
  next_cleanup: '7 days',
  recommendations: ['Archive conversations older than 30 days', 'Compress file snapshots', 'Clean up orphaned data'],
};
```

## ✅ **IMMEDIATE ACTION PLAN**

### **Today (Critical)**:

1. **Remove API key cookies** - Security vulnerability
2. **Implement message filtering** - Save 100MB immediately
3. **Move file snapshots to IndexedDB** - Save 50MB

### **This Week (High Priority)**:

1. **Optimize project storage** - Metadata only in Supabase
2. **Add storage monitoring** - Prevent future overages
3. **Implement cleanup automation** - Remove old data

### **Next Week (Medium Priority)**:

1. **Add data compression** - Further reduce storage
2. **Implement archiving** - Long-term data management
3. **Add storage analytics** - Usage insights

This strategy will reduce Supabase usage from ~300MB to ~120MB (60% reduction) while improving performance and maintaining all functionality.
